<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_directory_selection" modulePackage="com.minimalmusicplayer" filePath="app\src\main\res\layout\activity_directory_selection.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/activity_directory_selection_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="88" endOffset="14"/></Target><Target id="@+id/toolbar" view="androidx.appcompat.widget.Toolbar"><Expressions/><location startLine="9" startOffset="4" endLine="15" endOffset="62"/></Target><Target id="@+id/recyclerViewDirectories" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="29" startOffset="12" endLine="35" endOffset="57"/></Target><Target id="@+id/textViewEmptyState" view="TextView"><Expressions/><location startLine="38" startOffset="12" endLine="47" endOffset="43"/></Target><Target id="@+id/progressBar" view="ProgressBar"><Expressions/><location startLine="50" startOffset="12" endLine="55" endOffset="43"/></Target><Target id="@+id/buttonScanAll" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="66" startOffset="12" endLine="72" endOffset="43"/></Target><Target id="@+id/fabAddDirectory" view="com.google.android.material.floatingactionbutton.FloatingActionButton"><Expressions/><location startLine="78" startOffset="4" endLine="86" endOffset="41"/></Target></Targets></Layout>