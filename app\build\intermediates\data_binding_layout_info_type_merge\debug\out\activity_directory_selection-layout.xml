<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_directory_selection" modulePackage="com.minimalmusicplayer" filePath="app\src\main\res\layout\activity_directory_selection.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.coordinatorlayout.widget.CoordinatorLayout"><Targets><Target tag="layout/activity_directory_selection_0" view="androidx.coordinatorlayout.widget.CoordinatorLayout"><Expressions/><location startLine="1" startOffset="0" endLine="93" endOffset="53"/></Target><Target id="@+id/toolbar" view="androidx.appcompat.widget.Toolbar"><Expressions/><location startLine="13" startOffset="8" endLine="18" endOffset="66"/></Target><Target id="@+id/recyclerViewDirectories" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="34" startOffset="12" endLine="40" endOffset="57"/></Target><Target id="@+id/textViewEmptyState" view="TextView"><Expressions/><location startLine="43" startOffset="12" endLine="52" endOffset="43"/></Target><Target id="@+id/progressBar" view="ProgressBar"><Expressions/><location startLine="55" startOffset="12" endLine="60" endOffset="43"/></Target><Target id="@+id/buttonScanAll" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="71" startOffset="12" endLine="77" endOffset="43"/></Target><Target id="@+id/fabAddDirectory" view="com.google.android.material.floatingactionbutton.FloatingActionButton"><Expressions/><location startLine="83" startOffset="4" endLine="91" endOffset="41"/></Target></Targets></Layout>