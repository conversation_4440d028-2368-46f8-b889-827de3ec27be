1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.minimalmusicplayer"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
9        android:targetSdkVersion="34" />
10
11    <!-- Permissions for file access and media playback -->
12    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
12-->C:\Users\<USER>\Desktop\musicC\app\src\main\AndroidManifest.xml:6:5-80
12-->C:\Users\<USER>\Desktop\musicC\app\src\main\AndroidManifest.xml:6:22-77
13    <uses-permission android:name="android.permission.READ_MEDIA_AUDIO" />
13-->C:\Users\<USER>\Desktop\musicC\app\src\main\AndroidManifest.xml:7:5-75
13-->C:\Users\<USER>\Desktop\musicC\app\src\main\AndroidManifest.xml:7:22-72
14    <uses-permission android:name="android.permission.WAKE_LOCK" />
14-->C:\Users\<USER>\Desktop\musicC\app\src\main\AndroidManifest.xml:8:5-68
14-->C:\Users\<USER>\Desktop\musicC\app\src\main\AndroidManifest.xml:8:22-65
15    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
15-->C:\Users\<USER>\Desktop\musicC\app\src\main\AndroidManifest.xml:9:5-77
15-->C:\Users\<USER>\Desktop\musicC\app\src\main\AndroidManifest.xml:9:22-74
16    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_MEDIA_PLAYBACK" />
16-->C:\Users\<USER>\Desktop\musicC\app\src\main\AndroidManifest.xml:10:5-92
16-->C:\Users\<USER>\Desktop\musicC\app\src\main\AndroidManifest.xml:10:22-89
17
18    <!-- Widget permissions -->
19    <uses-permission android:name="android.permission.BIND_APPWIDGET" />
19-->C:\Users\<USER>\Desktop\musicC\app\src\main\AndroidManifest.xml:13:5-73
19-->C:\Users\<USER>\Desktop\musicC\app\src\main\AndroidManifest.xml:13:22-70
20
21    <!-- Media session permissions -->
22    <uses-permission android:name="android.permission.MEDIA_CONTENT_CONTROL" />
22-->C:\Users\<USER>\Desktop\musicC\app\src\main\AndroidManifest.xml:16:5-80
22-->C:\Users\<USER>\Desktop\musicC\app\src\main\AndroidManifest.xml:16:22-77
23    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
23-->[com.google.android.exoplayer:exoplayer-core:2.19.1] E:\android_development_cache\.gradle\caches\transforms-3\f6a56acf230789289efb8d310bea470b\transformed\exoplayer-core-2.19.1\AndroidManifest.xml:24:5-79
23-->[com.google.android.exoplayer:exoplayer-core:2.19.1] E:\android_development_cache\.gradle\caches\transforms-3\f6a56acf230789289efb8d310bea470b\transformed\exoplayer-core-2.19.1\AndroidManifest.xml:24:22-76
24
25    <permission
25-->[androidx.core:core:1.10.1] E:\android_development_cache\.gradle\caches\transforms-3\d001c43b87f49a29f46bbdd20bd4ce3a\transformed\core-1.10.1\AndroidManifest.xml:22:5-24:47
26        android:name="com.minimalmusicplayer.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
26-->[androidx.core:core:1.10.1] E:\android_development_cache\.gradle\caches\transforms-3\d001c43b87f49a29f46bbdd20bd4ce3a\transformed\core-1.10.1\AndroidManifest.xml:23:9-81
27        android:protectionLevel="signature" />
27-->[androidx.core:core:1.10.1] E:\android_development_cache\.gradle\caches\transforms-3\d001c43b87f49a29f46bbdd20bd4ce3a\transformed\core-1.10.1\AndroidManifest.xml:24:9-44
28
29    <uses-permission android:name="com.minimalmusicplayer.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
29-->[androidx.core:core:1.10.1] E:\android_development_cache\.gradle\caches\transforms-3\d001c43b87f49a29f46bbdd20bd4ce3a\transformed\core-1.10.1\AndroidManifest.xml:26:5-97
29-->[androidx.core:core:1.10.1] E:\android_development_cache\.gradle\caches\transforms-3\d001c43b87f49a29f46bbdd20bd4ce3a\transformed\core-1.10.1\AndroidManifest.xml:26:22-94
30
31    <application
31-->C:\Users\<USER>\Desktop\musicC\app\src\main\AndroidManifest.xml:18:5-74:19
32        android:allowBackup="true"
32-->C:\Users\<USER>\Desktop\musicC\app\src\main\AndroidManifest.xml:19:9-35
33        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
33-->[androidx.core:core:1.10.1] E:\android_development_cache\.gradle\caches\transforms-3\d001c43b87f49a29f46bbdd20bd4ce3a\transformed\core-1.10.1\AndroidManifest.xml:28:18-86
34        android:dataExtractionRules="@xml/data_extraction_rules"
34-->C:\Users\<USER>\Desktop\musicC\app\src\main\AndroidManifest.xml:20:9-65
35        android:debuggable="true"
36        android:extractNativeLibs="true"
37        android:fullBackupContent="@xml/backup_rules"
37-->C:\Users\<USER>\Desktop\musicC\app\src\main\AndroidManifest.xml:21:9-54
38        android:icon="@mipmap/ic_launcher"
38-->C:\Users\<USER>\Desktop\musicC\app\src\main\AndroidManifest.xml:22:9-43
39        android:label="@string/app_name"
39-->C:\Users\<USER>\Desktop\musicC\app\src\main\AndroidManifest.xml:23:9-41
40        android:roundIcon="@mipmap/ic_launcher_round"
40-->C:\Users\<USER>\Desktop\musicC\app\src\main\AndroidManifest.xml:24:9-54
41        android:supportsRtl="true"
41-->C:\Users\<USER>\Desktop\musicC\app\src\main\AndroidManifest.xml:25:9-35
42        android:theme="@style/Theme.MinimalMusicPlayer" >
42-->C:\Users\<USER>\Desktop\musicC\app\src\main\AndroidManifest.xml:26:9-56
43
44        <!-- Main Activity -->
45        <activity
45-->C:\Users\<USER>\Desktop\musicC\app\src\main\AndroidManifest.xml:30:9-38:20
46            android:name="com.minimalmusicplayer.MainActivity"
46-->C:\Users\<USER>\Desktop\musicC\app\src\main\AndroidManifest.xml:31:13-41
47            android:exported="true"
47-->C:\Users\<USER>\Desktop\musicC\app\src\main\AndroidManifest.xml:32:13-36
48            android:theme="@style/Theme.MinimalMusicPlayer" >
48-->C:\Users\<USER>\Desktop\musicC\app\src\main\AndroidManifest.xml:33:13-60
49            <intent-filter>
49-->C:\Users\<USER>\Desktop\musicC\app\src\main\AndroidManifest.xml:34:13-37:29
50                <action android:name="android.intent.action.MAIN" />
50-->C:\Users\<USER>\Desktop\musicC\app\src\main\AndroidManifest.xml:35:17-69
50-->C:\Users\<USER>\Desktop\musicC\app\src\main\AndroidManifest.xml:35:25-66
51
52                <category android:name="android.intent.category.LAUNCHER" />
52-->C:\Users\<USER>\Desktop\musicC\app\src\main\AndroidManifest.xml:36:17-77
52-->C:\Users\<USER>\Desktop\musicC\app\src\main\AndroidManifest.xml:36:27-74
53            </intent-filter>
54        </activity>
55
56        <!-- Directory Selection Activity -->
57        <activity
57-->C:\Users\<USER>\Desktop\musicC\app\src\main\AndroidManifest.xml:41:9-44:63
58            android:name="com.minimalmusicplayer.DirectorySelectionActivity"
58-->C:\Users\<USER>\Desktop\musicC\app\src\main\AndroidManifest.xml:42:13-55
59            android:exported="false"
59-->C:\Users\<USER>\Desktop\musicC\app\src\main\AndroidManifest.xml:43:13-37
60            android:theme="@style/Theme.MinimalMusicPlayer" />
60-->C:\Users\<USER>\Desktop\musicC\app\src\main\AndroidManifest.xml:44:13-60
61
62        <!-- Music Service -->
63        <service
63-->C:\Users\<USER>\Desktop\musicC\app\src\main\AndroidManifest.xml:47:9-51:61
64            android:name="com.minimalmusicplayer.service.MusicService"
64-->C:\Users\<USER>\Desktop\musicC\app\src\main\AndroidManifest.xml:48:13-49
65            android:enabled="true"
65-->C:\Users\<USER>\Desktop\musicC\app\src\main\AndroidManifest.xml:49:13-35
66            android:exported="false"
66-->C:\Users\<USER>\Desktop\musicC\app\src\main\AndroidManifest.xml:50:13-37
67            android:foregroundServiceType="mediaPlayback" />
67-->C:\Users\<USER>\Desktop\musicC\app\src\main\AndroidManifest.xml:51:13-58
68
69        <!-- Widget Provider -->
70        <receiver
70-->C:\Users\<USER>\Desktop\musicC\app\src\main\AndroidManifest.xml:54:9-63:20
71            android:name="com.minimalmusicplayer.widget.MusicWidgetProvider"
71-->C:\Users\<USER>\Desktop\musicC\app\src\main\AndroidManifest.xml:55:13-55
72            android:exported="true" >
72-->C:\Users\<USER>\Desktop\musicC\app\src\main\AndroidManifest.xml:56:13-36
73            <intent-filter>
73-->C:\Users\<USER>\Desktop\musicC\app\src\main\AndroidManifest.xml:57:13-59:29
74                <action android:name="android.appwidget.action.APPWIDGET_UPDATE" />
74-->C:\Users\<USER>\Desktop\musicC\app\src\main\AndroidManifest.xml:58:17-84
74-->C:\Users\<USER>\Desktop\musicC\app\src\main\AndroidManifest.xml:58:25-81
75            </intent-filter>
76
77            <meta-data
77-->C:\Users\<USER>\Desktop\musicC\app\src\main\AndroidManifest.xml:60:13-62:61
78                android:name="android.appwidget.provider"
78-->C:\Users\<USER>\Desktop\musicC\app\src\main\AndroidManifest.xml:61:17-58
79                android:resource="@xml/music_widget_info" />
79-->C:\Users\<USER>\Desktop\musicC\app\src\main\AndroidManifest.xml:62:17-58
80        </receiver>
81
82        <!-- Media Button Receiver -->
83        <receiver
83-->C:\Users\<USER>\Desktop\musicC\app\src\main\AndroidManifest.xml:66:9-72:20
84            android:name="androidx.media.session.MediaButtonReceiver"
84-->C:\Users\<USER>\Desktop\musicC\app\src\main\AndroidManifest.xml:67:13-70
85            android:exported="true" >
85-->C:\Users\<USER>\Desktop\musicC\app\src\main\AndroidManifest.xml:68:13-36
86            <intent-filter>
86-->C:\Users\<USER>\Desktop\musicC\app\src\main\AndroidManifest.xml:69:13-71:29
87                <action android:name="android.intent.action.MEDIA_BUTTON" />
87-->C:\Users\<USER>\Desktop\musicC\app\src\main\AndroidManifest.xml:70:17-77
87-->C:\Users\<USER>\Desktop\musicC\app\src\main\AndroidManifest.xml:70:25-74
88            </intent-filter>
89        </receiver>
90
91        <provider
91-->[androidx.emoji2:emoji2:1.2.0] E:\android_development_cache\.gradle\caches\transforms-3\1e3aff5aaafb1f3205851880ac1fd91b\transformed\emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
92            android:name="androidx.startup.InitializationProvider"
92-->[androidx.emoji2:emoji2:1.2.0] E:\android_development_cache\.gradle\caches\transforms-3\1e3aff5aaafb1f3205851880ac1fd91b\transformed\emoji2-1.2.0\AndroidManifest.xml:25:13-67
93            android:authorities="com.minimalmusicplayer.androidx-startup"
93-->[androidx.emoji2:emoji2:1.2.0] E:\android_development_cache\.gradle\caches\transforms-3\1e3aff5aaafb1f3205851880ac1fd91b\transformed\emoji2-1.2.0\AndroidManifest.xml:26:13-68
94            android:exported="false" >
94-->[androidx.emoji2:emoji2:1.2.0] E:\android_development_cache\.gradle\caches\transforms-3\1e3aff5aaafb1f3205851880ac1fd91b\transformed\emoji2-1.2.0\AndroidManifest.xml:27:13-37
95            <meta-data
95-->[androidx.emoji2:emoji2:1.2.0] E:\android_development_cache\.gradle\caches\transforms-3\1e3aff5aaafb1f3205851880ac1fd91b\transformed\emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
96                android:name="androidx.emoji2.text.EmojiCompatInitializer"
96-->[androidx.emoji2:emoji2:1.2.0] E:\android_development_cache\.gradle\caches\transforms-3\1e3aff5aaafb1f3205851880ac1fd91b\transformed\emoji2-1.2.0\AndroidManifest.xml:30:17-75
97                android:value="androidx.startup" />
97-->[androidx.emoji2:emoji2:1.2.0] E:\android_development_cache\.gradle\caches\transforms-3\1e3aff5aaafb1f3205851880ac1fd91b\transformed\emoji2-1.2.0\AndroidManifest.xml:31:17-49
98            <meta-data
98-->[androidx.lifecycle:lifecycle-process:2.6.1] E:\android_development_cache\.gradle\caches\transforms-3\d4bf48403fc213aec52ba971f9bad5bd\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:29:13-31:52
99                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
99-->[androidx.lifecycle:lifecycle-process:2.6.1] E:\android_development_cache\.gradle\caches\transforms-3\d4bf48403fc213aec52ba971f9bad5bd\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:30:17-78
100                android:value="androidx.startup" />
100-->[androidx.lifecycle:lifecycle-process:2.6.1] E:\android_development_cache\.gradle\caches\transforms-3\d4bf48403fc213aec52ba971f9bad5bd\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:31:17-49
101            <meta-data
101-->[androidx.profileinstaller:profileinstaller:1.3.0] E:\android_development_cache\.gradle\caches\transforms-3\0a451f34059334e77a169e82d985bb5f\transformed\profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
102                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
102-->[androidx.profileinstaller:profileinstaller:1.3.0] E:\android_development_cache\.gradle\caches\transforms-3\0a451f34059334e77a169e82d985bb5f\transformed\profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
103                android:value="androidx.startup" />
103-->[androidx.profileinstaller:profileinstaller:1.3.0] E:\android_development_cache\.gradle\caches\transforms-3\0a451f34059334e77a169e82d985bb5f\transformed\profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
104        </provider>
105
106        <uses-library
106-->[androidx.window:window:1.0.0] E:\android_development_cache\.gradle\caches\transforms-3\2d3a06f14c6f275b0094d4668de90ecb\transformed\window-1.0.0\AndroidManifest.xml:25:9-27:40
107            android:name="androidx.window.extensions"
107-->[androidx.window:window:1.0.0] E:\android_development_cache\.gradle\caches\transforms-3\2d3a06f14c6f275b0094d4668de90ecb\transformed\window-1.0.0\AndroidManifest.xml:26:13-54
108            android:required="false" />
108-->[androidx.window:window:1.0.0] E:\android_development_cache\.gradle\caches\transforms-3\2d3a06f14c6f275b0094d4668de90ecb\transformed\window-1.0.0\AndroidManifest.xml:27:13-37
109        <uses-library
109-->[androidx.window:window:1.0.0] E:\android_development_cache\.gradle\caches\transforms-3\2d3a06f14c6f275b0094d4668de90ecb\transformed\window-1.0.0\AndroidManifest.xml:28:9-30:40
110            android:name="androidx.window.sidecar"
110-->[androidx.window:window:1.0.0] E:\android_development_cache\.gradle\caches\transforms-3\2d3a06f14c6f275b0094d4668de90ecb\transformed\window-1.0.0\AndroidManifest.xml:29:13-51
111            android:required="false" />
111-->[androidx.window:window:1.0.0] E:\android_development_cache\.gradle\caches\transforms-3\2d3a06f14c6f275b0094d4668de90ecb\transformed\window-1.0.0\AndroidManifest.xml:30:13-37
112
113        <receiver
113-->[androidx.profileinstaller:profileinstaller:1.3.0] E:\android_development_cache\.gradle\caches\transforms-3\0a451f34059334e77a169e82d985bb5f\transformed\profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
114            android:name="androidx.profileinstaller.ProfileInstallReceiver"
114-->[androidx.profileinstaller:profileinstaller:1.3.0] E:\android_development_cache\.gradle\caches\transforms-3\0a451f34059334e77a169e82d985bb5f\transformed\profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
115            android:directBootAware="false"
115-->[androidx.profileinstaller:profileinstaller:1.3.0] E:\android_development_cache\.gradle\caches\transforms-3\0a451f34059334e77a169e82d985bb5f\transformed\profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
116            android:enabled="true"
116-->[androidx.profileinstaller:profileinstaller:1.3.0] E:\android_development_cache\.gradle\caches\transforms-3\0a451f34059334e77a169e82d985bb5f\transformed\profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
117            android:exported="true"
117-->[androidx.profileinstaller:profileinstaller:1.3.0] E:\android_development_cache\.gradle\caches\transforms-3\0a451f34059334e77a169e82d985bb5f\transformed\profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
118            android:permission="android.permission.DUMP" >
118-->[androidx.profileinstaller:profileinstaller:1.3.0] E:\android_development_cache\.gradle\caches\transforms-3\0a451f34059334e77a169e82d985bb5f\transformed\profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
119            <intent-filter>
119-->[androidx.profileinstaller:profileinstaller:1.3.0] E:\android_development_cache\.gradle\caches\transforms-3\0a451f34059334e77a169e82d985bb5f\transformed\profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
120                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
120-->[androidx.profileinstaller:profileinstaller:1.3.0] E:\android_development_cache\.gradle\caches\transforms-3\0a451f34059334e77a169e82d985bb5f\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
120-->[androidx.profileinstaller:profileinstaller:1.3.0] E:\android_development_cache\.gradle\caches\transforms-3\0a451f34059334e77a169e82d985bb5f\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
121            </intent-filter>
122            <intent-filter>
122-->[androidx.profileinstaller:profileinstaller:1.3.0] E:\android_development_cache\.gradle\caches\transforms-3\0a451f34059334e77a169e82d985bb5f\transformed\profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
123                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
123-->[androidx.profileinstaller:profileinstaller:1.3.0] E:\android_development_cache\.gradle\caches\transforms-3\0a451f34059334e77a169e82d985bb5f\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
123-->[androidx.profileinstaller:profileinstaller:1.3.0] E:\android_development_cache\.gradle\caches\transforms-3\0a451f34059334e77a169e82d985bb5f\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
124            </intent-filter>
125            <intent-filter>
125-->[androidx.profileinstaller:profileinstaller:1.3.0] E:\android_development_cache\.gradle\caches\transforms-3\0a451f34059334e77a169e82d985bb5f\transformed\profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
126                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
126-->[androidx.profileinstaller:profileinstaller:1.3.0] E:\android_development_cache\.gradle\caches\transforms-3\0a451f34059334e77a169e82d985bb5f\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
126-->[androidx.profileinstaller:profileinstaller:1.3.0] E:\android_development_cache\.gradle\caches\transforms-3\0a451f34059334e77a169e82d985bb5f\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
127            </intent-filter>
128            <intent-filter>
128-->[androidx.profileinstaller:profileinstaller:1.3.0] E:\android_development_cache\.gradle\caches\transforms-3\0a451f34059334e77a169e82d985bb5f\transformed\profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
129                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
129-->[androidx.profileinstaller:profileinstaller:1.3.0] E:\android_development_cache\.gradle\caches\transforms-3\0a451f34059334e77a169e82d985bb5f\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
129-->[androidx.profileinstaller:profileinstaller:1.3.0] E:\android_development_cache\.gradle\caches\transforms-3\0a451f34059334e77a169e82d985bb5f\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
130            </intent-filter>
131        </receiver>
132    </application>
133
134</manifest>
