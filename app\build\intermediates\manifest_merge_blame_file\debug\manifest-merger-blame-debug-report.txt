1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.minimalmusicplayer"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
9        android:targetSdkVersion="34" />
10
11    <!-- Permissions for file access and media playback -->
12    <uses-permission
12-->C:\Users\<USER>\Desktop\musicC\app\src\main\AndroidManifest.xml:6:5-7:38
13        android:name="android.permission.READ_EXTERNAL_STORAGE"
13-->C:\Users\<USER>\Desktop\musicC\app\src\main\AndroidManifest.xml:6:22-77
14        android:maxSdkVersion="32" />
14-->C:\Users\<USER>\Desktop\musicC\app\src\main\AndroidManifest.xml:7:9-35
15    <uses-permission android:name="android.permission.READ_MEDIA_AUDIO" />
15-->C:\Users\<USER>\Desktop\musicC\app\src\main\AndroidManifest.xml:8:5-75
15-->C:\Users\<USER>\Desktop\musicC\app\src\main\AndroidManifest.xml:8:22-72
16    <uses-permission android:name="android.permission.WAKE_LOCK" />
16-->C:\Users\<USER>\Desktop\musicC\app\src\main\AndroidManifest.xml:9:5-68
16-->C:\Users\<USER>\Desktop\musicC\app\src\main\AndroidManifest.xml:9:22-65
17    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
17-->C:\Users\<USER>\Desktop\musicC\app\src\main\AndroidManifest.xml:10:5-77
17-->C:\Users\<USER>\Desktop\musicC\app\src\main\AndroidManifest.xml:10:22-74
18    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_MEDIA_PLAYBACK" />
18-->C:\Users\<USER>\Desktop\musicC\app\src\main\AndroidManifest.xml:11:5-92
18-->C:\Users\<USER>\Desktop\musicC\app\src\main\AndroidManifest.xml:11:22-89
19
20    <!-- Widget permissions -->
21    <uses-permission android:name="android.permission.BIND_APPWIDGET" />
21-->C:\Users\<USER>\Desktop\musicC\app\src\main\AndroidManifest.xml:14:5-73
21-->C:\Users\<USER>\Desktop\musicC\app\src\main\AndroidManifest.xml:14:22-70
22
23    <!-- Media session permissions -->
24    <uses-permission android:name="android.permission.MEDIA_CONTENT_CONTROL" />
24-->C:\Users\<USER>\Desktop\musicC\app\src\main\AndroidManifest.xml:17:5-80
24-->C:\Users\<USER>\Desktop\musicC\app\src\main\AndroidManifest.xml:17:22-77
25    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
25-->[com.google.android.exoplayer:exoplayer-core:2.19.1] E:\android_development_cache\.gradle\caches\transforms-3\f6a56acf230789289efb8d310bea470b\transformed\exoplayer-core-2.19.1\AndroidManifest.xml:24:5-79
25-->[com.google.android.exoplayer:exoplayer-core:2.19.1] E:\android_development_cache\.gradle\caches\transforms-3\f6a56acf230789289efb8d310bea470b\transformed\exoplayer-core-2.19.1\AndroidManifest.xml:24:22-76
26
27    <permission
27-->[androidx.core:core:1.10.1] E:\android_development_cache\.gradle\caches\transforms-3\d001c43b87f49a29f46bbdd20bd4ce3a\transformed\core-1.10.1\AndroidManifest.xml:22:5-24:47
28        android:name="com.minimalmusicplayer.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
28-->[androidx.core:core:1.10.1] E:\android_development_cache\.gradle\caches\transforms-3\d001c43b87f49a29f46bbdd20bd4ce3a\transformed\core-1.10.1\AndroidManifest.xml:23:9-81
29        android:protectionLevel="signature" />
29-->[androidx.core:core:1.10.1] E:\android_development_cache\.gradle\caches\transforms-3\d001c43b87f49a29f46bbdd20bd4ce3a\transformed\core-1.10.1\AndroidManifest.xml:24:9-44
30
31    <uses-permission android:name="com.minimalmusicplayer.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
31-->[androidx.core:core:1.10.1] E:\android_development_cache\.gradle\caches\transforms-3\d001c43b87f49a29f46bbdd20bd4ce3a\transformed\core-1.10.1\AndroidManifest.xml:26:5-97
31-->[androidx.core:core:1.10.1] E:\android_development_cache\.gradle\caches\transforms-3\d001c43b87f49a29f46bbdd20bd4ce3a\transformed\core-1.10.1\AndroidManifest.xml:26:22-94
32
33    <application
33-->C:\Users\<USER>\Desktop\musicC\app\src\main\AndroidManifest.xml:19:5-81:19
34        android:allowBackup="true"
34-->C:\Users\<USER>\Desktop\musicC\app\src\main\AndroidManifest.xml:20:9-35
35        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
35-->[androidx.core:core:1.10.1] E:\android_development_cache\.gradle\caches\transforms-3\d001c43b87f49a29f46bbdd20bd4ce3a\transformed\core-1.10.1\AndroidManifest.xml:28:18-86
36        android:dataExtractionRules="@xml/data_extraction_rules"
36-->C:\Users\<USER>\Desktop\musicC\app\src\main\AndroidManifest.xml:21:9-65
37        android:debuggable="true"
38        android:extractNativeLibs="true"
39        android:fullBackupContent="@xml/backup_rules"
39-->C:\Users\<USER>\Desktop\musicC\app\src\main\AndroidManifest.xml:22:9-54
40        android:icon="@mipmap/ic_launcher"
40-->C:\Users\<USER>\Desktop\musicC\app\src\main\AndroidManifest.xml:23:9-43
41        android:label="@string/app_name"
41-->C:\Users\<USER>\Desktop\musicC\app\src\main\AndroidManifest.xml:24:9-41
42        android:roundIcon="@mipmap/ic_launcher_round"
42-->C:\Users\<USER>\Desktop\musicC\app\src\main\AndroidManifest.xml:25:9-54
43        android:supportsRtl="true"
43-->C:\Users\<USER>\Desktop\musicC\app\src\main\AndroidManifest.xml:26:9-35
44        android:theme="@style/Theme.MinimalMusicPlayer" >
44-->C:\Users\<USER>\Desktop\musicC\app\src\main\AndroidManifest.xml:27:9-56
45
46        <!-- Simple Main Activity for testing -->
47        <activity
47-->C:\Users\<USER>\Desktop\musicC\app\src\main\AndroidManifest.xml:31:9-39:20
48            android:name="com.minimalmusicplayer.SimpleMainActivity"
48-->C:\Users\<USER>\Desktop\musicC\app\src\main\AndroidManifest.xml:32:13-47
49            android:exported="true"
49-->C:\Users\<USER>\Desktop\musicC\app\src\main\AndroidManifest.xml:33:13-36
50            android:theme="@style/Theme.MinimalMusicPlayer" >
50-->C:\Users\<USER>\Desktop\musicC\app\src\main\AndroidManifest.xml:34:13-60
51            <intent-filter>
51-->C:\Users\<USER>\Desktop\musicC\app\src\main\AndroidManifest.xml:35:13-38:29
52                <action android:name="android.intent.action.MAIN" />
52-->C:\Users\<USER>\Desktop\musicC\app\src\main\AndroidManifest.xml:36:17-69
52-->C:\Users\<USER>\Desktop\musicC\app\src\main\AndroidManifest.xml:36:25-66
53
54                <category android:name="android.intent.category.LAUNCHER" />
54-->C:\Users\<USER>\Desktop\musicC\app\src\main\AndroidManifest.xml:37:17-77
54-->C:\Users\<USER>\Desktop\musicC\app\src\main\AndroidManifest.xml:37:27-74
55            </intent-filter>
56        </activity>
57
58        <!-- Main Activity -->
59        <activity
59-->C:\Users\<USER>\Desktop\musicC\app\src\main\AndroidManifest.xml:42:9-45:63
60            android:name="com.minimalmusicplayer.MainActivity"
60-->C:\Users\<USER>\Desktop\musicC\app\src\main\AndroidManifest.xml:43:13-41
61            android:exported="false"
61-->C:\Users\<USER>\Desktop\musicC\app\src\main\AndroidManifest.xml:44:13-37
62            android:theme="@style/Theme.MinimalMusicPlayer" />
62-->C:\Users\<USER>\Desktop\musicC\app\src\main\AndroidManifest.xml:45:13-60
63
64        <!-- Directory Selection Activity -->
65        <activity
65-->C:\Users\<USER>\Desktop\musicC\app\src\main\AndroidManifest.xml:48:9-51:63
66            android:name="com.minimalmusicplayer.DirectorySelectionActivity"
66-->C:\Users\<USER>\Desktop\musicC\app\src\main\AndroidManifest.xml:49:13-55
67            android:exported="false"
67-->C:\Users\<USER>\Desktop\musicC\app\src\main\AndroidManifest.xml:50:13-37
68            android:theme="@style/Theme.MinimalMusicPlayer" />
68-->C:\Users\<USER>\Desktop\musicC\app\src\main\AndroidManifest.xml:51:13-60
69
70        <!-- Music Service -->
71        <service
71-->C:\Users\<USER>\Desktop\musicC\app\src\main\AndroidManifest.xml:54:9-58:61
72            android:name="com.minimalmusicplayer.service.MusicService"
72-->C:\Users\<USER>\Desktop\musicC\app\src\main\AndroidManifest.xml:55:13-49
73            android:enabled="true"
73-->C:\Users\<USER>\Desktop\musicC\app\src\main\AndroidManifest.xml:56:13-35
74            android:exported="false"
74-->C:\Users\<USER>\Desktop\musicC\app\src\main\AndroidManifest.xml:57:13-37
75            android:foregroundServiceType="mediaPlayback" />
75-->C:\Users\<USER>\Desktop\musicC\app\src\main\AndroidManifest.xml:58:13-58
76
77        <!-- Widget Provider -->
78        <receiver
78-->C:\Users\<USER>\Desktop\musicC\app\src\main\AndroidManifest.xml:61:9-70:20
79            android:name="com.minimalmusicplayer.widget.MusicWidgetProvider"
79-->C:\Users\<USER>\Desktop\musicC\app\src\main\AndroidManifest.xml:62:13-55
80            android:exported="true" >
80-->C:\Users\<USER>\Desktop\musicC\app\src\main\AndroidManifest.xml:63:13-36
81            <intent-filter>
81-->C:\Users\<USER>\Desktop\musicC\app\src\main\AndroidManifest.xml:64:13-66:29
82                <action android:name="android.appwidget.action.APPWIDGET_UPDATE" />
82-->C:\Users\<USER>\Desktop\musicC\app\src\main\AndroidManifest.xml:65:17-84
82-->C:\Users\<USER>\Desktop\musicC\app\src\main\AndroidManifest.xml:65:25-81
83            </intent-filter>
84
85            <meta-data
85-->C:\Users\<USER>\Desktop\musicC\app\src\main\AndroidManifest.xml:67:13-69:61
86                android:name="android.appwidget.provider"
86-->C:\Users\<USER>\Desktop\musicC\app\src\main\AndroidManifest.xml:68:17-58
87                android:resource="@xml/music_widget_info" />
87-->C:\Users\<USER>\Desktop\musicC\app\src\main\AndroidManifest.xml:69:17-58
88        </receiver>
89
90        <!-- Media Button Receiver -->
91        <receiver
91-->C:\Users\<USER>\Desktop\musicC\app\src\main\AndroidManifest.xml:73:9-79:20
92            android:name="androidx.media.session.MediaButtonReceiver"
92-->C:\Users\<USER>\Desktop\musicC\app\src\main\AndroidManifest.xml:74:13-70
93            android:exported="true" >
93-->C:\Users\<USER>\Desktop\musicC\app\src\main\AndroidManifest.xml:75:13-36
94            <intent-filter>
94-->C:\Users\<USER>\Desktop\musicC\app\src\main\AndroidManifest.xml:76:13-78:29
95                <action android:name="android.intent.action.MEDIA_BUTTON" />
95-->C:\Users\<USER>\Desktop\musicC\app\src\main\AndroidManifest.xml:77:17-77
95-->C:\Users\<USER>\Desktop\musicC\app\src\main\AndroidManifest.xml:77:25-74
96            </intent-filter>
97        </receiver>
98
99        <provider
99-->[androidx.emoji2:emoji2:1.2.0] E:\android_development_cache\.gradle\caches\transforms-3\1e3aff5aaafb1f3205851880ac1fd91b\transformed\emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
100            android:name="androidx.startup.InitializationProvider"
100-->[androidx.emoji2:emoji2:1.2.0] E:\android_development_cache\.gradle\caches\transforms-3\1e3aff5aaafb1f3205851880ac1fd91b\transformed\emoji2-1.2.0\AndroidManifest.xml:25:13-67
101            android:authorities="com.minimalmusicplayer.androidx-startup"
101-->[androidx.emoji2:emoji2:1.2.0] E:\android_development_cache\.gradle\caches\transforms-3\1e3aff5aaafb1f3205851880ac1fd91b\transformed\emoji2-1.2.0\AndroidManifest.xml:26:13-68
102            android:exported="false" >
102-->[androidx.emoji2:emoji2:1.2.0] E:\android_development_cache\.gradle\caches\transforms-3\1e3aff5aaafb1f3205851880ac1fd91b\transformed\emoji2-1.2.0\AndroidManifest.xml:27:13-37
103            <meta-data
103-->[androidx.emoji2:emoji2:1.2.0] E:\android_development_cache\.gradle\caches\transforms-3\1e3aff5aaafb1f3205851880ac1fd91b\transformed\emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
104                android:name="androidx.emoji2.text.EmojiCompatInitializer"
104-->[androidx.emoji2:emoji2:1.2.0] E:\android_development_cache\.gradle\caches\transforms-3\1e3aff5aaafb1f3205851880ac1fd91b\transformed\emoji2-1.2.0\AndroidManifest.xml:30:17-75
105                android:value="androidx.startup" />
105-->[androidx.emoji2:emoji2:1.2.0] E:\android_development_cache\.gradle\caches\transforms-3\1e3aff5aaafb1f3205851880ac1fd91b\transformed\emoji2-1.2.0\AndroidManifest.xml:31:17-49
106            <meta-data
106-->[androidx.lifecycle:lifecycle-process:2.6.1] E:\android_development_cache\.gradle\caches\transforms-3\d4bf48403fc213aec52ba971f9bad5bd\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:29:13-31:52
107                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
107-->[androidx.lifecycle:lifecycle-process:2.6.1] E:\android_development_cache\.gradle\caches\transforms-3\d4bf48403fc213aec52ba971f9bad5bd\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:30:17-78
108                android:value="androidx.startup" />
108-->[androidx.lifecycle:lifecycle-process:2.6.1] E:\android_development_cache\.gradle\caches\transforms-3\d4bf48403fc213aec52ba971f9bad5bd\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:31:17-49
109            <meta-data
109-->[androidx.profileinstaller:profileinstaller:1.3.0] E:\android_development_cache\.gradle\caches\transforms-3\0a451f34059334e77a169e82d985bb5f\transformed\profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
110                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
110-->[androidx.profileinstaller:profileinstaller:1.3.0] E:\android_development_cache\.gradle\caches\transforms-3\0a451f34059334e77a169e82d985bb5f\transformed\profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
111                android:value="androidx.startup" />
111-->[androidx.profileinstaller:profileinstaller:1.3.0] E:\android_development_cache\.gradle\caches\transforms-3\0a451f34059334e77a169e82d985bb5f\transformed\profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
112        </provider>
113
114        <uses-library
114-->[androidx.window:window:1.0.0] E:\android_development_cache\.gradle\caches\transforms-3\2d3a06f14c6f275b0094d4668de90ecb\transformed\window-1.0.0\AndroidManifest.xml:25:9-27:40
115            android:name="androidx.window.extensions"
115-->[androidx.window:window:1.0.0] E:\android_development_cache\.gradle\caches\transforms-3\2d3a06f14c6f275b0094d4668de90ecb\transformed\window-1.0.0\AndroidManifest.xml:26:13-54
116            android:required="false" />
116-->[androidx.window:window:1.0.0] E:\android_development_cache\.gradle\caches\transforms-3\2d3a06f14c6f275b0094d4668de90ecb\transformed\window-1.0.0\AndroidManifest.xml:27:13-37
117        <uses-library
117-->[androidx.window:window:1.0.0] E:\android_development_cache\.gradle\caches\transforms-3\2d3a06f14c6f275b0094d4668de90ecb\transformed\window-1.0.0\AndroidManifest.xml:28:9-30:40
118            android:name="androidx.window.sidecar"
118-->[androidx.window:window:1.0.0] E:\android_development_cache\.gradle\caches\transforms-3\2d3a06f14c6f275b0094d4668de90ecb\transformed\window-1.0.0\AndroidManifest.xml:29:13-51
119            android:required="false" />
119-->[androidx.window:window:1.0.0] E:\android_development_cache\.gradle\caches\transforms-3\2d3a06f14c6f275b0094d4668de90ecb\transformed\window-1.0.0\AndroidManifest.xml:30:13-37
120
121        <receiver
121-->[androidx.profileinstaller:profileinstaller:1.3.0] E:\android_development_cache\.gradle\caches\transforms-3\0a451f34059334e77a169e82d985bb5f\transformed\profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
122            android:name="androidx.profileinstaller.ProfileInstallReceiver"
122-->[androidx.profileinstaller:profileinstaller:1.3.0] E:\android_development_cache\.gradle\caches\transforms-3\0a451f34059334e77a169e82d985bb5f\transformed\profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
123            android:directBootAware="false"
123-->[androidx.profileinstaller:profileinstaller:1.3.0] E:\android_development_cache\.gradle\caches\transforms-3\0a451f34059334e77a169e82d985bb5f\transformed\profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
124            android:enabled="true"
124-->[androidx.profileinstaller:profileinstaller:1.3.0] E:\android_development_cache\.gradle\caches\transforms-3\0a451f34059334e77a169e82d985bb5f\transformed\profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
125            android:exported="true"
125-->[androidx.profileinstaller:profileinstaller:1.3.0] E:\android_development_cache\.gradle\caches\transforms-3\0a451f34059334e77a169e82d985bb5f\transformed\profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
126            android:permission="android.permission.DUMP" >
126-->[androidx.profileinstaller:profileinstaller:1.3.0] E:\android_development_cache\.gradle\caches\transforms-3\0a451f34059334e77a169e82d985bb5f\transformed\profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
127            <intent-filter>
127-->[androidx.profileinstaller:profileinstaller:1.3.0] E:\android_development_cache\.gradle\caches\transforms-3\0a451f34059334e77a169e82d985bb5f\transformed\profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
128                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
128-->[androidx.profileinstaller:profileinstaller:1.3.0] E:\android_development_cache\.gradle\caches\transforms-3\0a451f34059334e77a169e82d985bb5f\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
128-->[androidx.profileinstaller:profileinstaller:1.3.0] E:\android_development_cache\.gradle\caches\transforms-3\0a451f34059334e77a169e82d985bb5f\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
129            </intent-filter>
130            <intent-filter>
130-->[androidx.profileinstaller:profileinstaller:1.3.0] E:\android_development_cache\.gradle\caches\transforms-3\0a451f34059334e77a169e82d985bb5f\transformed\profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
131                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
131-->[androidx.profileinstaller:profileinstaller:1.3.0] E:\android_development_cache\.gradle\caches\transforms-3\0a451f34059334e77a169e82d985bb5f\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
131-->[androidx.profileinstaller:profileinstaller:1.3.0] E:\android_development_cache\.gradle\caches\transforms-3\0a451f34059334e77a169e82d985bb5f\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
132            </intent-filter>
133            <intent-filter>
133-->[androidx.profileinstaller:profileinstaller:1.3.0] E:\android_development_cache\.gradle\caches\transforms-3\0a451f34059334e77a169e82d985bb5f\transformed\profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
134                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
134-->[androidx.profileinstaller:profileinstaller:1.3.0] E:\android_development_cache\.gradle\caches\transforms-3\0a451f34059334e77a169e82d985bb5f\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
134-->[androidx.profileinstaller:profileinstaller:1.3.0] E:\android_development_cache\.gradle\caches\transforms-3\0a451f34059334e77a169e82d985bb5f\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
135            </intent-filter>
136            <intent-filter>
136-->[androidx.profileinstaller:profileinstaller:1.3.0] E:\android_development_cache\.gradle\caches\transforms-3\0a451f34059334e77a169e82d985bb5f\transformed\profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
137                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
137-->[androidx.profileinstaller:profileinstaller:1.3.0] E:\android_development_cache\.gradle\caches\transforms-3\0a451f34059334e77a169e82d985bb5f\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
137-->[androidx.profileinstaller:profileinstaller:1.3.0] E:\android_development_cache\.gradle\caches\transforms-3\0a451f34059334e77a169e82d985bb5f\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
138            </intent-filter>
139        </receiver>
140    </application>
141
142</manifest>
