// Generated by view binder compiler. Do not edit!
package com.minimalmusicplayer.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.ProgressBar;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.Toolbar;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.google.android.material.button.MaterialButton;
import com.google.android.material.floatingactionbutton.FloatingActionButton;
import com.minimalmusicplayer.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityDirectorySelectionBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final MaterialButton buttonScanAll;

  @NonNull
  public final FloatingActionButton fabAddDirectory;

  @NonNull
  public final ProgressBar progressBar;

  @NonNull
  public final RecyclerView recyclerViewDirectories;

  @NonNull
  public final TextView textViewEmptyState;

  @NonNull
  public final Toolbar toolbar;

  private ActivityDirectorySelectionBinding(@NonNull LinearLayout rootView,
      @NonNull MaterialButton buttonScanAll, @NonNull FloatingActionButton fabAddDirectory,
      @NonNull ProgressBar progressBar, @NonNull RecyclerView recyclerViewDirectories,
      @NonNull TextView textViewEmptyState, @NonNull Toolbar toolbar) {
    this.rootView = rootView;
    this.buttonScanAll = buttonScanAll;
    this.fabAddDirectory = fabAddDirectory;
    this.progressBar = progressBar;
    this.recyclerViewDirectories = recyclerViewDirectories;
    this.textViewEmptyState = textViewEmptyState;
    this.toolbar = toolbar;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityDirectorySelectionBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityDirectorySelectionBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_directory_selection, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityDirectorySelectionBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.buttonScanAll;
      MaterialButton buttonScanAll = ViewBindings.findChildViewById(rootView, id);
      if (buttonScanAll == null) {
        break missingId;
      }

      id = R.id.fabAddDirectory;
      FloatingActionButton fabAddDirectory = ViewBindings.findChildViewById(rootView, id);
      if (fabAddDirectory == null) {
        break missingId;
      }

      id = R.id.progressBar;
      ProgressBar progressBar = ViewBindings.findChildViewById(rootView, id);
      if (progressBar == null) {
        break missingId;
      }

      id = R.id.recyclerViewDirectories;
      RecyclerView recyclerViewDirectories = ViewBindings.findChildViewById(rootView, id);
      if (recyclerViewDirectories == null) {
        break missingId;
      }

      id = R.id.textViewEmptyState;
      TextView textViewEmptyState = ViewBindings.findChildViewById(rootView, id);
      if (textViewEmptyState == null) {
        break missingId;
      }

      id = R.id.toolbar;
      Toolbar toolbar = ViewBindings.findChildViewById(rootView, id);
      if (toolbar == null) {
        break missingId;
      }

      return new ActivityDirectorySelectionBinding((LinearLayout) rootView, buttonScanAll,
          fabAddDirectory, progressBar, recyclerViewDirectories, textViewEmptyState, toolbar);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
