<?xml version="1.0" encoding="utf-8"?>
<resources>
    <color name="purple_200">#FFBB86FC</color>
    <color name="purple_500">#FF6200EE</color>
    <color name="purple_700">#FF3700B3</color>
    <color name="teal_200">#FF03DAC5</color>
    <color name="teal_700">#FF018786</color>
    <color name="black">#FF000000</color>
    <color name="white">#FFFFFFFF</color>
    
    <!-- Modern Dark Music App Colors -->
    <color name="music_primary">#1DB954</color>
    <color name="music_primary_container">#0D4F1C</color>
    <color name="music_on_primary_container">#E8F5E8</color>

    <color name="music_secondary">#FF6B35</color>
    <color name="music_secondary_container">#8B2500</color>
    <color name="music_on_secondary_container">#FFF0E6</color>

    <!-- Dark theme colors -->
    <color name="music_background">#121212</color>
    <color name="music_on_background">#FFFFFF</color>
    <color name="music_surface">#1E1E1E</color>
    <color name="music_on_surface">#FFFFFF</color>
    <color name="music_surface_variant">#2A2A2A</color>
    <color name="music_on_surface_variant">#B3B3B3</color>

    <!-- Player specific colors -->
    <color name="player_background">#121212</color>
    <color name="player_surface">#1E1E1E</color>
    <color name="player_on_surface">#FFFFFF</color>
    <color name="player_accent">#1DB954</color>

    <!-- Legacy colors -->
    <color name="primary">#1DB954</color>
    <color name="primary_dark">#0D4F1C</color>
    <color name="accent">#FF6B35</color>
    <color name="primary_text">#1A1A1A</color>
    <color name="secondary_text">#666666</color>
    <color name="divider">#E0E0E0</color>

    <!-- Current song highlighting -->
    <color name="current_song_background">#E8F5E8</color>
    <color name="current_song_text">#1DB954</color>

    <!-- Widget colors -->
    <color name="widget_background">#AA000000</color>
    <color name="widget_button_background">#33FFFFFF</color>
</resources>
