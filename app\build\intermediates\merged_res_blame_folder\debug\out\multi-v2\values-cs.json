{"logs": [{"outputFile": "com.minimalmusicplayer.app-mergeDebugResources-2:/values-cs/values-cs.xml", "map": [{"source": "E:\\android_development_cache\\.gradle\\caches\\transforms-3\\44bdd8c9f94ff480fecdbcca682a189c\\transformed\\exoplayer-ui-2.19.1\\res\\values-cs\\values-cs.xml", "from": {"startLines": "2,11,17,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,287,610,924,1005,1085,1163,1265,1363,1441,1505,1594,1686,1756,1822,1887,1959,2072,2187,2310,2384,2464,2536,2617,2711,2806,2873,2938,2991,3049,3097,3158,3224,3291,3354,3421,3486,3545,3610,3674,3740,3792,3855,3932,4009", "endLines": "10,16,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64", "endColumns": "17,12,12,80,79,77,101,97,77,63,88,91,69,65,64,71,112,114,122,73,79,71,80,93,94,66,64,52,57,47,60,65,66,62,66,64,58,64,63,65,51,62,76,76,53", "endOffsets": "282,605,919,1000,1080,1158,1260,1358,1436,1500,1589,1681,1751,1817,1882,1954,2067,2182,2305,2379,2459,2531,2612,2706,2801,2868,2933,2986,3044,3092,3153,3219,3286,3349,3416,3481,3540,3605,3669,3735,3787,3850,3927,4004,4058"}, "to": {"startLines": "2,11,17,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,382,705,5577,5658,5738,5816,5918,6016,6094,6158,6247,6339,6409,6475,6540,6612,6725,6840,6963,7037,7117,7189,7270,7364,7459,7526,8250,8303,8361,8409,8470,8536,8603,8666,8733,8798,8857,8922,8986,9052,9104,9167,9244,9321", "endLines": "10,16,22,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123", "endColumns": "17,12,12,80,79,77,101,97,77,63,88,91,69,65,64,71,112,114,122,73,79,71,80,93,94,66,64,52,57,47,60,65,66,62,66,64,58,64,63,65,51,62,76,76,53", "endOffsets": "377,700,1014,5653,5733,5811,5913,6011,6089,6153,6242,6334,6404,6470,6535,6607,6720,6835,6958,7032,7112,7184,7265,7359,7454,7521,7586,8298,8356,8404,8465,8531,8598,8661,8728,8793,8852,8917,8981,9047,9099,9162,9239,9316,9370"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\transforms-3\\550895908bcd254b169aeba9b6074ec2\\transformed\\preference-1.2.1\\res\\values-cs\\values-cs.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,178,276,354,501,670,754", "endColumns": "72,97,77,146,168,83,80", "endOffsets": "173,271,349,496,665,749,830"}, "to": {"startLines": "71,124,177,179,182,183,184", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "5439,9375,13500,13657,13988,14157,14241", "endColumns": "72,97,77,146,168,83,80", "endOffsets": "5507,9468,13573,13799,14152,14236,14317"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\transforms-3\\d001c43b87f49a29f46bbdd20bd4ce3a\\transformed\\core-1.10.1\\res\\values-cs\\values-cs.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,356,455,560,667,786", "endColumns": "97,101,100,98,104,106,118,100", "endOffsets": "148,250,351,450,555,662,781,882"}, "to": {"startLines": "61,62,63,64,65,66,67,181", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4409,4507,4609,4710,4809,4914,5021,13887", "endColumns": "97,101,100,98,104,106,118,100", "endOffsets": "4502,4604,4705,4804,4909,5016,5135,13983"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\transforms-3\\f6a56acf230789289efb8d310bea470b\\transformed\\exoplayer-core-2.19.1\\res\\values-cs\\values-cs.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,131,193,256,325,402,472,554,634", "endColumns": "75,61,62,68,76,69,81,79,79", "endOffsets": "126,188,251,320,397,467,549,629,709"}, "to": {"startLines": "97,98,99,100,101,102,103,104,105", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "7591,7667,7729,7792,7861,7938,8008,8090,8170", "endColumns": "75,61,62,68,76,69,81,79,79", "endOffsets": "7662,7724,7787,7856,7933,8003,8085,8165,8245"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\transforms-3\\1674ca777f183a0ca81338d078e0cd6f\\transformed\\material-1.9.0\\res\\values-cs\\values-cs.xml", "from": {"startLines": "2,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,368,446,524,601,704,796,888,1014,1095,1160,1259,1335,1396,1485,1549,1616,1670,1738,1798,1852,1969,2029,2091,2145,2217,2339,2423,2515,2652,2730,2812,2900,2954,3005,3071,3143,3220,3304,3376,3453,3527,3598,3686,3757,3850,3945,4019,4093,4189,4241,4308,4394,4482,4544,4608,4671,4781,4877,4976,5074,5132,5187", "endLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69", "endColumns": "12,77,77,76,102,91,91,125,80,64,98,75,60,88,63,66,53,67,59,53,116,59,61,53,71,121,83,91,136,77,81,87,53,50,65,71,76,83,71,76,73,70,87,70,92,94,73,73,95,51,66,85,87,61,63,62,109,95,98,97,57,54,78", "endOffsets": "363,441,519,596,699,791,883,1009,1090,1155,1254,1330,1391,1480,1544,1611,1665,1733,1793,1847,1964,2024,2086,2140,2212,2334,2418,2510,2647,2725,2807,2895,2949,3000,3066,3138,3215,3299,3371,3448,3522,3593,3681,3752,3845,3940,4014,4088,4184,4236,4303,4389,4477,4539,4603,4666,4776,4872,4971,5069,5127,5182,5261"}, "to": {"startLines": "23,56,57,58,59,60,68,69,70,72,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,178", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1019,3981,4059,4137,4214,4317,5140,5232,5358,5512,9473,9572,9648,9709,9798,9862,9929,9983,10051,10111,10165,10282,10342,10404,10458,10530,10652,10736,10828,10965,11043,11125,11213,11267,11318,11384,11456,11533,11617,11689,11766,11840,11911,11999,12070,12163,12258,12332,12406,12502,12554,12621,12707,12795,12857,12921,12984,13094,13190,13289,13387,13445,13578", "endLines": "28,56,57,58,59,60,68,69,70,72,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,178", "endColumns": "12,77,77,76,102,91,91,125,80,64,98,75,60,88,63,66,53,67,59,53,116,59,61,53,71,121,83,91,136,77,81,87,53,50,65,71,76,83,71,76,73,70,87,70,92,94,73,73,95,51,66,85,87,61,63,62,109,95,98,97,57,54,78", "endOffsets": "1282,4054,4132,4209,4312,4404,5227,5353,5434,5572,9567,9643,9704,9793,9857,9924,9978,10046,10106,10160,10277,10337,10399,10453,10525,10647,10731,10823,10960,11038,11120,11208,11262,11313,11379,11451,11528,11612,11684,11761,11835,11906,11994,12065,12158,12253,12327,12401,12497,12549,12616,12702,12790,12852,12916,12979,13089,13185,13284,13382,13440,13495,13652"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\transforms-3\\b61f7d3f27c328116375b3d38dc2a4b7\\transformed\\appcompat-1.6.1\\res\\values-cs\\values-cs.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,314,424,510,615,732,810,886,977,1070,1165,1259,1353,1446,1541,1638,1729,1820,1904,2008,2120,2219,2325,2436,2538,2701,2799", "endColumns": "106,101,109,85,104,116,77,75,90,92,94,93,93,92,94,96,90,90,83,103,111,98,105,110,101,162,97,82", "endOffsets": "207,309,419,505,610,727,805,881,972,1065,1160,1254,1348,1441,1536,1633,1724,1815,1899,2003,2115,2214,2320,2431,2533,2696,2794,2877"}, "to": {"startLines": "29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,180", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1287,1394,1496,1606,1692,1797,1914,1992,2068,2159,2252,2347,2441,2535,2628,2723,2820,2911,3002,3086,3190,3302,3401,3507,3618,3720,3883,13804", "endColumns": "106,101,109,85,104,116,77,75,90,92,94,93,93,92,94,96,90,90,83,103,111,98,105,110,101,162,97,82", "endOffsets": "1389,1491,1601,1687,1792,1909,1987,2063,2154,2247,2342,2436,2530,2623,2718,2815,2906,2997,3081,3185,3297,3396,3502,3613,3715,3878,3976,13882"}}]}]}