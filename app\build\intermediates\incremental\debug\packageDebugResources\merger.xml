<?xml version="1.0" encoding="utf-8"?>
<merger version="3" xmlns:ns1="http://schemas.android.com/tools"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\musicC\app\src\main\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\musicC\app\src\main\res"><file name="ic_add" path="C:\Users\<USER>\Desktop\musicC\app\src\main\res\drawable\ic_add.xml" qualifiers="" type="drawable"/><file name="ic_delete" path="C:\Users\<USER>\Desktop\musicC\app\src\main\res\drawable\ic_delete.xml" qualifiers="" type="drawable"/><file name="ic_folder" path="C:\Users\<USER>\Desktop\musicC\app\src\main\res\drawable\ic_folder.xml" qualifiers="" type="drawable"/><file name="ic_launcher_background" path="C:\Users\<USER>\Desktop\musicC\app\src\main\res\drawable\ic_launcher_background.xml" qualifiers="" type="drawable"/><file name="ic_launcher_foreground" path="C:\Users\<USER>\Desktop\musicC\app\src\main\res\drawable\ic_launcher_foreground.xml" qualifiers="" type="drawable"/><file name="ic_pause" path="C:\Users\<USER>\Desktop\musicC\app\src\main\res\drawable\ic_pause.xml" qualifiers="" type="drawable"/><file name="ic_play" path="C:\Users\<USER>\Desktop\musicC\app\src\main\res\drawable\ic_play.xml" qualifiers="" type="drawable"/><file name="ic_refresh" path="C:\Users\<USER>\Desktop\musicC\app\src\main\res\drawable\ic_refresh.xml" qualifiers="" type="drawable"/><file name="ic_skip_next" path="C:\Users\<USER>\Desktop\musicC\app\src\main\res\drawable\ic_skip_next.xml" qualifiers="" type="drawable"/><file name="ic_skip_previous" path="C:\Users\<USER>\Desktop\musicC\app\src\main\res\drawable\ic_skip_previous.xml" qualifiers="" type="drawable"/><file name="widget_background" path="C:\Users\<USER>\Desktop\musicC\app\src\main\res\drawable\widget_background.xml" qualifiers="" type="drawable"/><file name="widget_button_background" path="C:\Users\<USER>\Desktop\musicC\app\src\main\res\drawable\widget_button_background.xml" qualifiers="" type="drawable"/><file name="widget_preview" path="C:\Users\<USER>\Desktop\musicC\app\src\main\res\drawable\widget_preview.xml" qualifiers="" type="drawable"/><file name="activity_directory_selection" path="C:\Users\<USER>\Desktop\musicC\app\src\main\res\layout\activity_directory_selection.xml" qualifiers="" type="layout"/><file name="activity_main" path="C:\Users\<USER>\Desktop\musicC\app\src\main\res\layout\activity_main.xml" qualifiers="" type="layout"/><file name="item_directory" path="C:\Users\<USER>\Desktop\musicC\app\src\main\res\layout\item_directory.xml" qualifiers="" type="layout"/><file name="item_song" path="C:\Users\<USER>\Desktop\musicC\app\src\main\res\layout\item_song.xml" qualifiers="" type="layout"/><file name="widget_music_player" path="C:\Users\<USER>\Desktop\musicC\app\src\main\res\layout\widget_music_player.xml" qualifiers="" type="layout"/><file name="main_menu" path="C:\Users\<USER>\Desktop\musicC\app\src\main\res\menu\main_menu.xml" qualifiers="" type="menu"/><file name="ic_launcher" path="C:\Users\<USER>\Desktop\musicC\app\src\main\res\mipmap-anydpi-v26\ic_launcher.xml" qualifiers="anydpi-v26" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\Desktop\musicC\app\src\main\res\mipmap-anydpi-v26\ic_launcher_round.xml" qualifiers="anydpi-v26" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\Desktop\musicC\app\src\main\res\mipmap-hdpi\ic_launcher.png" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\Desktop\musicC\app\src\main\res\mipmap-hdpi\ic_launcher_round.png" qualifiers="hdpi-v4" type="mipmap"/><file path="C:\Users\<USER>\Desktop\musicC\app\src\main\res\values\colors.xml" qualifiers=""><color name="purple_200">#FFBB86FC</color><color name="purple_500">#FF6200EE</color><color name="purple_700">#FF3700B3</color><color name="teal_200">#FF03DAC5</color><color name="teal_700">#FF018786</color><color name="black">#FF000000</color><color name="white">#FFFFFFFF</color><color name="primary">#FF1976D2</color><color name="primary_dark">#FF1565C0</color><color name="accent">#FF03DAC5</color><color name="primary_text">#DE000000</color><color name="secondary_text">#8A000000</color><color name="current_song_background">#1A1976D2</color><color name="current_song_text">#FF1976D2</color><color name="widget_background">#AA000000</color><color name="widget_button_background">#33FFFFFF</color></file><file path="C:\Users\<USER>\Desktop\musicC\app\src\main\res\values\strings.xml" qualifiers=""><string name="app_name">Minimal Music Player</string><string name="widget_description">Music player controls</string><string name="notification_channel_name">Music Playback</string><string name="notification_channel_description">Controls for music playback</string><string name="action_select_directories">Select Folders</string><string name="action_refresh">Refresh</string><string name="permission_storage_rationale">Storage permission is required to access your music files</string><string name="permission_denied_message">Storage permission is required to access your music files. Please grant permission in app settings.</string><string name="empty_no_directories">No music directories selected.\n\nUse the menu to add music folders.</string><string name="empty_no_songs">No music files found in selected directories.</string><string name="empty_no_songs_directories">No music directories selected. Use the menu to add music folders.</string><string name="directory_added">Directory added successfully</string><string name="directory_removed">Directory removed</string><string name="directory_add_failed">Failed to add directory</string><string name="directory_remove_failed">Failed to remove directory</string><string name="scan_completed">Found %d songs</string><string name="scan_failed">Scan failed</string><string name="unknown_title">Unknown</string><string name="unknown_artist">Unknown Artist</string><string name="unknown_album">Unknown Album</string><string name="no_song_playing">No song playing</string></file><file path="C:\Users\<USER>\Desktop\musicC\app\src\main\res\values\themes.xml" qualifiers=""><style name="Theme.MinimalMusicPlayer" parent="Theme.Material3.DayNight">
        
        <item name="colorPrimary">@color/primary</item>
        <item name="colorPrimaryVariant">@color/primary_dark</item>
        <item name="colorOnPrimary">@color/white</item>
        
        <item name="colorSecondary">@color/accent</item>
        <item name="colorSecondaryVariant">@color/teal_700</item>
        <item name="colorOnSecondary">@color/black</item>
        
        <item name="android:statusBarColor" ns1:targetApi="l">?attr/colorPrimaryVariant</item>
        
        
        
        <item name="android:textColorPrimary">@color/primary_text</item>
        <item name="android:textColorSecondary">@color/secondary_text</item>
    </style></file><file name="backup_rules" path="C:\Users\<USER>\Desktop\musicC\app\src\main\res\xml\backup_rules.xml" qualifiers="" type="xml"/><file name="data_extraction_rules" path="C:\Users\<USER>\Desktop\musicC\app\src\main\res\xml\data_extraction_rules.xml" qualifiers="" type="xml"/><file name="music_widget_info" path="C:\Users\<USER>\Desktop\musicC\app\src\main\res\xml\music_widget_info.xml" qualifiers="" type="xml"/></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\musicC\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug" generated-set="debug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\musicC\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\musicC\app\build\generated\res\resValues\debug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\musicC\app\build\generated\res\resValues\debug"/></dataSet><mergedItems/></merger>