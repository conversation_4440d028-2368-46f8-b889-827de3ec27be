package com.minimalmusicplayer

import android.app.Activity
import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.provider.DocumentsContract
import android.view.View
import android.widget.Toast
import androidx.activity.result.contract.ActivityResultContracts
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import com.minimalmusicplayer.adapter.DirectoryAdapter
import com.minimalmusicplayer.databinding.ActivityDirectorySelectionBinding
import com.minimalmusicplayer.util.FileScanner
import com.minimalmusicplayer.util.PreferenceManager
import kotlinx.coroutines.launch

class DirectorySelectionActivity : AppCompatActivity() {
    
    private lateinit var binding: ActivityDirectorySelectionBinding
    private lateinit var directoryAdapter: DirectoryAdapter
    private lateinit var preferenceManager: PreferenceManager
    private lateinit var fileScanner: FileScanner
    
    private val directoryPickerLauncher = registerForActivityResult(
        ActivityResultContracts.StartActivityForResult()
    ) { result ->
        if (result.resultCode == Activity.RESULT_OK) {
            result.data?.data?.let { uri ->
                handleDirectorySelection(uri)
            }
        }
    }
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityDirectorySelectionBinding.inflate(layoutInflater)
        setContentView(binding.root)
        
        preferenceManager = PreferenceManager(this)
        fileScanner = FileScanner(this)
        
        setupToolbar()
        setupRecyclerView()
        setupClickListeners()
        loadSelectedDirectories()
    }
    
    private fun setupToolbar() {
        setSupportActionBar(binding.toolbar)
        supportActionBar?.apply {
            setDisplayHomeAsUpEnabled(true)
            title = "Select Music Folders"
        }
    }
    
    private fun setupRecyclerView() {
        directoryAdapter = DirectoryAdapter { directoryUri ->
            removeDirectory(directoryUri)
        }
        
        binding.recyclerViewDirectories.apply {
            layoutManager = LinearLayoutManager(this@DirectorySelectionActivity)
            adapter = directoryAdapter
        }
    }
    
    private fun setupClickListeners() {
        binding.fabAddDirectory.setOnClickListener {
            openDirectoryPicker()
        }
        
        binding.buttonScanAll.setOnClickListener {
            scanAllDirectories()
        }
    }
    
    private fun openDirectoryPicker() {
        val intent = Intent(Intent.ACTION_OPEN_DOCUMENT_TREE).apply {
            flags = Intent.FLAG_GRANT_READ_URI_PERMISSION or
                    Intent.FLAG_GRANT_PERSISTABLE_URI_PERMISSION
        }
        directoryPickerLauncher.launch(intent)
    }
    
    private fun handleDirectorySelection(uri: Uri) {
        try {
            // Take persistable permission
            contentResolver.takePersistableUriPermission(
                uri,
                Intent.FLAG_GRANT_READ_URI_PERMISSION
            )
            
            // Add to selected directories
            val currentDirectories = preferenceManager.getSelectedDirectories().toMutableSet()
            currentDirectories.add(uri.toString())
            preferenceManager.saveSelectedDirectories(currentDirectories)
            
            // Update UI
            loadSelectedDirectories()
            
            Toast.makeText(this, "Directory added successfully", Toast.LENGTH_SHORT).show()
            
        } catch (e: Exception) {
            Toast.makeText(this, "Failed to add directory: ${e.message}", Toast.LENGTH_LONG).show()
        }
    }
    
    private fun removeDirectory(directoryUri: String) {
        try {
            // Remove persistable permission
            val uri = Uri.parse(directoryUri)
            contentResolver.releasePersistableUriPermission(
                uri,
                Intent.FLAG_GRANT_READ_URI_PERMISSION
            )
            
            // Remove from preferences
            val currentDirectories = preferenceManager.getSelectedDirectories().toMutableSet()
            currentDirectories.remove(directoryUri)
            preferenceManager.saveSelectedDirectories(currentDirectories)
            
            // Update UI
            loadSelectedDirectories()
            
            Toast.makeText(this, "Directory removed", Toast.LENGTH_SHORT).show()
            
        } catch (e: Exception) {
            Toast.makeText(this, "Failed to remove directory: ${e.message}", Toast.LENGTH_LONG).show()
        }
    }
    
    private fun loadSelectedDirectories() {
        val directories = preferenceManager.getSelectedDirectories()
        val directoryInfoList = directories.map { uriString ->
            val uri = Uri.parse(uriString)
            val displayName = getDirectoryDisplayName(uri)
            DirectoryAdapter.DirectoryInfo(uriString, displayName)
        }
        
        directoryAdapter.updateDirectories(directoryInfoList)
        
        // Show/hide empty state
        if (directories.isEmpty()) {
            binding.textViewEmptyState.visibility = View.VISIBLE
            binding.buttonScanAll.visibility = View.GONE
        } else {
            binding.textViewEmptyState.visibility = View.GONE
            binding.buttonScanAll.visibility = View.VISIBLE
        }
    }
    
    private fun getDirectoryDisplayName(uri: Uri): String {
        return try {
            val docId = DocumentsContract.getTreeDocumentId(uri)
            val split = docId.split(":")
            if (split.size >= 2) {
                split[1].ifEmpty { "Root" }
            } else {
                "Unknown Directory"
            }
        } catch (e: Exception) {
            "Unknown Directory"
        }
    }
    
    private fun scanAllDirectories() {
        val directories = preferenceManager.getSelectedDirectories()
        if (directories.isEmpty()) {
            Toast.makeText(this, "No directories selected", Toast.LENGTH_SHORT).show()
            return
        }
        
        binding.progressBar.visibility = View.VISIBLE
        binding.buttonScanAll.isEnabled = false
        
        lifecycleScope.launch {
            try {
                var totalSongs = 0
                
                for (directoryUri in directories) {
                    val uri = Uri.parse(directoryUri)
                    val songs = fileScanner.scanDirectory(uri)
                    totalSongs += songs.size
                }
                
                binding.progressBar.visibility = View.GONE
                binding.buttonScanAll.isEnabled = true
                
                Toast.makeText(
                    this@DirectorySelectionActivity,
                    "Found $totalSongs songs",
                    Toast.LENGTH_LONG
                ).show()
                
                // Return result to MainActivity
                setResult(Activity.RESULT_OK)
                finish()
                
            } catch (e: Exception) {
                binding.progressBar.visibility = View.GONE
                binding.buttonScanAll.isEnabled = true
                
                Toast.makeText(
                    this@DirectorySelectionActivity,
                    "Scan failed: ${e.message}",
                    Toast.LENGTH_LONG
                ).show()
            }
        }
    }
    
    override fun onSupportNavigateUp(): Boolean {
        onBackPressed()
        return true
    }
}
