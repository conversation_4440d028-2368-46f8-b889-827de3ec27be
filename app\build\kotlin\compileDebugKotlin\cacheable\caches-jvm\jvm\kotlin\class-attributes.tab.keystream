1com.minimalmusicplayer.DirectorySelectionActivity#com.minimalmusicplayer.MainActivity/com.minimalmusicplayer.adapter.DirectoryAdapter=com.minimalmusicplayer.adapter.DirectoryAdapter.DirectoryInfoCcom.minimalmusicplayer.adapter.DirectoryAdapter.DirectoryViewHolder*com.minimalmusicplayer.adapter.SongAdapter9com.minimalmusicplayer.adapter.SongAdapter.SongViewHolder!com.minimalmusicplayer.model.Song+com.minimalmusicplayer.service.MusicService<com.minimalmusicplayer.service.MusicService.PlaybackListener7com.minimalmusicplayer.service.MusicService.MusicBinder5com.minimalmusicplayer.service.MusicService.Companion(com.minimalmusicplayer.util.ErrorHandler'com.minimalmusicplayer.util.FileScanner,com.minimalmusicplayer.util.PermissionHelper-com.minimalmusicplayer.util.PreferenceManager7com.minimalmusicplayer.util.PreferenceManager.Companion1com.minimalmusicplayer.widget.MusicWidgetProvider;com.minimalmusicplayer.widget.MusicWidgetProvider.CompanionDcom.minimalmusicplayer.databinding.ActivityDirectorySelectionBinding7com.minimalmusicplayer.databinding.ItemDirectoryBinding2com.minimalmusicplayer.databinding.ItemSongBinding6com.minimalmusicplayer.databinding.ActivityMainBinding                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 