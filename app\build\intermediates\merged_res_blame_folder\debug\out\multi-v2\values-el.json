{"logs": [{"outputFile": "com.minimalmusicplayer.app-mergeDebugResources-2:/values-el/values-el.xml", "map": [{"source": "E:\\android_development_cache\\.gradle\\caches\\transforms-3\\44bdd8c9f94ff480fecdbcca682a189c\\transformed\\exoplayer-ui-2.19.1\\res\\values-el\\values-el.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,287,510,738,826,915,997,1080,1172,1269,1335,1431,1527,1592,1662,1727,1801,1923,2046,2169,2239,2322,2394,2491,2596,2700,2766,2841,2894,2952,3006,3067,3132,3201,3266,3338,3400,3460,3525,3592,3659,3717,3783,3863,3943", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,87,88,81,82,91,96,65,95,95,64,69,64,73,121,122,122,69,82,71,96,104,103,65,74,52,57,53,60,64,68,64,71,61,59,64,66,66,57,65,79,79,53", "endOffsets": "282,505,733,821,910,992,1075,1167,1264,1330,1426,1522,1587,1657,1722,1796,1918,2041,2164,2234,2317,2389,2486,2591,2695,2761,2836,2889,2947,3001,3062,3127,3196,3261,3333,3395,3455,3520,3587,3654,3712,3778,3858,3938,3992"}, "to": {"startLines": "2,11,15,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,382,605,5405,5493,5582,5664,5747,5839,5936,6002,6098,6194,6259,6329,6394,6468,6590,6713,6836,6906,6989,7061,7158,7263,7367,7433,8169,8222,8280,8334,8395,8460,8529,8594,8666,8728,8788,8853,8920,8987,9045,9111,9191,9271", "endLines": "10,14,18,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117", "endColumns": "17,12,12,87,88,81,82,91,96,65,95,95,64,69,64,73,121,122,122,69,82,71,96,104,103,65,74,52,57,53,60,64,68,64,71,61,59,64,66,66,57,65,79,79,53", "endOffsets": "377,600,828,5488,5577,5659,5742,5834,5931,5997,6093,6189,6254,6324,6389,6463,6585,6708,6831,6901,6984,7056,7153,7258,7362,7428,7503,8217,8275,8329,8390,8455,8524,8589,8661,8723,8783,8848,8915,8982,9040,9106,9186,9266,9320"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\transforms-3\\b61f7d3f27c328116375b3d38dc2a4b7\\transformed\\appcompat-1.6.1\\res\\values-el\\values-el.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,223,334,451,536,642,765,854,939,1030,1123,1218,1312,1412,1505,1600,1697,1788,1879,1964,2075,2184,2286,2397,2507,2615,2786,2886", "endColumns": "117,110,116,84,105,122,88,84,90,92,94,93,99,92,94,96,90,90,84,110,108,101,110,109,107,170,99,85", "endOffsets": "218,329,446,531,637,760,849,934,1025,1118,1213,1307,1407,1500,1595,1692,1783,1874,1959,2070,2179,2281,2392,2502,2610,2781,2881,2967"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,174", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1003,1121,1232,1349,1434,1540,1663,1752,1837,1928,2021,2116,2210,2310,2403,2498,2595,2686,2777,2862,2973,3082,3184,3295,3405,3513,3684,13922", "endColumns": "117,110,116,84,105,122,88,84,90,92,94,93,99,92,94,96,90,90,84,110,108,101,110,109,107,170,99,85", "endOffsets": "1116,1227,1344,1429,1535,1658,1747,1832,1923,2016,2111,2205,2305,2398,2493,2590,2681,2772,2857,2968,3077,3179,3290,3400,3508,3679,3779,14003"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\transforms-3\\1674ca777f183a0ca81338d078e0cd6f\\transformed\\material-1.9.0\\res\\values-el\\values-el.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,270,358,444,529,625,712,814,931,1017,1083,1183,1265,1328,1419,1482,1547,1609,1678,1740,1794,1932,1989,2050,2104,2177,2330,2415,2499,2638,2719,2804,2894,2949,3000,3066,3144,3229,3314,3386,3466,3546,3617,3709,3781,3878,3975,4049,4123,4225,4281,4353,4441,4533,4595,4659,4722,4838,4946,5055,5163,5222,5277", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "endColumns": "12,87,85,84,95,86,101,116,85,65,99,81,62,90,62,64,61,68,61,53,137,56,60,53,72,152,84,83,138,80,84,89,54,50,65,77,84,84,71,79,79,70,91,71,96,96,73,73,101,55,71,87,91,61,63,62,115,107,108,107,58,54,90", "endOffsets": "265,353,439,524,620,707,809,926,1012,1078,1178,1260,1323,1414,1477,1542,1604,1673,1735,1789,1927,1984,2045,2099,2172,2325,2410,2494,2633,2714,2799,2889,2944,2995,3061,3139,3224,3309,3381,3461,3541,3612,3704,3776,3873,3970,4044,4118,4220,4276,4348,4436,4528,4590,4654,4717,4833,4941,5050,5158,5217,5272,5363"}, "to": {"startLines": "19,50,51,52,53,54,62,63,64,66,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,172", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "833,3784,3872,3958,4043,4139,4961,5063,5180,5339,9412,9512,9594,9657,9748,9811,9876,9938,10007,10069,10123,10261,10318,10379,10433,10506,10659,10744,10828,10967,11048,11133,11223,11278,11329,11395,11473,11558,11643,11715,11795,11875,11946,12038,12110,12207,12304,12378,12452,12554,12610,12682,12770,12862,12924,12988,13051,13167,13275,13384,13492,13551,13689", "endLines": "22,50,51,52,53,54,62,63,64,66,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,172", "endColumns": "12,87,85,84,95,86,101,116,85,65,99,81,62,90,62,64,61,68,61,53,137,56,60,53,72,152,84,83,138,80,84,89,54,50,65,77,84,84,71,79,79,70,91,71,96,96,73,73,101,55,71,87,91,61,63,62,115,107,108,107,58,54,90", "endOffsets": "998,3867,3953,4038,4134,4221,5058,5175,5261,5400,9507,9589,9652,9743,9806,9871,9933,10002,10064,10118,10256,10313,10374,10428,10501,10654,10739,10823,10962,11043,11128,11218,11273,11324,11390,11468,11553,11638,11710,11790,11870,11941,12033,12105,12202,12299,12373,12447,12549,12605,12677,12765,12857,12919,12983,13046,13162,13270,13379,13487,13546,13601,13775"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\transforms-3\\f6a56acf230789289efb8d310bea470b\\transformed\\exoplayer-core-2.19.1\\res\\values-el\\values-el.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,126,184,242,305,379,455,554,649", "endColumns": "70,57,57,62,73,75,98,94,66", "endOffsets": "121,179,237,300,374,450,549,644,711"}, "to": {"startLines": "91,92,93,94,95,96,97,98,99", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "7508,7579,7637,7695,7758,7832,7908,8007,8102", "endColumns": "70,57,57,62,73,75,98,94,66", "endOffsets": "7574,7632,7690,7753,7827,7903,8002,8097,8164"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\transforms-3\\550895908bcd254b169aeba9b6074ec2\\transformed\\preference-1.2.1\\res\\values-el\\values-el.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,178,265,348,490,659,744", "endColumns": "72,86,82,141,168,84,79", "endOffsets": "173,260,343,485,654,739,819"}, "to": {"startLines": "65,118,171,173,176,177,178", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "5266,9325,13606,13780,14109,14278,14363", "endColumns": "72,86,82,141,168,84,79", "endOffsets": "5334,9407,13684,13917,14273,14358,14438"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\transforms-3\\d001c43b87f49a29f46bbdd20bd4ce3a\\transformed\\core-1.10.1\\res\\values-el\\values-el.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,256,356,459,567,673,790", "endColumns": "97,102,99,102,107,105,116,100", "endOffsets": "148,251,351,454,562,668,785,886"}, "to": {"startLines": "55,56,57,58,59,60,61,175", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4226,4324,4427,4527,4630,4738,4844,14008", "endColumns": "97,102,99,102,107,105,116,100", "endOffsets": "4319,4422,4522,4625,4733,4839,4956,14104"}}]}]}