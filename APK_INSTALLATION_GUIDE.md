# 🎵 Minimal Music Player - APK 安装指南

## ✅ APK 构建成功！

**APK 文件位置**: `app\build\outputs\apk\debug\app-debug.apk`
**文件大小**: 9.25 MB
**构建时间**: 2025年7月14日

## 📱 安装步骤

### 方法一：直接安装到连接的Android设备
1. 确保你的Android设备已连接到电脑
2. 启用USB调试模式
3. 运行以下命令：
```bash
adb install app\build\outputs\apk\debug\app-debug.apk
```

### 方法二：手动安装
1. 将 `app-debug.apk` 文件复制到你的Android设备
2. 在设备上找到APK文件
3. 点击安装（可能需要允许"未知来源"安装）

### 方法三：使用Android Studio
1. 在Android Studio中打开项目
2. 连接Android设备或启动模拟器
3. 点击"Run"按钮直接安装和运行

## 🔧 系统要求

- **最低Android版本**: Android 5.0 (API 21)
- **目标Android版本**: Android 14 (API 34)
- **架构支持**: ARM, ARM64, x86, x86_64
- **权限需求**: 
  - 存储访问权限（读取音频文件）
  - 前台服务权限（后台播放）
  - 小部件权限（桌面小部件）

## 🎯 首次使用指南

### 1. 授予权限
- 首次启动时会请求存储权限
- 点击"允许"以访问音频文件

### 2. 选择音乐目录
- 点击工具栏中的文件夹图标
- 选择包含音乐文件的目录
- 点击"扫描所有目录"

### 3. 开始播放
- 音乐列表会显示扫描到的歌曲
- 点击任意歌曲开始播放
- 使用底部的播放控制栏

### 4. 添加桌面小部件
- 长按桌面空白处
- 选择"小部件"
- 找到"Minimal Music Player"
- 拖拽2x2小部件到桌面

## 🎵 支持的音频格式

- **MP3** - 最常见的音频格式
- **FLAC** - 无损压缩格式
- **OGG** - 开源音频格式
- **M4A/AAC** - Apple音频格式
- **WAV** - 未压缩音频
- **WMA** - Windows Media Audio

## 🔍 功能特性

### 核心功能
- ✅ 本地音频播放
- ✅ 目录选择和管理
- ✅ 基本播放控制（播放/暂停/上一首/下一首）
- ✅ 后台播放支持
- ✅ 媒体会话集成（锁屏控制）

### 桌面小部件
- ✅ 2x2紧凑型小部件
- ✅ 实时歌曲信息显示
- ✅ 直接播放控制
- ✅ 自动状态更新

### 用户界面
- ✅ Material Design 3设计
- ✅ 极简主义界面
- ✅ 深色/浅色主题支持
- ✅ 响应式布局

## 🛠️ 故障排除

### 权限问题
- 如果无法访问音乐文件，请检查存储权限
- 在设置 > 应用 > Minimal Music Player > 权限中手动授予

### 播放问题
- 确保音频文件格式受支持
- 检查文件是否损坏
- 重新扫描音乐目录

### 小部件问题
- 如果小部件不更新，尝试重新添加
- 确保应用没有被系统杀死
- 检查电池优化设置

### 性能问题
- 大量音频文件可能需要更长扫描时间
- 建议分批选择目录
- 清理应用缓存可能有帮助

## 📞 技术支持

如果遇到问题：
1. 检查Android版本是否兼容（需要5.0+）
2. 确认设备有足够存储空间
3. 重启应用或设备
4. 查看系统日志获取详细错误信息

## 🔄 重新构建APK

如果需要重新构建APK：
```bash
# 清理之前的构建
.\gradlew.bat clean

# 构建新的Debug APK
.\gradlew.bat assembleDebug

# 构建Release APK（需要签名配置）
.\gradlew.bat assembleRelease
```

## 📝 版本信息

- **应用名称**: Minimal Music Player
- **包名**: com.minimalmusicplayer
- **版本号**: 1.0 (versionCode: 1)
- **构建类型**: Debug
- **签名**: Debug签名（仅用于测试）

---

🎉 **恭喜！你的极简音乐播放器已经准备就绪！**

享受你的音乐播放体验吧！ 🎵
