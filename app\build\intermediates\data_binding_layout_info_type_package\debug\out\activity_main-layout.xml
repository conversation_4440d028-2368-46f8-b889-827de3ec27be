<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_main" modulePackage="com.minimalmusicplayer" filePath="app\src\main\res\layout\activity_main.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.coordinatorlayout.widget.CoordinatorLayout"><Targets><Target tag="layout/activity_main_0" view="androidx.coordinatorlayout.widget.CoordinatorLayout"><Expressions/><location startLine="1" startOffset="0" endLine="148" endOffset="53"/></Target><Target id="@+id/toolbar" view="androidx.appcompat.widget.Toolbar"><Expressions/><location startLine="13" startOffset="8" endLine="18" endOffset="66"/></Target><Target id="@+id/recyclerViewSongs" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="35" startOffset="12" endLine="41" endOffset="52"/></Target><Target id="@+id/textViewEmptyState" view="TextView"><Expressions/><location startLine="44" startOffset="12" endLine="53" endOffset="43"/></Target><Target id="@+id/progressBar" view="ProgressBar"><Expressions/><location startLine="56" startOffset="12" endLine="61" endOffset="43"/></Target><Target id="@+id/playerContainer" view="LinearLayout"><Expressions/><location startLine="66" startOffset="8" endLine="144" endOffset="22"/></Target><Target id="@+id/textViewSongTitle" view="TextView"><Expressions/><location startLine="84" startOffset="16" endLine="93" endOffset="45"/></Target><Target id="@+id/textViewSongArtist" view="TextView"><Expressions/><location startLine="95" startOffset="16" endLine="103" endOffset="46"/></Target><Target id="@+id/buttonPrevious" view="ImageButton"><Expressions/><location startLine="114" startOffset="16" endLine="121" endOffset="57"/></Target><Target id="@+id/buttonPlayPause" view="ImageButton"><Expressions/><location startLine="123" startOffset="16" endLine="131" endOffset="57"/></Target><Target id="@+id/buttonNext" view="ImageButton"><Expressions/><location startLine="133" startOffset="16" endLine="140" endOffset="57"/></Target></Targets></Layout>