// Generated by view binder compiler. Do not edit!
package com.minimalmusicplayer.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageButton;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.minimalmusicplayer.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemDirectoryBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final ImageButton buttonRemove;

  @NonNull
  public final TextView textViewDirectoryName;

  @NonNull
  public final TextView textViewDirectoryPath;

  private ItemDirectoryBinding(@NonNull LinearLayout rootView, @NonNull ImageButton buttonRemove,
      @NonNull TextView textViewDirectoryName, @NonNull TextView textViewDirectoryPath) {
    this.rootView = rootView;
    this.buttonRemove = buttonRemove;
    this.textViewDirectoryName = textViewDirectoryName;
    this.textViewDirectoryPath = textViewDirectoryPath;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemDirectoryBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemDirectoryBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_directory, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemDirectoryBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.buttonRemove;
      ImageButton buttonRemove = ViewBindings.findChildViewById(rootView, id);
      if (buttonRemove == null) {
        break missingId;
      }

      id = R.id.textViewDirectoryName;
      TextView textViewDirectoryName = ViewBindings.findChildViewById(rootView, id);
      if (textViewDirectoryName == null) {
        break missingId;
      }

      id = R.id.textViewDirectoryPath;
      TextView textViewDirectoryPath = ViewBindings.findChildViewById(rootView, id);
      if (textViewDirectoryPath == null) {
        break missingId;
      }

      return new ItemDirectoryBinding((LinearLayout) rootView, buttonRemove, textViewDirectoryName,
          textViewDirectoryPath);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
