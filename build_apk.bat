@echo off
echo Building Minimal Music Player Debug APK...
echo.

REM Check if Android SDK is available
if not defined ANDROID_HOME (
    echo ERROR: ANDROID_HOME environment variable is not set
    echo Please set ANDROID_HOME to your Android SDK path
    pause
    exit /b 1
)

echo Android SDK found at: %ANDROID_HOME%
echo.

REM Check if Java is available
java -version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Java is not installed or not in PATH
    echo Please install Java JDK 8 or higher
    pause
    exit /b 1
)

echo Java is available
echo.

REM Download Gradle if not present
if not exist "gradle\wrapper\gradle-wrapper.jar" (
    echo Downloading Gradle wrapper...
    mkdir gradle\wrapper 2>nul
    
    REM Try to download gradle-wrapper.jar using PowerShell
    powershell -Command "& {Invoke-WebRequest -Uri 'https://github.com/gradle/gradle/raw/v8.0.0/gradle/wrapper/gradle-wrapper.jar' -OutFile 'gradle\wrapper\gradle-wrapper.jar'}"
    
    if not exist "gradle\wrapper\gradle-wrapper.jar" (
        echo ERROR: Failed to download Gradle wrapper
        echo Please download gradle-wrapper.jar manually and place it in gradle\wrapper\
        pause
        exit /b 1
    )
)

echo Gradle wrapper is ready
echo.

REM Build the APK
echo Building debug APK...
gradlew.bat assembleDebug

if errorlevel 1 (
    echo.
    echo ERROR: Build failed
    echo Please check the error messages above
    pause
    exit /b 1
)

echo.
echo SUCCESS: Debug APK built successfully!
echo.
echo APK location: app\build\outputs\apk\debug\app-debug.apk
echo.

REM Check if APK exists
if exist "app\build\outputs\apk\debug\app-debug.apk" (
    echo APK file size:
    dir "app\build\outputs\apk\debug\app-debug.apk" | find "app-debug.apk"
    echo.
    echo You can now install this APK on your Android device
) else (
    echo WARNING: APK file not found at expected location
    echo Please check the build output above
)

pause
