// Generated by view binder compiler. Do not edit!
package com.minimalmusicplayer.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageButton;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.minimalmusicplayer.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class MusicWidgetBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final ImageButton widgetBtnNext;

  @NonNull
  public final ImageButton widgetBtnPlayPause;

  @NonNull
  public final ImageButton widgetBtnPrevious;

  @NonNull
  public final TextView widgetSongArtist;

  @NonNull
  public final TextView widgetSongTitle;

  private MusicWidgetBinding(@NonNull LinearLayout rootView, @NonNull ImageButton widgetBtnNext,
      @NonNull ImageButton widgetBtnPlayPause, @NonNull ImageButton widgetBtnPrevious,
      @NonNull TextView widgetSongArtist, @NonNull TextView widgetSongTitle) {
    this.rootView = rootView;
    this.widgetBtnNext = widgetBtnNext;
    this.widgetBtnPlayPause = widgetBtnPlayPause;
    this.widgetBtnPrevious = widgetBtnPrevious;
    this.widgetSongArtist = widgetSongArtist;
    this.widgetSongTitle = widgetSongTitle;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static MusicWidgetBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static MusicWidgetBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.music_widget, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static MusicWidgetBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.widget_btn_next;
      ImageButton widgetBtnNext = ViewBindings.findChildViewById(rootView, id);
      if (widgetBtnNext == null) {
        break missingId;
      }

      id = R.id.widget_btn_play_pause;
      ImageButton widgetBtnPlayPause = ViewBindings.findChildViewById(rootView, id);
      if (widgetBtnPlayPause == null) {
        break missingId;
      }

      id = R.id.widget_btn_previous;
      ImageButton widgetBtnPrevious = ViewBindings.findChildViewById(rootView, id);
      if (widgetBtnPrevious == null) {
        break missingId;
      }

      id = R.id.widget_song_artist;
      TextView widgetSongArtist = ViewBindings.findChildViewById(rootView, id);
      if (widgetSongArtist == null) {
        break missingId;
      }

      id = R.id.widget_song_title;
      TextView widgetSongTitle = ViewBindings.findChildViewById(rootView, id);
      if (widgetSongTitle == null) {
        break missingId;
      }

      return new MusicWidgetBinding((LinearLayout) rootView, widgetBtnNext, widgetBtnPlayPause,
          widgetBtnPrevious, widgetSongArtist, widgetSongTitle);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
