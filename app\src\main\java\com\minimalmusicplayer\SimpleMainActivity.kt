package com.minimalmusicplayer

import android.os.Bundle
import android.widget.TextView
import androidx.appcompat.app.AppCompatActivity

class SimpleMainActivity : AppCompatActivity() {
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        // Create a simple layout programmatically to avoid layout issues
        val textView = TextView(this).apply {
            text = "Minimal Music Player\n\nApp is working!\n\nThis is a test version to ensure the app doesn't crash."
            textSize = 18f
            setPadding(32, 32, 32, 32)
        }
        
        setContentView(textView)
    }
}
