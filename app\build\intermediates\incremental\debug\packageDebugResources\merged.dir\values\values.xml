<?xml version="1.0" encoding="utf-8"?>
<resources>
    <color name="accent">#FF6B35</color>
    <color name="black">#FF000000</color>
    <color name="current_song_background">#E8F5E8</color>
    <color name="current_song_text">#1DB954</color>
    <color name="divider">#E0E0E0</color>
    <color name="music_background">#121212</color>
    <color name="music_card_background">#1A1A1A</color>
    <color name="music_on_surface">#FFFFFF</color>
    <color name="music_on_surface_variant">#B3B3B3</color>
    <color name="music_primary">#1DB954</color>
    <color name="music_surface">#191414</color>
    <color name="music_surface_variant">#282828</color>
    <color name="player_accent">#1DB954</color>
    <color name="player_background">#121212</color>
    <color name="player_on_surface">#FFFFFF</color>
    <color name="player_surface">#1E1E1E</color>
    <color name="primary">#1DB954</color>
    <color name="primary_dark">#0D4F1C</color>
    <color name="primary_text">#1A1A1A</color>
    <color name="purple_200">#FFBB86FC</color>
    <color name="purple_500">#FF6200EE</color>
    <color name="purple_700">#FF3700B3</color>
    <color name="secondary_text">#666666</color>
    <color name="spotify_black">#191414</color>
    <color name="spotify_dark_gray">#121212</color>
    <color name="spotify_dark_green">#1ED760</color>
    <color name="spotify_gray">#282828</color>
    <color name="spotify_green">#1DB954</color>
    <color name="spotify_light_gray">#B3B3B3</color>
    <color name="spotify_white">#FFFFFF</color>
    <color name="teal_200">#FF03DAC5</color>
    <color name="teal_700">#FF018786</color>
    <color name="white">#FFFFFFFF</color>
    <color name="widget_background">#AA000000</color>
    <color name="widget_button_background">#33FFFFFF</color>
    <string name="action_refresh">Refresh</string>
    <string name="action_select_directories">Select Folders</string>
    <string name="app_name">Minimal Music Player</string>
    <string name="directory_add_failed">Failed to add directory</string>
    <string name="directory_added">Directory added successfully</string>
    <string name="directory_remove_failed">Failed to remove directory</string>
    <string name="directory_removed">Directory removed</string>
    <string name="empty_no_directories">No music directories selected.\n\nUse the menu to add music folders.</string>
    <string name="empty_no_songs">No music files found in selected directories.</string>
    <string name="empty_no_songs_directories">No music directories selected. Use the menu to add music folders.</string>
    <string name="no_song_playing">No song playing</string>
    <string name="notification_channel_description">Controls for music playback</string>
    <string name="notification_channel_name">Music Playback</string>
    <string name="permission_denied_message">Storage permission is required to access your music files. Please grant permission in app settings.</string>
    <string name="permission_storage_rationale">Storage permission is required to access your music files</string>
    <string name="scan_completed">Found %d songs</string>
    <string name="scan_failed">Scan failed</string>
    <string name="unknown_album">Unknown Album</string>
    <string name="unknown_artist">Unknown Artist</string>
    <string name="unknown_title">Unknown</string>
    <string name="widget_description">Music player controls</string>
    <style name="Theme.MinimalMusicPlayer" parent="Theme.Material3.DayNight">
        
        <item name="colorPrimary">@color/spotify_green</item>
        <item name="colorOnPrimary">@color/spotify_white</item>

        
        <item name="colorSecondary">@color/spotify_green</item>
        <item name="colorOnSecondary">@color/spotify_white</item>

        
        <item name="colorSurface">@color/spotify_black</item>
        <item name="colorOnSurface">@color/spotify_white</item>
        <item name="colorSurfaceVariant">@color/spotify_gray</item>
        <item name="colorOnSurfaceVariant">@color/spotify_light_gray</item>

        
        <item name="android:colorBackground">@color/spotify_dark_gray</item>
        <item name="colorOnBackground">@color/spotify_white</item>

        
        <item name="android:statusBarColor">@color/spotify_black</item>
        <item name="android:navigationBarColor">@color/spotify_black</item>

        
        <item name="android:windowLightStatusBar">false</item>
        <item name="android:windowLightNavigationBar">false</item>
    </style>
</resources>