<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="item_song" modulePackage="com.minimalmusicplayer" filePath="app\src\main\res\layout\item_song.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/item_song_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="51" endOffset="14"/></Target><Target id="@+id/textViewTitle" view="TextView"><Expressions/><location startLine="16" startOffset="8" endLine="25" endOffset="37"/></Target><Target id="@+id/textViewArtist" view="TextView"><Expressions/><location startLine="27" startOffset="8" endLine="36" endOffset="38"/></Target><Target id="@+id/textViewDuration" view="TextView"><Expressions/><location startLine="41" startOffset="4" endLine="49" endOffset="27"/></Target></Targets></Layout>