<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/widget_container"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/widget_background"
    android:orientation="vertical"
    android:padding="8dp">

    <!-- Song info section -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:gravity="center_vertical"
        android:orientation="vertical">

        <TextView
            android:id="@+id/widget_song_title"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:gravity="center"
            android:maxLines="1"
            android:text="No song playing"
            android:textColor="@android:color/white"
            android:textSize="12sp"
            android:textStyle="bold" />

        <TextView
            android:id="@+id/widget_artist"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="2dp"
            android:ellipsize="end"
            android:gravity="center"
            android:maxLines="1"
            android:text="Unknown Artist"
            android:textColor="@android:color/white"
            android:textSize="10sp" />

    </LinearLayout>

    <!-- Control buttons section -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:orientation="horizontal">

        <ImageButton
            android:id="@+id/widget_previous"
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:background="@drawable/widget_button_background"
            android:contentDescription="Previous"
            android:scaleType="centerInside"
            android:src="@drawable/ic_skip_previous"
            android:tint="@android:color/white" />

        <ImageButton
            android:id="@+id/widget_play_pause"
            android:layout_width="36dp"
            android:layout_height="36dp"
            android:layout_marginHorizontal="8dp"
            android:background="@drawable/widget_button_background"
            android:contentDescription="Play/Pause"
            android:scaleType="centerInside"
            android:src="@drawable/ic_play"
            android:tint="@android:color/white" />

        <ImageButton
            android:id="@+id/widget_next"
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:background="@drawable/widget_button_background"
            android:contentDescription="Next"
            android:scaleType="centerInside"
            android:src="@drawable/ic_skip_next"
            android:tint="@android:color/white" />

    </LinearLayout>

</LinearLayout>
