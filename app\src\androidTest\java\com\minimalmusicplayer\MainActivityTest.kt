package com.minimalmusicplayer

import androidx.test.espresso.Espresso.onView
import androidx.test.espresso.action.ViewActions.click
import androidx.test.espresso.assertion.ViewAssertions.matches
import androidx.test.espresso.matcher.ViewMatchers.*
import androidx.test.ext.junit.rules.ActivityScenarioRule
import androidx.test.ext.junit.runners.AndroidJUnit4
import androidx.test.platform.app.InstrumentationRegistry
import org.junit.Rule
import org.junit.Test
import org.junit.runner.RunWith

@RunWith(AndroidJUnit4::class)
class MainActivityTest {
    
    @get:Rule
    val activityRule = ActivityScenarioRule(MainActivity::class.java)
    
    @Test
    fun testMainActivityLaunches() {
        // Test that the main activity launches successfully
        onView(withId(R.id.toolbar))
            .check(matches(isDisplayed()))
    }
    
    @Test
    fun testRecyclerViewExists() {
        // Test that the RecyclerView for songs exists
        onView(withId(R.id.recyclerViewSongs))
            .check(matches(isDisplayed()))
    }
    
    @Test
    fun testPlaybackControlsExist() {
        // Test that playback controls exist (they may be hidden initially)
        onView(withId(R.id.buttonPlayPause))
            .check(matches(isDisplayed()))
        
        onView(withId(R.id.buttonPrevious))
            .check(matches(isDisplayed()))
        
        onView(withId(R.id.buttonNext))
            .check(matches(isDisplayed()))
    }
    
    @Test
    fun testMenuExists() {
        // Test that the options menu can be opened
        onView(withId(R.id.toolbar))
            .check(matches(isDisplayed()))
    }
    
    @Test
    fun testEmptyStateHandling() {
        // This test would need to be expanded based on the actual state
        // For now, just check that the empty state text view exists
        onView(withId(R.id.textViewEmptyState))
            .check(matches(isDisplayed()))
    }
}
