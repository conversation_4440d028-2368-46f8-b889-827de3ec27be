<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    tools:context=".MainActivity">

    <androidx.appcompat.widget.Toolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="?attr/colorPrimary"
        android:theme="@style/ThemeOverlay.AppCompat.Dark.ActionBar"
        app:popupTheme="@style/ThemeOverlay.AppCompat.Light" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:orientation="vertical">

        <!-- Main content area -->
        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1">

            <!-- Song list -->
            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recyclerViewSongs"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:clipToPadding="false"
                android:paddingBottom="80dp"
                tools:listitem="@layout/item_song" />

            <!-- Empty state -->
            <TextView
                android:id="@+id/textViewEmptyState"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:gravity="center"
                android:padding="32dp"
                android:text="No music files found"
                android:textSize="16sp"
                android:visibility="gone" />

            <!-- Loading indicator -->
            <ProgressBar
                android:id="@+id/progressBar"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:visibility="gone" />

        </FrameLayout>

        <!-- Mini player -->
        <LinearLayout
            android:id="@+id/playerContainer"
            android:layout_width="match_parent"
            android:layout_height="80dp"
            android:background="?attr/colorSurface"
            android:elevation="8dp"
            android:orientation="horizontal"
            android:padding="16dp"
            android:visibility="gone">

            <!-- Song info -->
            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:gravity="center_vertical"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/textViewSongTitle"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:ellipsize="end"
                    android:maxLines="1"
                    android:textColor="?attr/colorOnSurface"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    tools:text="Song Title" />

                <TextView
                    android:id="@+id/textViewSongArtist"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:ellipsize="end"
                    android:maxLines="1"
                    android:textColor="?attr/colorOnSurfaceVariant"
                    android:textSize="14sp"
                    tools:text="Artist Name" />

            </LinearLayout>

            <!-- Playback controls -->
            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <ImageButton
                    android:id="@+id/buttonPrevious"
                    android:layout_width="48dp"
                    android:layout_height="48dp"
                    android:background="?attr/selectableItemBackgroundBorderless"
                    android:contentDescription="Previous"
                    android:src="@drawable/ic_skip_previous"
                    android:tint="?attr/colorOnSurface" />

                <ImageButton
                    android:id="@+id/buttonPlayPause"
                    android:layout_width="48dp"
                    android:layout_height="48dp"
                    android:layout_marginHorizontal="8dp"
                    android:background="?attr/selectableItemBackgroundBorderless"
                    android:contentDescription="Play/Pause"
                    android:src="@drawable/ic_play"
                    android:tint="?attr/colorOnSurface" />

                <ImageButton
                    android:id="@+id/buttonNext"
                    android:layout_width="48dp"
                    android:layout_height="48dp"
                    android:background="?attr/selectableItemBackgroundBorderless"
                    android:contentDescription="Next"
                    android:src="@drawable/ic_skip_next"
                    android:tint="?attr/colorOnSurface" />

            </LinearLayout>

        </LinearLayout>

    </LinearLayout>

</LinearLayout>
