<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="widget_music_player" modulePackage="com.minimalmusicplayer" filePath="app\src\main\res\layout\widget_music_player.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout" rootNodeViewId="@+id/widget_container"><Targets><Target id="@+id/widget_container" tag="layout/widget_music_player_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="83" endOffset="14"/></Target><Target id="@+id/widget_song_title" view="TextView"><Expressions/><location startLine="17" startOffset="8" endLine="27" endOffset="38"/></Target><Target id="@+id/widget_artist" view="TextView"><Expressions/><location startLine="29" startOffset="8" endLine="39" endOffset="37"/></Target><Target id="@+id/widget_previous" view="ImageButton"><Expressions/><location startLine="50" startOffset="8" endLine="58" endOffset="49"/></Target><Target id="@+id/widget_play_pause" view="ImageButton"><Expressions/><location startLine="60" startOffset="8" endLine="69" endOffset="49"/></Target><Target id="@+id/widget_next" view="ImageButton"><Expressions/><location startLine="71" startOffset="8" endLine="79" endOffset="49"/></Target></Targets></Layout>