<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="item_directory" modulePackage="com.minimalmusicplayer" filePath="app\src\main\res\layout\item_directory.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/item_directory_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="60" endOffset="14"/></Target><Target id="@+id/textViewDirectoryName" view="TextView"><Expressions/><location startLine="25" startOffset="8" endLine="34" endOffset="32"/></Target><Target id="@+id/textViewDirectoryPath" view="TextView"><Expressions/><location startLine="36" startOffset="8" endLine="45" endOffset="52"/></Target><Target id="@+id/buttonRemove" view="ImageButton"><Expressions/><location startLine="50" startOffset="4" endLine="58" endOffset="41"/></Target></Targets></Layout>