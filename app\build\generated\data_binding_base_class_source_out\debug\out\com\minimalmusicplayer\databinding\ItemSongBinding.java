// Generated by view binder compiler. Do not edit!
package com.minimalmusicplayer.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.minimalmusicplayer.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemSongBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final TextView textViewArtist;

  @NonNull
  public final TextView textViewDuration;

  @NonNull
  public final TextView textViewTitle;

  private ItemSongBinding(@NonNull LinearLayout rootView, @NonNull TextView textViewArtist,
      @NonNull TextView textViewDuration, @NonNull TextView textViewTitle) {
    this.rootView = rootView;
    this.textViewArtist = textViewArtist;
    this.textViewDuration = textViewDuration;
    this.textViewTitle = textViewTitle;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemSongBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemSongBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_song, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemSongBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.textViewArtist;
      TextView textViewArtist = ViewBindings.findChildViewById(rootView, id);
      if (textViewArtist == null) {
        break missingId;
      }

      id = R.id.textViewDuration;
      TextView textViewDuration = ViewBindings.findChildViewById(rootView, id);
      if (textViewDuration == null) {
        break missingId;
      }

      id = R.id.textViewTitle;
      TextView textViewTitle = ViewBindings.findChildViewById(rootView, id);
      if (textViewTitle == null) {
        break missingId;
      }

      return new ItemSongBinding((LinearLayout) rootView, textViewArtist, textViewDuration,
          textViewTitle);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
