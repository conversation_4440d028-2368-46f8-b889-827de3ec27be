<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <!-- Permissions for file access and media playback -->
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE"
        android:maxSdkVersion="32" />
    <uses-permission android:name="android.permission.READ_MEDIA_AUDIO" />
    <uses-permission android:name="android.permission.WAKE_LOCK" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_MEDIA_PLAYBACK" />
    
    <!-- Widget permissions -->
    <uses-permission android:name="android.permission.BIND_APPWIDGET" />
    
    <!-- Media session permissions -->
    <uses-permission android:name="android.permission.MEDIA_CONTENT_CONTROL" />

    <application
        android:allowBackup="true"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/Theme.MinimalMusicPlayer"
        tools:targetApi="31">
        
        <!-- Simple Main Activity for testing -->
        <activity
            android:name=".SimpleMainActivity"
            android:exported="true"
            android:theme="@style/Theme.MinimalMusicPlayer">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>

        <!-- Main Activity -->
        <activity
            android:name=".MainActivity"
            android:exported="false"
            android:theme="@style/Theme.MinimalMusicPlayer" />
        
        <!-- Directory Selection Activity -->
        <activity
            android:name=".DirectorySelectionActivity"
            android:exported="false"
            android:theme="@style/Theme.MinimalMusicPlayer" />
        
        <!-- Music Service -->
        <service
            android:name=".service.MusicService"
            android:enabled="true"
            android:exported="false"
            android:foregroundServiceType="mediaPlayback" />
        
        <!-- Widget Provider -->
        <receiver
            android:name=".widget.MusicWidgetProvider"
            android:exported="true">
            <intent-filter>
                <action android:name="android.appwidget.action.APPWIDGET_UPDATE" />
            </intent-filter>
            <meta-data
                android:name="android.appwidget.provider"
                android:resource="@xml/music_widget_info" />
        </receiver>
        
        <!-- Media Button Receiver -->
        <receiver
            android:name="androidx.media.session.MediaButtonReceiver"
            android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.MEDIA_BUTTON" />
            </intent-filter>
        </receiver>
        
    </application>

</manifest>
