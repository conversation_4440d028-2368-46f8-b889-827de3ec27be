package com.minimalmusicplayer.widget

import android.app.PendingIntent
import android.appwidget.AppWidgetManager
import android.appwidget.AppWidgetProvider
import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.widget.RemoteViews
import com.minimalmusicplayer.MainActivity
import com.minimalmusicplayer.R
import com.minimalmusicplayer.service.MusicService
import com.minimalmusicplayer.util.ErrorHandler

class MusicWidgetProvider : AppWidgetProvider() {
    
    companion object {
        const val ACTION_PLAY_PAUSE = "com.minimalmusicplayer.widget.PLAY_PAUSE"
        const val ACTION_NEXT = "com.minimalmusicplayer.widget.NEXT"
        const val ACTION_PREVIOUS = "com.minimalmusicplayer.widget.PREVIOUS"
        
        fun updateWidget(context: Context, songTitle: String?, artist: String?, isPlaying: Boolean) {
            try {
                val appWidgetManager = AppWidgetManager.getInstance(context)
                val componentName = ComponentName(context, MusicWidgetProvider::class.java)
                val widgetIds = appWidgetManager.getAppWidgetIds(componentName)

                for (widgetId in widgetIds) {
                    updateAppWidget(context, appWidgetManager, widgetId, songTitle, artist, isPlaying)
                }
            } catch (e: Exception) {
                ErrorHandler.handleWidgetError(e)
            }
        }
        
        private fun updateAppWidget(
            context: Context,
            appWidgetManager: AppWidgetManager,
            appWidgetId: Int,
            songTitle: String?,
            artist: String?,
            isPlaying: Boolean
        ) {
            val views = RemoteViews(context.packageName, R.layout.widget_music_player)
            
            // Update song info
            views.setTextViewText(
                R.id.widget_song_title,
                songTitle ?: "No song playing"
            )
            views.setTextViewText(
                R.id.widget_artist,
                artist ?: "Unknown Artist"
            )
            
            // Update play/pause button
            val playPauseIcon = if (isPlaying) R.drawable.ic_pause else R.drawable.ic_play
            views.setImageViewResource(R.id.widget_play_pause, playPauseIcon)
            
            // Set up click intents
            setupClickIntents(context, views)
            
            appWidgetManager.updateAppWidget(appWidgetId, views)
        }
        
        private fun setupClickIntents(context: Context, views: RemoteViews) {
            // App launch intent
            val appIntent = Intent(context, MainActivity::class.java)
            val appPendingIntent = PendingIntent.getActivity(
                context,
                0,
                appIntent,
                PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
            )
            views.setOnClickPendingIntent(R.id.widget_container, appPendingIntent)
            
            // Play/Pause intent
            val playPauseIntent = Intent(context, MusicWidgetProvider::class.java).apply {
                action = ACTION_PLAY_PAUSE
            }
            val playPausePendingIntent = PendingIntent.getBroadcast(
                context,
                1,
                playPauseIntent,
                PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
            )
            views.setOnClickPendingIntent(R.id.widget_play_pause, playPausePendingIntent)
            
            // Previous intent
            val previousIntent = Intent(context, MusicWidgetProvider::class.java).apply {
                action = ACTION_PREVIOUS
            }
            val previousPendingIntent = PendingIntent.getBroadcast(
                context,
                2,
                previousIntent,
                PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
            )
            views.setOnClickPendingIntent(R.id.widget_previous, previousPendingIntent)
            
            // Next intent
            val nextIntent = Intent(context, MusicWidgetProvider::class.java).apply {
                action = ACTION_NEXT
            }
            val nextPendingIntent = PendingIntent.getBroadcast(
                context,
                3,
                nextIntent,
                PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
            )
            views.setOnClickPendingIntent(R.id.widget_next, nextPendingIntent)
        }
    }
    
    override fun onUpdate(
        context: Context,
        appWidgetManager: AppWidgetManager,
        appWidgetIds: IntArray
    ) {
        for (appWidgetId in appWidgetIds) {
            updateAppWidget(context, appWidgetManager, appWidgetId, null, null, false)
        }
    }
    
    override fun onReceive(context: Context, intent: Intent) {
        super.onReceive(context, intent)
        
        when (intent.action) {
            ACTION_PLAY_PAUSE -> {
                sendMusicServiceCommand(context, "PLAY_PAUSE")
            }
            ACTION_NEXT -> {
                sendMusicServiceCommand(context, "NEXT")
            }
            ACTION_PREVIOUS -> {
                sendMusicServiceCommand(context, "PREVIOUS")
            }
        }
    }
    
    private fun sendMusicServiceCommand(context: Context, command: String) {
        val serviceIntent = Intent(context, MusicService::class.java).apply {
            action = command
        }
        context.startService(serviceIntent)
    }
    
    override fun onEnabled(context: Context) {
        // Called when the first widget is created
    }
    
    override fun onDisabled(context: Context) {
        // Called when the last widget is removed
    }
}
