<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:ns1="http://schemas.android.com/tools">
    <color name="accent">#FF03DAC5</color>
    <color name="black">#FF000000</color>
    <color name="current_song_background">#1A1976D2</color>
    <color name="current_song_text">#FF1976D2</color>
    <color name="primary">#FF1976D2</color>
    <color name="primary_dark">#FF1565C0</color>
    <color name="primary_text">#DE000000</color>
    <color name="purple_200">#FFBB86FC</color>
    <color name="purple_500">#FF6200EE</color>
    <color name="purple_700">#FF3700B3</color>
    <color name="secondary_text">#8A000000</color>
    <color name="teal_200">#FF03DAC5</color>
    <color name="teal_700">#FF018786</color>
    <color name="white">#FFFFFFFF</color>
    <color name="widget_background">#AA000000</color>
    <color name="widget_button_background">#33FFFFFF</color>
    <string name="action_refresh">Refresh</string>
    <string name="action_select_directories">Select Folders</string>
    <string name="app_name">Minimal Music Player</string>
    <string name="directory_add_failed">Failed to add directory</string>
    <string name="directory_added">Directory added successfully</string>
    <string name="directory_remove_failed">Failed to remove directory</string>
    <string name="directory_removed">Directory removed</string>
    <string name="empty_no_directories">No music directories selected.\n\nUse the menu to add music folders.</string>
    <string name="empty_no_songs">No music files found in selected directories.</string>
    <string name="empty_no_songs_directories">No music directories selected. Use the menu to add music folders.</string>
    <string name="no_song_playing">No song playing</string>
    <string name="notification_channel_description">Controls for music playback</string>
    <string name="notification_channel_name">Music Playback</string>
    <string name="permission_denied_message">Storage permission is required to access your music files. Please grant permission in app settings.</string>
    <string name="permission_storage_rationale">Storage permission is required to access your music files</string>
    <string name="scan_completed">Found %d songs</string>
    <string name="scan_failed">Scan failed</string>
    <string name="unknown_album">Unknown Album</string>
    <string name="unknown_artist">Unknown Artist</string>
    <string name="unknown_title">Unknown</string>
    <string name="widget_description">Music player controls</string>
    <style name="Theme.MinimalMusicPlayer" parent="Theme.Material3.DayNight">
        
        <item name="colorPrimary">@color/primary</item>
        <item name="colorPrimaryVariant">@color/primary_dark</item>
        <item name="colorOnPrimary">@color/white</item>
        
        <item name="colorSecondary">@color/accent</item>
        <item name="colorSecondaryVariant">@color/teal_700</item>
        <item name="colorOnSecondary">@color/black</item>
        
        <item name="android:statusBarColor" ns1:targetApi="l">?attr/colorPrimaryVariant</item>
        
        
        
        <item name="android:textColorPrimary">@color/primary_text</item>
        <item name="android:textColorSecondary">@color/secondary_text</item>
    </style>
</resources>