<?xml version="1.0" encoding="utf-8"?>
<resources>
    <color name="accent">#FF6B35</color>
    <color name="black">#FF000000</color>
    <color name="current_song_background">#E8F5E8</color>
    <color name="current_song_text">#1DB954</color>
    <color name="divider">#E0E0E0</color>
    <color name="music_background">#121212</color>
    <color name="music_on_background">#FFFFFF</color>
    <color name="music_on_primary_container">#E8F5E8</color>
    <color name="music_on_secondary_container">#FFF0E6</color>
    <color name="music_on_surface">#FFFFFF</color>
    <color name="music_on_surface_variant">#B3B3B3</color>
    <color name="music_primary">#1DB954</color>
    <color name="music_primary_container">#0D4F1C</color>
    <color name="music_secondary">#FF6B35</color>
    <color name="music_secondary_container">#8B2500</color>
    <color name="music_surface">#1E1E1E</color>
    <color name="music_surface_variant">#2A2A2A</color>
    <color name="player_accent">#1DB954</color>
    <color name="player_background">#121212</color>
    <color name="player_on_surface">#FFFFFF</color>
    <color name="player_surface">#1E1E1E</color>
    <color name="primary">#1DB954</color>
    <color name="primary_dark">#0D4F1C</color>
    <color name="primary_text">#1A1A1A</color>
    <color name="purple_200">#FFBB86FC</color>
    <color name="purple_500">#FF6200EE</color>
    <color name="purple_700">#FF3700B3</color>
    <color name="secondary_text">#666666</color>
    <color name="teal_200">#FF03DAC5</color>
    <color name="teal_700">#FF018786</color>
    <color name="white">#FFFFFFFF</color>
    <color name="widget_background">#AA000000</color>
    <color name="widget_button_background">#33FFFFFF</color>
    <string name="action_refresh">Refresh</string>
    <string name="action_select_directories">Select Folders</string>
    <string name="app_name">Minimal Music Player</string>
    <string name="directory_add_failed">Failed to add directory</string>
    <string name="directory_added">Directory added successfully</string>
    <string name="directory_remove_failed">Failed to remove directory</string>
    <string name="directory_removed">Directory removed</string>
    <string name="empty_no_directories">No music directories selected.\n\nUse the menu to add music folders.</string>
    <string name="empty_no_songs">No music files found in selected directories.</string>
    <string name="empty_no_songs_directories">No music directories selected. Use the menu to add music folders.</string>
    <string name="no_song_playing">No song playing</string>
    <string name="notification_channel_description">Controls for music playback</string>
    <string name="notification_channel_name">Music Playback</string>
    <string name="permission_denied_message">Storage permission is required to access your music files. Please grant permission in app settings.</string>
    <string name="permission_storage_rationale">Storage permission is required to access your music files</string>
    <string name="scan_completed">Found %d songs</string>
    <string name="scan_failed">Scan failed</string>
    <string name="unknown_album">Unknown Album</string>
    <string name="unknown_artist">Unknown Artist</string>
    <string name="unknown_title">Unknown</string>
    <string name="widget_description">Music player controls</string>
    <style name="Theme.MinimalMusicPlayer" parent="Theme.Material3.DayNight">
        
        <item name="colorPrimary">@color/music_primary</item>
        <item name="colorOnPrimary">@color/white</item>
        <item name="colorPrimaryContainer">@color/music_primary_container</item>
        <item name="colorOnPrimaryContainer">@color/music_on_primary_container</item>

        
        <item name="colorSecondary">@color/music_secondary</item>
        <item name="colorOnSecondary">@color/white</item>
        <item name="colorSecondaryContainer">@color/music_secondary_container</item>
        <item name="colorOnSecondaryContainer">@color/music_on_secondary_container</item>

        
        <item name="colorSurface">@color/music_surface</item>
        <item name="colorOnSurface">@color/music_on_surface</item>
        <item name="colorSurfaceVariant">@color/music_surface_variant</item>
        <item name="colorOnSurfaceVariant">@color/music_on_surface_variant</item>

        
        <item name="android:colorBackground">@color/music_background</item>
        <item name="colorOnBackground">@color/music_on_background</item>

        
        <item name="android:statusBarColor">@color/music_primary</item>
        <item name="android:navigationBarColor">@color/music_surface</item>

        
        <item name="android:windowLightStatusBar">false</item>
        <item name="android:windowLightNavigationBar">true</item>
    </style>
</resources>