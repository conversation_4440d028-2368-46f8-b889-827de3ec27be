package com.minimalmusicplayer

import com.minimalmusicplayer.model.Song
import org.junit.Test
import org.junit.Assert.*

class SongTest {
    
    @Test
    fun testSongCreation() {
        val song = Song(
            id = 1,
            title = "Test Song",
            artist = "Test Artist",
            album = "Test Album",
            duration = 180000, // 3 minutes
            path = "/storage/music/test.mp3",
            size = 5000000,
            dateModified = System.currentTimeMillis()
        )
        
        assertEquals("Test Song", song.title)
        assertEquals("Test Artist", song.artist)
        assertEquals("Test Album", song.album)
        assertEquals(180000, song.duration)
    }
    
    @Test
    fun testGetDisplayTitle() {
        val songWithTitle = Song(
            id = 1,
            title = "My Song",
            artist = "Artist",
            album = "Album",
            duration = 180000,
            path = "/music/mysong.mp3",
            size = 5000000,
            dateModified = System.currentTimeMillis()
        )
        
        assertEquals("My Song", songWithTitle.getDisplayTitle())
        
        val songWithoutTitle = Song(
            id = 2,
            title = "",
            artist = "Artist",
            album = "Album",
            duration = 180000,
            path = "/music/filename.mp3",
            size = 5000000,
            dateModified = System.currentTimeMillis()
        )
        
        assertEquals("filename", songWithoutTitle.getDisplayTitle())
    }
    
    @Test
    fun testGetDisplayArtist() {
        val songWithArtist = Song(
            id = 1,
            title = "Song",
            artist = "Known Artist",
            album = "Album",
            duration = 180000,
            path = "/music/song.mp3",
            size = 5000000,
            dateModified = System.currentTimeMillis()
        )
        
        assertEquals("Known Artist", songWithArtist.getDisplayArtist())
        
        val songWithoutArtist = Song(
            id = 2,
            title = "Song",
            artist = "<unknown>",
            album = "Album",
            duration = 180000,
            path = "/music/song.mp3",
            size = 5000000,
            dateModified = System.currentTimeMillis()
        )
        
        assertEquals("Unknown Artist", songWithoutArtist.getDisplayArtist())
    }
    
    @Test
    fun testGetDurationString() {
        val song = Song(
            id = 1,
            title = "Song",
            artist = "Artist",
            album = "Album",
            duration = 185000, // 3:05
            path = "/music/song.mp3",
            size = 5000000,
            dateModified = System.currentTimeMillis()
        )
        
        assertEquals("3:05", song.getDurationString())
    }
    
    @Test
    fun testGetFileExtension() {
        val mp3Song = Song(
            id = 1,
            title = "Song",
            artist = "Artist",
            album = "Album",
            duration = 180000,
            path = "/music/song.mp3",
            size = 5000000,
            dateModified = System.currentTimeMillis()
        )
        
        assertEquals("mp3", mp3Song.getFileExtension())
        
        val flacSong = Song(
            id = 2,
            title = "Song",
            artist = "Artist",
            album = "Album",
            duration = 180000,
            path = "/music/song.FLAC",
            size = 5000000,
            dateModified = System.currentTimeMillis()
        )
        
        assertEquals("flac", flacSong.getFileExtension())
    }
    
    @Test
    fun testIsValidAudioFile() {
        val validFormats = listOf("mp3", "flac", "ogg", "m4a", "aac", "wav", "wma")
        val invalidFormats = listOf("txt", "jpg", "pdf", "doc")
        
        validFormats.forEach { format ->
            val song = Song(
                id = 1,
                title = "Song",
                artist = "Artist",
                album = "Album",
                duration = 180000,
                path = "/music/song.$format",
                size = 5000000,
                dateModified = System.currentTimeMillis()
            )
            assertTrue("$format should be valid", song.isValidAudioFile())
        }
        
        invalidFormats.forEach { format ->
            val song = Song(
                id = 1,
                title = "Song",
                artist = "Artist",
                album = "Album",
                duration = 180000,
                path = "/music/file.$format",
                size = 5000000,
                dateModified = System.currentTimeMillis()
            )
            assertFalse("$format should be invalid", song.isValidAudioFile())
        }
    }
}
