# Minimal Music Player

A minimalist native Android music app with local audio playback, directory selection, and home screen widget support.

## Features

### Core Features
- **Local Audio Playback**: Browse and play local audio files from user-specified directories
- **Directory Selection**: Choose which folders to scan for music files with persistent storage
- **Basic Playback Controls**: Play, pause, skip forward/backward functionality
- **File Format Support**: Supports MP3, FLAC, OGG, M4A, AAC, WAV, WMA formats

### Widget Features
- **2x2 Home Screen Widget**: Compact widget for Android home screen
- **Widget Controls**: Display current song info with basic playback controls
- **Real-time Updates**: Widget updates automatically to reflect current playing state

### Technical Features
- Native Android development with Kotlin
- Target API level 21+ (Android 5.0+)
- Material Design UI with minimalist approach
- Proper media session handling for system integration
- ExoPlayer for robust audio playback
- Background service for continuous music playback
- Comprehensive error handling and logging

## Architecture

### Components
- **MainActivity**: Main UI with song list and playback controls
- **DirectorySelectionActivity**: Interface for selecting music directories
- **MusicService**: Background service handling audio playback
- **MusicWidgetProvider**: Home screen widget implementation
- **FileScanner**: Utility for scanning directories and extracting metadata
- **ErrorHandler**: Centralized error handling and logging

### Data Models
- **Song**: Core data model with metadata and utility methods
- **PreferenceManager**: Persistent storage for user preferences
- **PermissionHelper**: Runtime permission management

## Setup and Installation

### Prerequisites
- Android Studio Arctic Fox or later
- Android SDK with API level 21+
- Kotlin support enabled

### Building the Project
1. Clone or download the project
2. Open in Android Studio
3. Sync Gradle files
4. Build and run on device or emulator

### Permissions Required
- `READ_EXTERNAL_STORAGE` (API < 33)
- `READ_MEDIA_AUDIO` (API 33+)
- `WAKE_LOCK` for background playback
- `FOREGROUND_SERVICE` for music service
- `BIND_APPWIDGET` for widget functionality

## Usage

### First Launch
1. Grant storage permissions when prompted
2. Use the folder icon in the toolbar to select music directories
3. Tap "Scan All Directories" to index your music files
4. Songs will appear in the main list

### Playing Music
- Tap any song to start playback
- Use the mini-player controls at the bottom
- Current song is highlighted in the list
- Playback continues in background

### Adding Widget
1. Long-press on home screen
2. Select "Widgets"
3. Find "Minimal Music Player"
4. Drag the 2x2 widget to desired location
5. Widget will show current song and provide playback controls

## File Structure

```
app/src/main/java/com/minimalmusicplayer/
├── MainActivity.kt                 # Main activity
├── DirectorySelectionActivity.kt   # Directory selection UI
├── adapter/
│   ├── SongAdapter.kt             # RecyclerView adapter for songs
│   └── DirectoryAdapter.kt        # RecyclerView adapter for directories
├── model/
│   └── Song.kt                    # Song data model
├── service/
│   └── MusicService.kt            # Background music service
├── util/
│   ├── FileScanner.kt             # Directory scanning utility
│   ├── PermissionHelper.kt        # Permission management
│   ├── PreferenceManager.kt       # Settings storage
│   └── ErrorHandler.kt            # Error handling
└── widget/
    └── MusicWidgetProvider.kt     # Home screen widget
```

## Error Handling

The app includes comprehensive error handling for:
- File access permissions
- Corrupted or unsupported audio files
- Network-related issues
- Service binding failures
- Widget update errors

All errors are logged and user-friendly messages are displayed when appropriate.

## Testing

### Unit Tests
- Song model validation
- Utility function testing
- Data transformation tests

### Instrumentation Tests
- UI component testing
- Activity lifecycle testing
- Permission flow testing

Run tests with:
```bash
./gradlew test                    # Unit tests
./gradlew connectedAndroidTest    # Instrumentation tests
```

## Supported Audio Formats

- **MP3**: Most common format, widely supported
- **FLAC**: Lossless compression, high quality
- **OGG**: Open source alternative to MP3
- **M4A/AAC**: Apple's audio format
- **WAV**: Uncompressed audio
- **WMA**: Windows Media Audio

## Performance Considerations

- Efficient directory scanning with coroutines
- Lazy loading of song metadata
- Background service for uninterrupted playback
- Minimal memory footprint
- Optimized RecyclerView with ViewBinding

## Future Enhancements

Potential features for future versions:
- Playlist creation and management
- Equalizer integration
- Album art display
- Search functionality
- Shuffle and repeat modes
- Sleep timer
- Crossfade between tracks

## License

This project is created as a demonstration of Android music player development. 
Use and modify as needed for your projects.

## Contributing

This is a complete implementation example. Feel free to:
- Report issues or bugs
- Suggest improvements
- Fork and enhance the codebase
- Use as a learning resource

## Support

For questions or issues:
1. Check the error logs in Android Studio
2. Verify permissions are granted
3. Ensure audio files are in supported formats
4. Test with different Android versions if needed
