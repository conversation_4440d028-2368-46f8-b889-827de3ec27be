package com.minimalmusicplayer.model

import android.os.Parcelable
import kotlinx.parcelize.Parcelize

@Parcelize
data class Song(
    val id: Long = 0,
    val title: String,
    val artist: String,
    val album: String,
    val duration: Long,
    val path: String,
    val size: Long,
    val dateModified: Long,
    val albumArt: String? = null
) : Parcelable {
    
    fun getDisplayTitle(): String {
        return if (title.isNotBlank()) title else {
            // Extract filename without extension as fallback
            path.substringAfterLast('/').substringBeforeLast('.')
        }
    }
    
    fun getDisplayArtist(): String {
        return if (artist.isNotBlank() && artist != "<unknown>") artist else "Unknown Artist"
    }
    
    fun getDisplayAlbum(): String {
        return if (album.isNotBlank() && album != "<unknown>") album else "Unknown Album"
    }
    
    fun getDurationString(): String {
        val minutes = duration / 1000 / 60
        val seconds = (duration / 1000) % 60
        return String.format("%d:%02d", minutes, seconds)
    }
    
    fun getFileExtension(): String {
        return path.substringAfterLast('.', "").lowercase()
    }
    
    fun isValidAudioFile(): Bo<PERSON>an {
        val supportedFormats = setOf("mp3", "flac", "ogg", "m4a", "aac", "wav", "wma")
        return supportedFormats.contains(getFileExtension())
    }
}
