<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:tools="http://schemas.android.com/tools">
    <!-- Base application theme. -->
    <style name="Theme.MinimalMusicPlayer" parent="Theme.Material3.DayNight">
        <!-- Primary brand color. -->
        <item name="colorPrimary">@color/spotify_green</item>
        <item name="colorOnPrimary">@color/spotify_white</item>

        <!-- Secondary brand color. -->
        <item name="colorSecondary">@color/spotify_green</item>
        <item name="colorOnSecondary">@color/spotify_white</item>

        <!-- Surface colors -->
        <item name="colorSurface">@color/spotify_black</item>
        <item name="colorOnSurface">@color/spotify_white</item>
        <item name="colorSurfaceVariant">@color/spotify_gray</item>
        <item name="colorOnSurfaceVariant">@color/spotify_light_gray</item>

        <!-- Background -->
        <item name="android:colorBackground">@color/spotify_dark_gray</item>
        <item name="colorOnBackground">@color/spotify_white</item>

        <!-- Status bar -->
        <item name="android:statusBarColor">@color/spotify_black</item>
        <item name="android:navigationBarColor">@color/spotify_black</item>

        <!-- Window -->
        <item name="android:windowLightStatusBar">false</item>
        <item name="android:windowLightNavigationBar">false</item>
    </style>
</resources>
