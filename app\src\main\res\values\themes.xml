<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:tools="http://schemas.android.com/tools">
    <!-- Base application theme. -->
    <style name="Theme.MinimalMusicPlayer" parent="Theme.Material3.DayNight">
        <!-- Primary brand color. -->
        <item name="colorPrimary">@color/music_primary</item>
        <item name="colorOnPrimary">@color/white</item>
        <item name="colorPrimaryContainer">@color/music_primary_container</item>
        <item name="colorOnPrimaryContainer">@color/music_on_primary_container</item>

        <!-- Secondary brand color. -->
        <item name="colorSecondary">@color/music_secondary</item>
        <item name="colorOnSecondary">@color/white</item>
        <item name="colorSecondaryContainer">@color/music_secondary_container</item>
        <item name="colorOnSecondaryContainer">@color/music_on_secondary_container</item>

        <!-- Surface colors -->
        <item name="colorSurface">@color/music_surface</item>
        <item name="colorOnSurface">@color/music_on_surface</item>
        <item name="colorSurfaceVariant">@color/music_surface_variant</item>
        <item name="colorOnSurfaceVariant">@color/music_on_surface_variant</item>

        <!-- Background -->
        <item name="android:colorBackground">@color/music_background</item>
        <item name="colorOnBackground">@color/music_on_background</item>

        <!-- Status bar -->
        <item name="android:statusBarColor">@color/music_primary</item>
        <item name="android:navigationBarColor">@color/music_surface</item>

        <!-- Window -->
        <item name="android:windowLightStatusBar">false</item>
        <item name="android:windowLightNavigationBar">true</item>
    </style>
</resources>
