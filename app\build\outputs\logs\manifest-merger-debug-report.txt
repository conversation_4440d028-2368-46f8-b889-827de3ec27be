-- Merging decision tree log ---
manifest
ADDED from C:\Users\<USER>\Desktop\musicC\app\src\main\AndroidManifest.xml:2:1-83:12
INJECTED from C:\Users\<USER>\Desktop\musicC\app\src\main\AndroidManifest.xml:2:1-83:12
INJECTED from C:\Users\<USER>\Desktop\musicC\app\src\main\AndroidManifest.xml:2:1-83:12
INJECTED from C:\Users\<USER>\Desktop\musicC\app\src\main\AndroidManifest.xml:2:1-83:12
MERGED from [androidx.databinding:viewbinding:8.1.0] E:\android_development_cache\.gradle\caches\transforms-3\0edf337fc3ec6c9f19b97b14ae87a7fa\transformed\viewbinding-8.1.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.material:material:1.9.0] E:\android_development_cache\.gradle\caches\transforms-3\1674ca777f183a0ca81338d078e0cd6f\transformed\material-1.9.0\AndroidManifest.xml:17:1-26:12
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] E:\android_development_cache\.gradle\caches\transforms-3\c8ef59029249929a4eb260446819e1eb\transformed\constraintlayout-2.1.4\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] E:\android_development_cache\.gradle\caches\transforms-3\b868a45f1cb4f34fd2a0b44eb158e1ad\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.preference:preference-ktx:1.2.1] E:\android_development_cache\.gradle\caches\transforms-3\b020e5477e327788b3597aa0340dd94a\transformed\preference-ktx-1.2.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.preference:preference:1.2.1] E:\android_development_cache\.gradle\caches\transforms-3\550895908bcd254b169aeba9b6074ec2\transformed\preference-1.2.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.appcompat:appcompat:1.6.1] E:\android_development_cache\.gradle\caches\transforms-3\b61f7d3f27c328116375b3d38dc2a4b7\transformed\appcompat-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.viewpager2:viewpager2:1.0.0] E:\android_development_cache\.gradle\caches\transforms-3\54d1147b334842ea9d35debc5d0818d3\transformed\viewpager2-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.fragment:fragment:1.6.0] E:\android_development_cache\.gradle\caches\transforms-3\a80080028180f64fa205dbe1ac58595b\transformed\fragment-1.6.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.fragment:fragment-ktx:1.6.0] E:\android_development_cache\.gradle\caches\transforms-3\d9165d658b5745365082eb72d2544072\transformed\fragment-ktx-1.6.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.activity:activity:1.7.2] E:\android_development_cache\.gradle\caches\transforms-3\297a9a8be5a3f0cba015a9c5b1ff7d5b\transformed\activity-1.7.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.activity:activity-ktx:1.7.2] E:\android_development_cache\.gradle\caches\transforms-3\580305acfe7d42ab3f367aeb32518093\transformed\activity-ktx-1.7.2\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.exoplayer:exoplayer:2.19.1] E:\android_development_cache\.gradle\caches\transforms-3\b77e4dd1e212f209b58f0ba5e67fd051\transformed\exoplayer-2.19.1\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-ui:2.19.1] E:\android_development_cache\.gradle\caches\transforms-3\44bdd8c9f94ff480fecdbcca682a189c\transformed\exoplayer-ui-2.19.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.recyclerview:recyclerview:1.3.0] E:\android_development_cache\.gradle\caches\transforms-3\1f0146ec2ae315a63a731c80e67908a3\transformed\recyclerview-1.3.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] E:\android_development_cache\.gradle\caches\transforms-3\e9129ec634a3824560a2a29049f6f59f\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] E:\android_development_cache\.gradle\caches\transforms-3\7289ece9072a54439c7ffc0a7c7ae627\transformed\emoji2-views-helper-1.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.2.0] E:\android_development_cache\.gradle\caches\transforms-3\1e3aff5aaafb1f3205851880ac1fd91b\transformed\emoji2-1.2.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.1] E:\android_development_cache\.gradle\caches\transforms-3\d0985bac13295ea1c43a688589def24c\transformed\lifecycle-livedata-core-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] E:\android_development_cache\.gradle\caches\transforms-3\8b4f3056707ba398b4b7b532a4229536\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] E:\android_development_cache\.gradle\caches\transforms-3\cb5935bd8d5a4b58e21eaba396088330\transformed\savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] E:\android_development_cache\.gradle\caches\transforms-3\d4bf48403fc213aec52ba971f9bad5bd\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.6.1] E:\android_development_cache\.gradle\caches\transforms-3\5df500ce6072c8d5640324fc8f80a047\transformed\lifecycle-runtime-ktx-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] E:\android_development_cache\.gradle\caches\transforms-3\742f327de25e211d5566309ac5f601ce\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] E:\android_development_cache\.gradle\caches\transforms-3\89175d84dc0a3c1be5be88e102a751f7\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] E:\android_development_cache\.gradle\caches\transforms-3\ad122f509755bd5b19d6a4ff3c35294c\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.1] E:\android_development_cache\.gradle\caches\transforms-3\7014ff4358c05084125ed7f3a2c62400\transformed\lifecycle-livedata-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.6.1] E:\android_development_cache\.gradle\caches\transforms-3\07e0256a07b6f0714dfab5524800f1b3\transformed\lifecycle-livedata-core-ktx-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.1] E:\android_development_cache\.gradle\caches\transforms-3\a519aa53cc5a54db68fd4eef3066d1d0\transformed\lifecycle-viewmodel-savedstate-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core-ktx:1.10.1] E:\android_development_cache\.gradle\caches\transforms-3\9cf84af5e60d4f3c5688e98c7ddd5cce\transformed\core-ktx-1.10.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.media:media:1.6.0] E:\android_development_cache\.gradle\caches\transforms-3\75cbb85482a7b0f8683d438e7fda04c0\transformed\media-1.6.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] E:\android_development_cache\.gradle\caches\transforms-3\ef5a8f2e4efaabb13198b6471241143d\transformed\drawerlayout-1.1.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] E:\android_development_cache\.gradle\caches\transforms-3\92f1e3fd5723842a926da63b692ad7d6\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] E:\android_development_cache\.gradle\caches\transforms-3\3a27799b72b56434ba9c59535eb88add\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] E:\android_development_cache\.gradle\caches\transforms-3\a3fa1b437e9b8111bb2221e74a892079\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-dash:2.19.1] E:\android_development_cache\.gradle\caches\transforms-3\0488ac98b46cfa7cdb7b1988c2f3cf75\transformed\exoplayer-dash-2.19.1\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-hls:2.19.1] E:\android_development_cache\.gradle\caches\transforms-3\4e00f8bc03ad9c351091e06cbbb0537c\transformed\exoplayer-hls-2.19.1\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-rtsp:2.19.1] E:\android_development_cache\.gradle\caches\transforms-3\d79483e08cacc43ba843ff22812179ac\transformed\exoplayer-rtsp-2.19.1\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-smoothstreaming:2.19.1] E:\android_development_cache\.gradle\caches\transforms-3\ac2d15b69ca5995997c822cb08ab469b\transformed\exoplayer-smoothstreaming-2.19.1\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-core:2.19.1] E:\android_development_cache\.gradle\caches\transforms-3\f6a56acf230789289efb8d310bea470b\transformed\exoplayer-core-2.19.1\AndroidManifest.xml:17:1-26:12
MERGED from [androidx.viewpager:viewpager:1.0.0] E:\android_development_cache\.gradle\caches\transforms-3\a6b411d466fa5bc4e1cdba45a8720a5a\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] E:\android_development_cache\.gradle\caches\transforms-3\a3b0831b0d52155b3f82f1fc9e5a9f53\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.customview:customview:1.1.0] E:\android_development_cache\.gradle\caches\transforms-3\5e33f653b0ffd44898777a12dddc6037\transformed\customview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.transition:transition:1.4.1] E:\android_development_cache\.gradle\caches\transforms-3\64183a724b5fa46bffcb469cd9981a1a\transformed\transition-1.4.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.window:window:1.0.0] E:\android_development_cache\.gradle\caches\transforms-3\2d3a06f14c6f275b0094d4668de90ecb\transformed\window-1.0.0\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.core:core:1.10.1] E:\android_development_cache\.gradle\caches\transforms-3\d001c43b87f49a29f46bbdd20bd4ce3a\transformed\core-1.10.1\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.1] E:\android_development_cache\.gradle\caches\transforms-3\a5161a22e8b0c9085ff0451ae1ac0ff0\transformed\lifecycle-runtime-2.6.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.1] E:\android_development_cache\.gradle\caches\transforms-3\65ac82e6d474f37c10e337b919402baa\transformed\lifecycle-viewmodel-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-ktx:2.6.1] E:\android_development_cache\.gradle\caches\transforms-3\e33b50b0a00cc550932fe7f1405fb04b\transformed\lifecycle-livedata-ktx-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.1] E:\android_development_cache\.gradle\caches\transforms-3\091a317d5186d0b5c78479fbae6290d2\transformed\lifecycle-viewmodel-ktx-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] E:\android_development_cache\.gradle\caches\transforms-3\0af589a6a526c74d1c1522107befa980\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cardview:cardview:1.0.0] E:\android_development_cache\.gradle\caches\transforms-3\512f0a198db1701c9340e2c5bd5633de\transformed\cardview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.exoplayer:exoplayer-datasource:2.19.1] E:\android_development_cache\.gradle\caches\transforms-3\8a5205b3cc6895e4dc09ce886a538cd7\transformed\exoplayer-datasource-2.19.1\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-database:2.19.1] E:\android_development_cache\.gradle\caches\transforms-3\6f2b5fc4effd9cdacc912741d5a1c21d\transformed\exoplayer-database-2.19.1\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-extractor:2.19.1] E:\android_development_cache\.gradle\caches\transforms-3\ffb363c4939028601d1c403737f109e5\transformed\exoplayer-extractor-2.19.1\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-decoder:2.19.1] E:\android_development_cache\.gradle\caches\transforms-3\8ff2571008664fee1a3cfc13df7a1dda\transformed\exoplayer-decoder-2.19.1\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-container:2.19.1] E:\android_development_cache\.gradle\caches\transforms-3\e1525ba4821871d75012ae0c11f4e9d5\transformed\exoplayer-container-2.19.1\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-common:2.19.1] E:\android_development_cache\.gradle\caches\transforms-3\1ffadba9633f93500dbc5aa7a7a5588f\transformed\exoplayer-common-2.19.1\AndroidManifest.xml:17:1-26:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] E:\android_development_cache\.gradle\caches\transforms-3\a78bb0dce35b64f05c94f1ddf9989bdb\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.interpolator:interpolator:1.0.0] E:\android_development_cache\.gradle\caches\transforms-3\4a6905b96b27c384f72e6aa870d4c8cc\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] E:\android_development_cache\.gradle\caches\transforms-3\fdcf7198ce795448479892e9de9cef70\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] E:\android_development_cache\.gradle\caches\transforms-3\0a451f34059334e77a169e82d985bb5f\transformed\profileinstaller-1.3.0\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] E:\android_development_cache\.gradle\caches\transforms-3\170a7390bc51a31d71767465835a3424\transformed\startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.0.0] E:\android_development_cache\.gradle\caches\transforms-3\a4c58de877dd5a47bb5b1f395cd4c5db\transformed\tracing-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.documentfile:documentfile:1.0.0] E:\android_development_cache\.gradle\caches\transforms-3\84768ce3bf55ca731ee844c7540692a5\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] E:\android_development_cache\.gradle\caches\transforms-3\cd300bf11df9db2281fa0f7b5d87327b\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] E:\android_development_cache\.gradle\caches\transforms-3\6eea7d4305c2ee7699337db4da02166f\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.annotation:annotation-experimental:1.3.0] E:\android_development_cache\.gradle\caches\transforms-3\482253b582f2ea20699a03e3220c2e25\transformed\annotation-experimental-1.3.0\AndroidManifest.xml:17:1-22:12
	package
		INJECTED from C:\Users\<USER>\Desktop\musicC\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from C:\Users\<USER>\Desktop\musicC\app\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from C:\Users\<USER>\Desktop\musicC\app\src\main\AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from C:\Users\<USER>\Desktop\musicC\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from C:\Users\<USER>\Desktop\musicC\app\src\main\AndroidManifest.xml:2:11-69
uses-permission#android.permission.READ_EXTERNAL_STORAGE
ADDED from C:\Users\<USER>\Desktop\musicC\app\src\main\AndroidManifest.xml:6:5-7:38
	android:maxSdkVersion
		ADDED from C:\Users\<USER>\Desktop\musicC\app\src\main\AndroidManifest.xml:7:9-35
	android:name
		ADDED from C:\Users\<USER>\Desktop\musicC\app\src\main\AndroidManifest.xml:6:22-77
uses-permission#android.permission.READ_MEDIA_AUDIO
ADDED from C:\Users\<USER>\Desktop\musicC\app\src\main\AndroidManifest.xml:8:5-75
	android:name
		ADDED from C:\Users\<USER>\Desktop\musicC\app\src\main\AndroidManifest.xml:8:22-72
uses-permission#android.permission.WAKE_LOCK
ADDED from C:\Users\<USER>\Desktop\musicC\app\src\main\AndroidManifest.xml:9:5-68
	android:name
		ADDED from C:\Users\<USER>\Desktop\musicC\app\src\main\AndroidManifest.xml:9:22-65
uses-permission#android.permission.FOREGROUND_SERVICE
ADDED from C:\Users\<USER>\Desktop\musicC\app\src\main\AndroidManifest.xml:10:5-77
	android:name
		ADDED from C:\Users\<USER>\Desktop\musicC\app\src\main\AndroidManifest.xml:10:22-74
uses-permission#android.permission.FOREGROUND_SERVICE_MEDIA_PLAYBACK
ADDED from C:\Users\<USER>\Desktop\musicC\app\src\main\AndroidManifest.xml:11:5-92
	android:name
		ADDED from C:\Users\<USER>\Desktop\musicC\app\src\main\AndroidManifest.xml:11:22-89
uses-permission#android.permission.BIND_APPWIDGET
ADDED from C:\Users\<USER>\Desktop\musicC\app\src\main\AndroidManifest.xml:14:5-73
	android:name
		ADDED from C:\Users\<USER>\Desktop\musicC\app\src\main\AndroidManifest.xml:14:22-70
uses-permission#android.permission.MEDIA_CONTENT_CONTROL
ADDED from C:\Users\<USER>\Desktop\musicC\app\src\main\AndroidManifest.xml:17:5-80
	android:name
		ADDED from C:\Users\<USER>\Desktop\musicC\app\src\main\AndroidManifest.xml:17:22-77
application
ADDED from C:\Users\<USER>\Desktop\musicC\app\src\main\AndroidManifest.xml:19:5-81:19
INJECTED from C:\Users\<USER>\Desktop\musicC\app\src\main\AndroidManifest.xml:19:5-81:19
MERGED from [com.google.android.material:material:1.9.0] E:\android_development_cache\.gradle\caches\transforms-3\1674ca777f183a0ca81338d078e0cd6f\transformed\material-1.9.0\AndroidManifest.xml:24:5-20
MERGED from [com.google.android.material:material:1.9.0] E:\android_development_cache\.gradle\caches\transforms-3\1674ca777f183a0ca81338d078e0cd6f\transformed\material-1.9.0\AndroidManifest.xml:24:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] E:\android_development_cache\.gradle\caches\transforms-3\c8ef59029249929a4eb260446819e1eb\transformed\constraintlayout-2.1.4\AndroidManifest.xml:9:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] E:\android_development_cache\.gradle\caches\transforms-3\c8ef59029249929a4eb260446819e1eb\transformed\constraintlayout-2.1.4\AndroidManifest.xml:9:5-20
MERGED from [androidx.emoji2:emoji2:1.2.0] E:\android_development_cache\.gradle\caches\transforms-3\1e3aff5aaafb1f3205851880ac1fd91b\transformed\emoji2-1.2.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.2.0] E:\android_development_cache\.gradle\caches\transforms-3\1e3aff5aaafb1f3205851880ac1fd91b\transformed\emoji2-1.2.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] E:\android_development_cache\.gradle\caches\transforms-3\d4bf48403fc213aec52ba971f9bad5bd\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] E:\android_development_cache\.gradle\caches\transforms-3\d4bf48403fc213aec52ba971f9bad5bd\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.window:window:1.0.0] E:\android_development_cache\.gradle\caches\transforms-3\2d3a06f14c6f275b0094d4668de90ecb\transformed\window-1.0.0\AndroidManifest.xml:24:5-31:19
MERGED from [androidx.window:window:1.0.0] E:\android_development_cache\.gradle\caches\transforms-3\2d3a06f14c6f275b0094d4668de90ecb\transformed\window-1.0.0\AndroidManifest.xml:24:5-31:19
MERGED from [androidx.core:core:1.10.1] E:\android_development_cache\.gradle\caches\transforms-3\d001c43b87f49a29f46bbdd20bd4ce3a\transformed\core-1.10.1\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.10.1] E:\android_development_cache\.gradle\caches\transforms-3\d001c43b87f49a29f46bbdd20bd4ce3a\transformed\core-1.10.1\AndroidManifest.xml:28:5-89
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] E:\android_development_cache\.gradle\caches\transforms-3\a78bb0dce35b64f05c94f1ddf9989bdb\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] E:\android_development_cache\.gradle\caches\transforms-3\a78bb0dce35b64f05c94f1ddf9989bdb\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] E:\android_development_cache\.gradle\caches\transforms-3\0a451f34059334e77a169e82d985bb5f\transformed\profileinstaller-1.3.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] E:\android_development_cache\.gradle\caches\transforms-3\0a451f34059334e77a169e82d985bb5f\transformed\profileinstaller-1.3.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] E:\android_development_cache\.gradle\caches\transforms-3\170a7390bc51a31d71767465835a3424\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] E:\android_development_cache\.gradle\caches\transforms-3\170a7390bc51a31d71767465835a3424\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
	android:extractNativeLibs
		INJECTED from C:\Users\<USER>\Desktop\musicC\app\src\main\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.10.1] E:\android_development_cache\.gradle\caches\transforms-3\d001c43b87f49a29f46bbdd20bd4ce3a\transformed\core-1.10.1\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from C:\Users\<USER>\Desktop\musicC\app\src\main\AndroidManifest.xml:26:9-35
	android:label
		ADDED from C:\Users\<USER>\Desktop\musicC\app\src\main\AndroidManifest.xml:24:9-41
	android:fullBackupContent
		ADDED from C:\Users\<USER>\Desktop\musicC\app\src\main\AndroidManifest.xml:22:9-54
	android:roundIcon
		ADDED from C:\Users\<USER>\Desktop\musicC\app\src\main\AndroidManifest.xml:25:9-54
	tools:targetApi
		ADDED from C:\Users\<USER>\Desktop\musicC\app\src\main\AndroidManifest.xml:28:9-29
	android:icon
		ADDED from C:\Users\<USER>\Desktop\musicC\app\src\main\AndroidManifest.xml:23:9-43
	android:allowBackup
		ADDED from C:\Users\<USER>\Desktop\musicC\app\src\main\AndroidManifest.xml:20:9-35
	android:theme
		ADDED from C:\Users\<USER>\Desktop\musicC\app\src\main\AndroidManifest.xml:27:9-56
	android:dataExtractionRules
		ADDED from C:\Users\<USER>\Desktop\musicC\app\src\main\AndroidManifest.xml:21:9-65
activity#com.minimalmusicplayer.SimpleMainActivity
ADDED from C:\Users\<USER>\Desktop\musicC\app\src\main\AndroidManifest.xml:31:9-39:20
	android:exported
		ADDED from C:\Users\<USER>\Desktop\musicC\app\src\main\AndroidManifest.xml:33:13-36
	android:theme
		ADDED from C:\Users\<USER>\Desktop\musicC\app\src\main\AndroidManifest.xml:34:13-60
	android:name
		ADDED from C:\Users\<USER>\Desktop\musicC\app\src\main\AndroidManifest.xml:32:13-47
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\Desktop\musicC\app\src\main\AndroidManifest.xml:35:13-38:29
action#android.intent.action.MAIN
ADDED from C:\Users\<USER>\Desktop\musicC\app\src\main\AndroidManifest.xml:36:17-69
	android:name
		ADDED from C:\Users\<USER>\Desktop\musicC\app\src\main\AndroidManifest.xml:36:25-66
category#android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\Desktop\musicC\app\src\main\AndroidManifest.xml:37:17-77
	android:name
		ADDED from C:\Users\<USER>\Desktop\musicC\app\src\main\AndroidManifest.xml:37:27-74
activity#com.minimalmusicplayer.MainActivity
ADDED from C:\Users\<USER>\Desktop\musicC\app\src\main\AndroidManifest.xml:42:9-45:63
	android:exported
		ADDED from C:\Users\<USER>\Desktop\musicC\app\src\main\AndroidManifest.xml:44:13-37
	android:theme
		ADDED from C:\Users\<USER>\Desktop\musicC\app\src\main\AndroidManifest.xml:45:13-60
	android:name
		ADDED from C:\Users\<USER>\Desktop\musicC\app\src\main\AndroidManifest.xml:43:13-41
activity#com.minimalmusicplayer.DirectorySelectionActivity
ADDED from C:\Users\<USER>\Desktop\musicC\app\src\main\AndroidManifest.xml:48:9-51:63
	android:exported
		ADDED from C:\Users\<USER>\Desktop\musicC\app\src\main\AndroidManifest.xml:50:13-37
	android:theme
		ADDED from C:\Users\<USER>\Desktop\musicC\app\src\main\AndroidManifest.xml:51:13-60
	android:name
		ADDED from C:\Users\<USER>\Desktop\musicC\app\src\main\AndroidManifest.xml:49:13-55
service#com.minimalmusicplayer.service.MusicService
ADDED from C:\Users\<USER>\Desktop\musicC\app\src\main\AndroidManifest.xml:54:9-58:61
	android:enabled
		ADDED from C:\Users\<USER>\Desktop\musicC\app\src\main\AndroidManifest.xml:56:13-35
	android:exported
		ADDED from C:\Users\<USER>\Desktop\musicC\app\src\main\AndroidManifest.xml:57:13-37
	android:foregroundServiceType
		ADDED from C:\Users\<USER>\Desktop\musicC\app\src\main\AndroidManifest.xml:58:13-58
	android:name
		ADDED from C:\Users\<USER>\Desktop\musicC\app\src\main\AndroidManifest.xml:55:13-49
receiver#com.minimalmusicplayer.widget.MusicWidgetProvider
ADDED from C:\Users\<USER>\Desktop\musicC\app\src\main\AndroidManifest.xml:61:9-70:20
	android:exported
		ADDED from C:\Users\<USER>\Desktop\musicC\app\src\main\AndroidManifest.xml:63:13-36
	android:name
		ADDED from C:\Users\<USER>\Desktop\musicC\app\src\main\AndroidManifest.xml:62:13-55
intent-filter#action:name:android.appwidget.action.APPWIDGET_UPDATE
ADDED from C:\Users\<USER>\Desktop\musicC\app\src\main\AndroidManifest.xml:64:13-66:29
action#android.appwidget.action.APPWIDGET_UPDATE
ADDED from C:\Users\<USER>\Desktop\musicC\app\src\main\AndroidManifest.xml:65:17-84
	android:name
		ADDED from C:\Users\<USER>\Desktop\musicC\app\src\main\AndroidManifest.xml:65:25-81
meta-data#android.appwidget.provider
ADDED from C:\Users\<USER>\Desktop\musicC\app\src\main\AndroidManifest.xml:67:13-69:61
	android:resource
		ADDED from C:\Users\<USER>\Desktop\musicC\app\src\main\AndroidManifest.xml:69:17-58
	android:name
		ADDED from C:\Users\<USER>\Desktop\musicC\app\src\main\AndroidManifest.xml:68:17-58
receiver#androidx.media.session.MediaButtonReceiver
ADDED from C:\Users\<USER>\Desktop\musicC\app\src\main\AndroidManifest.xml:73:9-79:20
	android:exported
		ADDED from C:\Users\<USER>\Desktop\musicC\app\src\main\AndroidManifest.xml:75:13-36
	android:name
		ADDED from C:\Users\<USER>\Desktop\musicC\app\src\main\AndroidManifest.xml:74:13-70
intent-filter#action:name:android.intent.action.MEDIA_BUTTON
ADDED from C:\Users\<USER>\Desktop\musicC\app\src\main\AndroidManifest.xml:76:13-78:29
action#android.intent.action.MEDIA_BUTTON
ADDED from C:\Users\<USER>\Desktop\musicC\app\src\main\AndroidManifest.xml:77:17-77
	android:name
		ADDED from C:\Users\<USER>\Desktop\musicC\app\src\main\AndroidManifest.xml:77:25-74
uses-sdk
INJECTED from C:\Users\<USER>\Desktop\musicC\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from C:\Users\<USER>\Desktop\musicC\app\src\main\AndroidManifest.xml
INJECTED from C:\Users\<USER>\Desktop\musicC\app\src\main\AndroidManifest.xml
MERGED from [androidx.databinding:viewbinding:8.1.0] E:\android_development_cache\.gradle\caches\transforms-3\0edf337fc3ec6c9f19b97b14ae87a7fa\transformed\viewbinding-8.1.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:viewbinding:8.1.0] E:\android_development_cache\.gradle\caches\transforms-3\0edf337fc3ec6c9f19b97b14ae87a7fa\transformed\viewbinding-8.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.material:material:1.9.0] E:\android_development_cache\.gradle\caches\transforms-3\1674ca777f183a0ca81338d078e0cd6f\transformed\material-1.9.0\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.material:material:1.9.0] E:\android_development_cache\.gradle\caches\transforms-3\1674ca777f183a0ca81338d078e0cd6f\transformed\material-1.9.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] E:\android_development_cache\.gradle\caches\transforms-3\c8ef59029249929a4eb260446819e1eb\transformed\constraintlayout-2.1.4\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] E:\android_development_cache\.gradle\caches\transforms-3\c8ef59029249929a4eb260446819e1eb\transformed\constraintlayout-2.1.4\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] E:\android_development_cache\.gradle\caches\transforms-3\b868a45f1cb4f34fd2a0b44eb158e1ad\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] E:\android_development_cache\.gradle\caches\transforms-3\b868a45f1cb4f34fd2a0b44eb158e1ad\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.preference:preference-ktx:1.2.1] E:\android_development_cache\.gradle\caches\transforms-3\b020e5477e327788b3597aa0340dd94a\transformed\preference-ktx-1.2.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.preference:preference-ktx:1.2.1] E:\android_development_cache\.gradle\caches\transforms-3\b020e5477e327788b3597aa0340dd94a\transformed\preference-ktx-1.2.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.preference:preference:1.2.1] E:\android_development_cache\.gradle\caches\transforms-3\550895908bcd254b169aeba9b6074ec2\transformed\preference-1.2.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.preference:preference:1.2.1] E:\android_development_cache\.gradle\caches\transforms-3\550895908bcd254b169aeba9b6074ec2\transformed\preference-1.2.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.appcompat:appcompat:1.6.1] E:\android_development_cache\.gradle\caches\transforms-3\b61f7d3f27c328116375b3d38dc2a4b7\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] E:\android_development_cache\.gradle\caches\transforms-3\b61f7d3f27c328116375b3d38dc2a4b7\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager2:viewpager2:1.0.0] E:\android_development_cache\.gradle\caches\transforms-3\54d1147b334842ea9d35debc5d0818d3\transformed\viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager2:viewpager2:1.0.0] E:\android_development_cache\.gradle\caches\transforms-3\54d1147b334842ea9d35debc5d0818d3\transformed\viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.6.0] E:\android_development_cache\.gradle\caches\transforms-3\a80080028180f64fa205dbe1ac58595b\transformed\fragment-1.6.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment:1.6.0] E:\android_development_cache\.gradle\caches\transforms-3\a80080028180f64fa205dbe1ac58595b\transformed\fragment-1.6.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment-ktx:1.6.0] E:\android_development_cache\.gradle\caches\transforms-3\d9165d658b5745365082eb72d2544072\transformed\fragment-ktx-1.6.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment-ktx:1.6.0] E:\android_development_cache\.gradle\caches\transforms-3\d9165d658b5745365082eb72d2544072\transformed\fragment-ktx-1.6.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.7.2] E:\android_development_cache\.gradle\caches\transforms-3\297a9a8be5a3f0cba015a9c5b1ff7d5b\transformed\activity-1.7.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.7.2] E:\android_development_cache\.gradle\caches\transforms-3\297a9a8be5a3f0cba015a9c5b1ff7d5b\transformed\activity-1.7.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity-ktx:1.7.2] E:\android_development_cache\.gradle\caches\transforms-3\580305acfe7d42ab3f367aeb32518093\transformed\activity-ktx-1.7.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity-ktx:1.7.2] E:\android_development_cache\.gradle\caches\transforms-3\580305acfe7d42ab3f367aeb32518093\transformed\activity-ktx-1.7.2\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.exoplayer:exoplayer:2.19.1] E:\android_development_cache\.gradle\caches\transforms-3\b77e4dd1e212f209b58f0ba5e67fd051\transformed\exoplayer-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer:2.19.1] E:\android_development_cache\.gradle\caches\transforms-3\b77e4dd1e212f209b58f0ba5e67fd051\transformed\exoplayer-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-ui:2.19.1] E:\android_development_cache\.gradle\caches\transforms-3\44bdd8c9f94ff480fecdbcca682a189c\transformed\exoplayer-ui-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-ui:2.19.1] E:\android_development_cache\.gradle\caches\transforms-3\44bdd8c9f94ff480fecdbcca682a189c\transformed\exoplayer-ui-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.recyclerview:recyclerview:1.3.0] E:\android_development_cache\.gradle\caches\transforms-3\1f0146ec2ae315a63a731c80e67908a3\transformed\recyclerview-1.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.3.0] E:\android_development_cache\.gradle\caches\transforms-3\1f0146ec2ae315a63a731c80e67908a3\transformed\recyclerview-1.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] E:\android_development_cache\.gradle\caches\transforms-3\e9129ec634a3824560a2a29049f6f59f\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] E:\android_development_cache\.gradle\caches\transforms-3\e9129ec634a3824560a2a29049f6f59f\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] E:\android_development_cache\.gradle\caches\transforms-3\7289ece9072a54439c7ffc0a7c7ae627\transformed\emoji2-views-helper-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] E:\android_development_cache\.gradle\caches\transforms-3\7289ece9072a54439c7ffc0a7c7ae627\transformed\emoji2-views-helper-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.2.0] E:\android_development_cache\.gradle\caches\transforms-3\1e3aff5aaafb1f3205851880ac1fd91b\transformed\emoji2-1.2.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.2.0] E:\android_development_cache\.gradle\caches\transforms-3\1e3aff5aaafb1f3205851880ac1fd91b\transformed\emoji2-1.2.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.1] E:\android_development_cache\.gradle\caches\transforms-3\d0985bac13295ea1c43a688589def24c\transformed\lifecycle-livedata-core-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.1] E:\android_development_cache\.gradle\caches\transforms-3\d0985bac13295ea1c43a688589def24c\transformed\lifecycle-livedata-core-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] E:\android_development_cache\.gradle\caches\transforms-3\8b4f3056707ba398b4b7b532a4229536\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] E:\android_development_cache\.gradle\caches\transforms-3\8b4f3056707ba398b4b7b532a4229536\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] E:\android_development_cache\.gradle\caches\transforms-3\cb5935bd8d5a4b58e21eaba396088330\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] E:\android_development_cache\.gradle\caches\transforms-3\cb5935bd8d5a4b58e21eaba396088330\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] E:\android_development_cache\.gradle\caches\transforms-3\d4bf48403fc213aec52ba971f9bad5bd\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] E:\android_development_cache\.gradle\caches\transforms-3\d4bf48403fc213aec52ba971f9bad5bd\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.6.1] E:\android_development_cache\.gradle\caches\transforms-3\5df500ce6072c8d5640324fc8f80a047\transformed\lifecycle-runtime-ktx-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.6.1] E:\android_development_cache\.gradle\caches\transforms-3\5df500ce6072c8d5640324fc8f80a047\transformed\lifecycle-runtime-ktx-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] E:\android_development_cache\.gradle\caches\transforms-3\742f327de25e211d5566309ac5f601ce\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] E:\android_development_cache\.gradle\caches\transforms-3\742f327de25e211d5566309ac5f601ce\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] E:\android_development_cache\.gradle\caches\transforms-3\89175d84dc0a3c1be5be88e102a751f7\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] E:\android_development_cache\.gradle\caches\transforms-3\89175d84dc0a3c1be5be88e102a751f7\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] E:\android_development_cache\.gradle\caches\transforms-3\ad122f509755bd5b19d6a4ff3c35294c\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] E:\android_development_cache\.gradle\caches\transforms-3\ad122f509755bd5b19d6a4ff3c35294c\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.1] E:\android_development_cache\.gradle\caches\transforms-3\7014ff4358c05084125ed7f3a2c62400\transformed\lifecycle-livedata-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.1] E:\android_development_cache\.gradle\caches\transforms-3\7014ff4358c05084125ed7f3a2c62400\transformed\lifecycle-livedata-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.6.1] E:\android_development_cache\.gradle\caches\transforms-3\07e0256a07b6f0714dfab5524800f1b3\transformed\lifecycle-livedata-core-ktx-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.6.1] E:\android_development_cache\.gradle\caches\transforms-3\07e0256a07b6f0714dfab5524800f1b3\transformed\lifecycle-livedata-core-ktx-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.1] E:\android_development_cache\.gradle\caches\transforms-3\a519aa53cc5a54db68fd4eef3066d1d0\transformed\lifecycle-viewmodel-savedstate-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.1] E:\android_development_cache\.gradle\caches\transforms-3\a519aa53cc5a54db68fd4eef3066d1d0\transformed\lifecycle-viewmodel-savedstate-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core-ktx:1.10.1] E:\android_development_cache\.gradle\caches\transforms-3\9cf84af5e60d4f3c5688e98c7ddd5cce\transformed\core-ktx-1.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.10.1] E:\android_development_cache\.gradle\caches\transforms-3\9cf84af5e60d4f3c5688e98c7ddd5cce\transformed\core-ktx-1.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.media:media:1.6.0] E:\android_development_cache\.gradle\caches\transforms-3\75cbb85482a7b0f8683d438e7fda04c0\transformed\media-1.6.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.media:media:1.6.0] E:\android_development_cache\.gradle\caches\transforms-3\75cbb85482a7b0f8683d438e7fda04c0\transformed\media-1.6.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] E:\android_development_cache\.gradle\caches\transforms-3\ef5a8f2e4efaabb13198b6471241143d\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] E:\android_development_cache\.gradle\caches\transforms-3\ef5a8f2e4efaabb13198b6471241143d\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] E:\android_development_cache\.gradle\caches\transforms-3\92f1e3fd5723842a926da63b692ad7d6\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] E:\android_development_cache\.gradle\caches\transforms-3\92f1e3fd5723842a926da63b692ad7d6\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] E:\android_development_cache\.gradle\caches\transforms-3\3a27799b72b56434ba9c59535eb88add\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] E:\android_development_cache\.gradle\caches\transforms-3\3a27799b72b56434ba9c59535eb88add\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] E:\android_development_cache\.gradle\caches\transforms-3\a3fa1b437e9b8111bb2221e74a892079\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] E:\android_development_cache\.gradle\caches\transforms-3\a3fa1b437e9b8111bb2221e74a892079\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-dash:2.19.1] E:\android_development_cache\.gradle\caches\transforms-3\0488ac98b46cfa7cdb7b1988c2f3cf75\transformed\exoplayer-dash-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-dash:2.19.1] E:\android_development_cache\.gradle\caches\transforms-3\0488ac98b46cfa7cdb7b1988c2f3cf75\transformed\exoplayer-dash-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-hls:2.19.1] E:\android_development_cache\.gradle\caches\transforms-3\4e00f8bc03ad9c351091e06cbbb0537c\transformed\exoplayer-hls-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-hls:2.19.1] E:\android_development_cache\.gradle\caches\transforms-3\4e00f8bc03ad9c351091e06cbbb0537c\transformed\exoplayer-hls-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-rtsp:2.19.1] E:\android_development_cache\.gradle\caches\transforms-3\d79483e08cacc43ba843ff22812179ac\transformed\exoplayer-rtsp-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-rtsp:2.19.1] E:\android_development_cache\.gradle\caches\transforms-3\d79483e08cacc43ba843ff22812179ac\transformed\exoplayer-rtsp-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-smoothstreaming:2.19.1] E:\android_development_cache\.gradle\caches\transforms-3\ac2d15b69ca5995997c822cb08ab469b\transformed\exoplayer-smoothstreaming-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-smoothstreaming:2.19.1] E:\android_development_cache\.gradle\caches\transforms-3\ac2d15b69ca5995997c822cb08ab469b\transformed\exoplayer-smoothstreaming-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-core:2.19.1] E:\android_development_cache\.gradle\caches\transforms-3\f6a56acf230789289efb8d310bea470b\transformed\exoplayer-core-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-core:2.19.1] E:\android_development_cache\.gradle\caches\transforms-3\f6a56acf230789289efb8d310bea470b\transformed\exoplayer-core-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager:viewpager:1.0.0] E:\android_development_cache\.gradle\caches\transforms-3\a6b411d466fa5bc4e1cdba45a8720a5a\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] E:\android_development_cache\.gradle\caches\transforms-3\a6b411d466fa5bc4e1cdba45a8720a5a\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] E:\android_development_cache\.gradle\caches\transforms-3\a3b0831b0d52155b3f82f1fc9e5a9f53\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] E:\android_development_cache\.gradle\caches\transforms-3\a3b0831b0d52155b3f82f1fc9e5a9f53\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] E:\android_development_cache\.gradle\caches\transforms-3\5e33f653b0ffd44898777a12dddc6037\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] E:\android_development_cache\.gradle\caches\transforms-3\5e33f653b0ffd44898777a12dddc6037\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.4.1] E:\android_development_cache\.gradle\caches\transforms-3\64183a724b5fa46bffcb469cd9981a1a\transformed\transition-1.4.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.4.1] E:\android_development_cache\.gradle\caches\transforms-3\64183a724b5fa46bffcb469cd9981a1a\transformed\transition-1.4.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.window:window:1.0.0] E:\android_development_cache\.gradle\caches\transforms-3\2d3a06f14c6f275b0094d4668de90ecb\transformed\window-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.window:window:1.0.0] E:\android_development_cache\.gradle\caches\transforms-3\2d3a06f14c6f275b0094d4668de90ecb\transformed\window-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core:1.10.1] E:\android_development_cache\.gradle\caches\transforms-3\d001c43b87f49a29f46bbdd20bd4ce3a\transformed\core-1.10.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.10.1] E:\android_development_cache\.gradle\caches\transforms-3\d001c43b87f49a29f46bbdd20bd4ce3a\transformed\core-1.10.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.1] E:\android_development_cache\.gradle\caches\transforms-3\a5161a22e8b0c9085ff0451ae1ac0ff0\transformed\lifecycle-runtime-2.6.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.1] E:\android_development_cache\.gradle\caches\transforms-3\a5161a22e8b0c9085ff0451ae1ac0ff0\transformed\lifecycle-runtime-2.6.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.1] E:\android_development_cache\.gradle\caches\transforms-3\65ac82e6d474f37c10e337b919402baa\transformed\lifecycle-viewmodel-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.1] E:\android_development_cache\.gradle\caches\transforms-3\65ac82e6d474f37c10e337b919402baa\transformed\lifecycle-viewmodel-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-ktx:2.6.1] E:\android_development_cache\.gradle\caches\transforms-3\e33b50b0a00cc550932fe7f1405fb04b\transformed\lifecycle-livedata-ktx-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-ktx:2.6.1] E:\android_development_cache\.gradle\caches\transforms-3\e33b50b0a00cc550932fe7f1405fb04b\transformed\lifecycle-livedata-ktx-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.1] E:\android_development_cache\.gradle\caches\transforms-3\091a317d5186d0b5c78479fbae6290d2\transformed\lifecycle-viewmodel-ktx-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.1] E:\android_development_cache\.gradle\caches\transforms-3\091a317d5186d0b5c78479fbae6290d2\transformed\lifecycle-viewmodel-ktx-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] E:\android_development_cache\.gradle\caches\transforms-3\0af589a6a526c74d1c1522107befa980\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] E:\android_development_cache\.gradle\caches\transforms-3\0af589a6a526c74d1c1522107befa980\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] E:\android_development_cache\.gradle\caches\transforms-3\512f0a198db1701c9340e2c5bd5633de\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] E:\android_development_cache\.gradle\caches\transforms-3\512f0a198db1701c9340e2c5bd5633de\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.exoplayer:exoplayer-datasource:2.19.1] E:\android_development_cache\.gradle\caches\transforms-3\8a5205b3cc6895e4dc09ce886a538cd7\transformed\exoplayer-datasource-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-datasource:2.19.1] E:\android_development_cache\.gradle\caches\transforms-3\8a5205b3cc6895e4dc09ce886a538cd7\transformed\exoplayer-datasource-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-database:2.19.1] E:\android_development_cache\.gradle\caches\transforms-3\6f2b5fc4effd9cdacc912741d5a1c21d\transformed\exoplayer-database-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-database:2.19.1] E:\android_development_cache\.gradle\caches\transforms-3\6f2b5fc4effd9cdacc912741d5a1c21d\transformed\exoplayer-database-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-extractor:2.19.1] E:\android_development_cache\.gradle\caches\transforms-3\ffb363c4939028601d1c403737f109e5\transformed\exoplayer-extractor-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-extractor:2.19.1] E:\android_development_cache\.gradle\caches\transforms-3\ffb363c4939028601d1c403737f109e5\transformed\exoplayer-extractor-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-decoder:2.19.1] E:\android_development_cache\.gradle\caches\transforms-3\8ff2571008664fee1a3cfc13df7a1dda\transformed\exoplayer-decoder-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-decoder:2.19.1] E:\android_development_cache\.gradle\caches\transforms-3\8ff2571008664fee1a3cfc13df7a1dda\transformed\exoplayer-decoder-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-container:2.19.1] E:\android_development_cache\.gradle\caches\transforms-3\e1525ba4821871d75012ae0c11f4e9d5\transformed\exoplayer-container-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-container:2.19.1] E:\android_development_cache\.gradle\caches\transforms-3\e1525ba4821871d75012ae0c11f4e9d5\transformed\exoplayer-container-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-common:2.19.1] E:\android_development_cache\.gradle\caches\transforms-3\1ffadba9633f93500dbc5aa7a7a5588f\transformed\exoplayer-common-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-common:2.19.1] E:\android_development_cache\.gradle\caches\transforms-3\1ffadba9633f93500dbc5aa7a7a5588f\transformed\exoplayer-common-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] E:\android_development_cache\.gradle\caches\transforms-3\a78bb0dce35b64f05c94f1ddf9989bdb\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] E:\android_development_cache\.gradle\caches\transforms-3\a78bb0dce35b64f05c94f1ddf9989bdb\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.interpolator:interpolator:1.0.0] E:\android_development_cache\.gradle\caches\transforms-3\4a6905b96b27c384f72e6aa870d4c8cc\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] E:\android_development_cache\.gradle\caches\transforms-3\4a6905b96b27c384f72e6aa870d4c8cc\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] E:\android_development_cache\.gradle\caches\transforms-3\fdcf7198ce795448479892e9de9cef70\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] E:\android_development_cache\.gradle\caches\transforms-3\fdcf7198ce795448479892e9de9cef70\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] E:\android_development_cache\.gradle\caches\transforms-3\0a451f34059334e77a169e82d985bb5f\transformed\profileinstaller-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] E:\android_development_cache\.gradle\caches\transforms-3\0a451f34059334e77a169e82d985bb5f\transformed\profileinstaller-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] E:\android_development_cache\.gradle\caches\transforms-3\170a7390bc51a31d71767465835a3424\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] E:\android_development_cache\.gradle\caches\transforms-3\170a7390bc51a31d71767465835a3424\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.0.0] E:\android_development_cache\.gradle\caches\transforms-3\a4c58de877dd5a47bb5b1f395cd4c5db\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing:1.0.0] E:\android_development_cache\.gradle\caches\transforms-3\a4c58de877dd5a47bb5b1f395cd4c5db\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.documentfile:documentfile:1.0.0] E:\android_development_cache\.gradle\caches\transforms-3\84768ce3bf55ca731ee844c7540692a5\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] E:\android_development_cache\.gradle\caches\transforms-3\84768ce3bf55ca731ee844c7540692a5\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] E:\android_development_cache\.gradle\caches\transforms-3\cd300bf11df9db2281fa0f7b5d87327b\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] E:\android_development_cache\.gradle\caches\transforms-3\cd300bf11df9db2281fa0f7b5d87327b\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] E:\android_development_cache\.gradle\caches\transforms-3\6eea7d4305c2ee7699337db4da02166f\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] E:\android_development_cache\.gradle\caches\transforms-3\6eea7d4305c2ee7699337db4da02166f\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.3.0] E:\android_development_cache\.gradle\caches\transforms-3\482253b582f2ea20699a03e3220c2e25\transformed\annotation-experimental-1.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.3.0] E:\android_development_cache\.gradle\caches\transforms-3\482253b582f2ea20699a03e3220c2e25\transformed\annotation-experimental-1.3.0\AndroidManifest.xml:20:5-44
	android:targetSdkVersion
		INJECTED from C:\Users\<USER>\Desktop\musicC\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from C:\Users\<USER>\Desktop\musicC\app\src\main\AndroidManifest.xml
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.2.0] E:\android_development_cache\.gradle\caches\transforms-3\1e3aff5aaafb1f3205851880ac1fd91b\transformed\emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] E:\android_development_cache\.gradle\caches\transforms-3\d4bf48403fc213aec52ba971f9bad5bd\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] E:\android_development_cache\.gradle\caches\transforms-3\d4bf48403fc213aec52ba971f9bad5bd\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] E:\android_development_cache\.gradle\caches\transforms-3\0a451f34059334e77a169e82d985bb5f\transformed\profileinstaller-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] E:\android_development_cache\.gradle\caches\transforms-3\0a451f34059334e77a169e82d985bb5f\transformed\profileinstaller-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] E:\android_development_cache\.gradle\caches\transforms-3\170a7390bc51a31d71767465835a3424\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] E:\android_development_cache\.gradle\caches\transforms-3\170a7390bc51a31d71767465835a3424\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.2.0] E:\android_development_cache\.gradle\caches\transforms-3\1e3aff5aaafb1f3205851880ac1fd91b\transformed\emoji2-1.2.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.2.0] E:\android_development_cache\.gradle\caches\transforms-3\1e3aff5aaafb1f3205851880ac1fd91b\transformed\emoji2-1.2.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.2.0] E:\android_development_cache\.gradle\caches\transforms-3\1e3aff5aaafb1f3205851880ac1fd91b\transformed\emoji2-1.2.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.2.0] E:\android_development_cache\.gradle\caches\transforms-3\1e3aff5aaafb1f3205851880ac1fd91b\transformed\emoji2-1.2.0\AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.2.0] E:\android_development_cache\.gradle\caches\transforms-3\1e3aff5aaafb1f3205851880ac1fd91b\transformed\emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.2.0] E:\android_development_cache\.gradle\caches\transforms-3\1e3aff5aaafb1f3205851880ac1fd91b\transformed\emoji2-1.2.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.2.0] E:\android_development_cache\.gradle\caches\transforms-3\1e3aff5aaafb1f3205851880ac1fd91b\transformed\emoji2-1.2.0\AndroidManifest.xml:30:17-75
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.6.1] E:\android_development_cache\.gradle\caches\transforms-3\d4bf48403fc213aec52ba971f9bad5bd\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.6.1] E:\android_development_cache\.gradle\caches\transforms-3\d4bf48403fc213aec52ba971f9bad5bd\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.6.1] E:\android_development_cache\.gradle\caches\transforms-3\d4bf48403fc213aec52ba971f9bad5bd\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:30:17-78
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from [com.google.android.exoplayer:exoplayer-core:2.19.1] E:\android_development_cache\.gradle\caches\transforms-3\f6a56acf230789289efb8d310bea470b\transformed\exoplayer-core-2.19.1\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.exoplayer:exoplayer-common:2.19.1] E:\android_development_cache\.gradle\caches\transforms-3\1ffadba9633f93500dbc5aa7a7a5588f\transformed\exoplayer-common-2.19.1\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.exoplayer:exoplayer-common:2.19.1] E:\android_development_cache\.gradle\caches\transforms-3\1ffadba9633f93500dbc5aa7a7a5588f\transformed\exoplayer-common-2.19.1\AndroidManifest.xml:24:5-79
	android:name
		ADDED from [com.google.android.exoplayer:exoplayer-core:2.19.1] E:\android_development_cache\.gradle\caches\transforms-3\f6a56acf230789289efb8d310bea470b\transformed\exoplayer-core-2.19.1\AndroidManifest.xml:24:22-76
uses-library#androidx.window.extensions
ADDED from [androidx.window:window:1.0.0] E:\android_development_cache\.gradle\caches\transforms-3\2d3a06f14c6f275b0094d4668de90ecb\transformed\window-1.0.0\AndroidManifest.xml:25:9-27:40
	android:required
		ADDED from [androidx.window:window:1.0.0] E:\android_development_cache\.gradle\caches\transforms-3\2d3a06f14c6f275b0094d4668de90ecb\transformed\window-1.0.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.window:window:1.0.0] E:\android_development_cache\.gradle\caches\transforms-3\2d3a06f14c6f275b0094d4668de90ecb\transformed\window-1.0.0\AndroidManifest.xml:26:13-54
uses-library#androidx.window.sidecar
ADDED from [androidx.window:window:1.0.0] E:\android_development_cache\.gradle\caches\transforms-3\2d3a06f14c6f275b0094d4668de90ecb\transformed\window-1.0.0\AndroidManifest.xml:28:9-30:40
	android:required
		ADDED from [androidx.window:window:1.0.0] E:\android_development_cache\.gradle\caches\transforms-3\2d3a06f14c6f275b0094d4668de90ecb\transformed\window-1.0.0\AndroidManifest.xml:30:13-37
	android:name
		ADDED from [androidx.window:window:1.0.0] E:\android_development_cache\.gradle\caches\transforms-3\2d3a06f14c6f275b0094d4668de90ecb\transformed\window-1.0.0\AndroidManifest.xml:29:13-51
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.10.1] E:\android_development_cache\.gradle\caches\transforms-3\d001c43b87f49a29f46bbdd20bd4ce3a\transformed\core-1.10.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.10.1] E:\android_development_cache\.gradle\caches\transforms-3\d001c43b87f49a29f46bbdd20bd4ce3a\transformed\core-1.10.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.10.1] E:\android_development_cache\.gradle\caches\transforms-3\d001c43b87f49a29f46bbdd20bd4ce3a\transformed\core-1.10.1\AndroidManifest.xml:23:9-81
permission#com.minimalmusicplayer.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.10.1] E:\android_development_cache\.gradle\caches\transforms-3\d001c43b87f49a29f46bbdd20bd4ce3a\transformed\core-1.10.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.10.1] E:\android_development_cache\.gradle\caches\transforms-3\d001c43b87f49a29f46bbdd20bd4ce3a\transformed\core-1.10.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.10.1] E:\android_development_cache\.gradle\caches\transforms-3\d001c43b87f49a29f46bbdd20bd4ce3a\transformed\core-1.10.1\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.10.1] E:\android_development_cache\.gradle\caches\transforms-3\d001c43b87f49a29f46bbdd20bd4ce3a\transformed\core-1.10.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.10.1] E:\android_development_cache\.gradle\caches\transforms-3\d001c43b87f49a29f46bbdd20bd4ce3a\transformed\core-1.10.1\AndroidManifest.xml:26:22-94
uses-permission#com.minimalmusicplayer.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.10.1] E:\android_development_cache\.gradle\caches\transforms-3\d001c43b87f49a29f46bbdd20bd4ce3a\transformed\core-1.10.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.10.1] E:\android_development_cache\.gradle\caches\transforms-3\d001c43b87f49a29f46bbdd20bd4ce3a\transformed\core-1.10.1\AndroidManifest.xml:26:22-94
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] E:\android_development_cache\.gradle\caches\transforms-3\0a451f34059334e77a169e82d985bb5f\transformed\profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] E:\android_development_cache\.gradle\caches\transforms-3\0a451f34059334e77a169e82d985bb5f\transformed\profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] E:\android_development_cache\.gradle\caches\transforms-3\0a451f34059334e77a169e82d985bb5f\transformed\profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] E:\android_development_cache\.gradle\caches\transforms-3\0a451f34059334e77a169e82d985bb5f\transformed\profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] E:\android_development_cache\.gradle\caches\transforms-3\0a451f34059334e77a169e82d985bb5f\transformed\profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] E:\android_development_cache\.gradle\caches\transforms-3\0a451f34059334e77a169e82d985bb5f\transformed\profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] E:\android_development_cache\.gradle\caches\transforms-3\0a451f34059334e77a169e82d985bb5f\transformed\profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] E:\android_development_cache\.gradle\caches\transforms-3\0a451f34059334e77a169e82d985bb5f\transformed\profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] E:\android_development_cache\.gradle\caches\transforms-3\0a451f34059334e77a169e82d985bb5f\transformed\profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] E:\android_development_cache\.gradle\caches\transforms-3\0a451f34059334e77a169e82d985bb5f\transformed\profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] E:\android_development_cache\.gradle\caches\transforms-3\0a451f34059334e77a169e82d985bb5f\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] E:\android_development_cache\.gradle\caches\transforms-3\0a451f34059334e77a169e82d985bb5f\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] E:\android_development_cache\.gradle\caches\transforms-3\0a451f34059334e77a169e82d985bb5f\transformed\profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] E:\android_development_cache\.gradle\caches\transforms-3\0a451f34059334e77a169e82d985bb5f\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] E:\android_development_cache\.gradle\caches\transforms-3\0a451f34059334e77a169e82d985bb5f\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] E:\android_development_cache\.gradle\caches\transforms-3\0a451f34059334e77a169e82d985bb5f\transformed\profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] E:\android_development_cache\.gradle\caches\transforms-3\0a451f34059334e77a169e82d985bb5f\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] E:\android_development_cache\.gradle\caches\transforms-3\0a451f34059334e77a169e82d985bb5f\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] E:\android_development_cache\.gradle\caches\transforms-3\0a451f34059334e77a169e82d985bb5f\transformed\profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] E:\android_development_cache\.gradle\caches\transforms-3\0a451f34059334e77a169e82d985bb5f\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] E:\android_development_cache\.gradle\caches\transforms-3\0a451f34059334e77a169e82d985bb5f\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
