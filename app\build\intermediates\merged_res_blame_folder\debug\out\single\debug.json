[{"merged": "E:\\android_development_cache\\.gradle\\daemon\\8.0\\com.minimalmusicplayer.app-merged_res-4:\\drawable_ic_launcher_foreground.xml.flat", "source": "E:\\android_development_cache\\.gradle\\daemon\\8.0\\com.minimalmusicplayer.app-main-6:\\drawable\\ic_launcher_foreground.xml"}, {"merged": "E:\\android_development_cache\\.gradle\\daemon\\8.0\\com.minimalmusicplayer.app-merged_res-4:\\drawable_ic_add.xml.flat", "source": "E:\\android_development_cache\\.gradle\\daemon\\8.0\\com.minimalmusicplayer.app-main-6:\\drawable\\ic_add.xml"}, {"merged": "E:\\android_development_cache\\.gradle\\daemon\\8.0\\com.minimalmusicplayer.app-merged_res-4:\\layout_activity_main.xml.flat", "source": "E:\\android_development_cache\\.gradle\\daemon\\8.0\\com.minimalmusicplayer.app-main-6:\\layout\\activity_main.xml"}, {"merged": "E:\\android_development_cache\\.gradle\\daemon\\8.0\\com.minimalmusicplayer.app-merged_res-4:\\mipmap-anydpi-v26_ic_launcher.xml.flat", "source": "E:\\android_development_cache\\.gradle\\daemon\\8.0\\com.minimalmusicplayer.app-main-6:\\mipmap-anydpi-v26\\ic_launcher.xml"}, {"merged": "E:\\android_development_cache\\.gradle\\daemon\\8.0\\com.minimalmusicplayer.app-merged_res-4:\\drawable_ic_skip_previous.xml.flat", "source": "E:\\android_development_cache\\.gradle\\daemon\\8.0\\com.minimalmusicplayer.app-main-6:\\drawable\\ic_skip_previous.xml"}, {"merged": "E:\\android_development_cache\\.gradle\\daemon\\8.0\\com.minimalmusicplayer.app-merged_res-4:\\drawable_ic_delete.xml.flat", "source": "E:\\android_development_cache\\.gradle\\daemon\\8.0\\com.minimalmusicplayer.app-main-6:\\drawable\\ic_delete.xml"}, {"merged": "E:\\android_development_cache\\.gradle\\daemon\\8.0\\com.minimalmusicplayer.app-merged_res-4:\\drawable_widget_button_background.xml.flat", "source": "E:\\android_development_cache\\.gradle\\daemon\\8.0\\com.minimalmusicplayer.app-main-6:\\drawable\\widget_button_background.xml"}, {"merged": "E:\\android_development_cache\\.gradle\\daemon\\8.0\\com.minimalmusicplayer.app-merged_res-4:\\drawable_ic_folder.xml.flat", "source": "E:\\android_development_cache\\.gradle\\daemon\\8.0\\com.minimalmusicplayer.app-main-6:\\drawable\\ic_folder.xml"}, {"merged": "com.minimalmusicplayer.app-merged_res-4:/xml_music_widget_info.xml.flat", "source": "com.minimalmusicplayer.app-main-6:/xml/music_widget_info.xml"}, {"merged": "E:\\android_development_cache\\.gradle\\daemon\\8.0\\com.minimalmusicplayer.app-merged_res-4:\\mipmap-anydpi-v26_ic_launcher_round.xml.flat", "source": "E:\\android_development_cache\\.gradle\\daemon\\8.0\\com.minimalmusicplayer.app-main-6:\\mipmap-anydpi-v26\\ic_launcher_round.xml"}, {"merged": "E:\\android_development_cache\\.gradle\\daemon\\8.0\\com.minimalmusicplayer.app-merged_res-4:\\mipmap-hdpi_ic_launcher.png.flat", "source": "E:\\android_development_cache\\.gradle\\daemon\\8.0\\com.minimalmusicplayer.app-main-6:\\mipmap-hdpi\\ic_launcher.png"}, {"merged": "E:\\android_development_cache\\.gradle\\daemon\\8.0\\com.minimalmusicplayer.app-merged_res-4:\\drawable_widget_preview.xml.flat", "source": "E:\\android_development_cache\\.gradle\\daemon\\8.0\\com.minimalmusicplayer.app-main-6:\\drawable\\widget_preview.xml"}, {"merged": "E:\\android_development_cache\\.gradle\\daemon\\8.0\\com.minimalmusicplayer.app-merged_res-4:\\drawable_ic_music_note.xml.flat", "source": "E:\\android_development_cache\\.gradle\\daemon\\8.0\\com.minimalmusicplayer.app-main-6:\\drawable\\ic_music_note.xml"}, {"merged": "E:\\android_development_cache\\.gradle\\daemon\\8.0\\com.minimalmusicplayer.app-merged_res-4:\\drawable_ic_skip_next.xml.flat", "source": "E:\\android_development_cache\\.gradle\\daemon\\8.0\\com.minimalmusicplayer.app-main-6:\\drawable\\ic_skip_next.xml"}, {"merged": "E:\\android_development_cache\\.gradle\\daemon\\8.0\\com.minimalmusicplayer.app-merged_res-4:\\menu_main_menu.xml.flat", "source": "E:\\android_development_cache\\.gradle\\daemon\\8.0\\com.minimalmusicplayer.app-main-6:\\menu\\main_menu.xml"}, {"merged": "E:\\android_development_cache\\.gradle\\daemon\\8.0\\com.minimalmusicplayer.app-merged_res-4:\\drawable_ic_pause.xml.flat", "source": "E:\\android_development_cache\\.gradle\\daemon\\8.0\\com.minimalmusicplayer.app-main-6:\\drawable\\ic_pause.xml"}, {"merged": "E:\\android_development_cache\\.gradle\\daemon\\8.0\\com.minimalmusicplayer.app-merged_res-4:\\xml_backup_rules.xml.flat", "source": "E:\\android_development_cache\\.gradle\\daemon\\8.0\\com.minimalmusicplayer.app-main-6:\\xml\\backup_rules.xml"}, {"merged": "E:\\android_development_cache\\.gradle\\daemon\\8.0\\com.minimalmusicplayer.app-merged_res-4:\\drawable_widget_background.xml.flat", "source": "E:\\android_development_cache\\.gradle\\daemon\\8.0\\com.minimalmusicplayer.app-main-6:\\drawable\\widget_background.xml"}, {"merged": "E:\\android_development_cache\\.gradle\\daemon\\8.0\\com.minimalmusicplayer.app-merged_res-4:\\layout_widget_music_player.xml.flat", "source": "E:\\android_development_cache\\.gradle\\daemon\\8.0\\com.minimalmusicplayer.app-main-6:\\layout\\widget_music_player.xml"}, {"merged": "E:\\android_development_cache\\.gradle\\daemon\\8.0\\com.minimalmusicplayer.app-merged_res-4:\\xml_data_extraction_rules.xml.flat", "source": "E:\\android_development_cache\\.gradle\\daemon\\8.0\\com.minimalmusicplayer.app-main-6:\\xml\\data_extraction_rules.xml"}, {"merged": "E:\\android_development_cache\\.gradle\\daemon\\8.0\\com.minimalmusicplayer.app-merged_res-4:\\layout_item_directory.xml.flat", "source": "E:\\android_development_cache\\.gradle\\daemon\\8.0\\com.minimalmusicplayer.app-main-6:\\layout\\item_directory.xml"}, {"merged": "E:\\android_development_cache\\.gradle\\daemon\\8.0\\com.minimalmusicplayer.app-merged_res-4:\\xml_music_widget_info.xml.flat", "source": "E:\\android_development_cache\\.gradle\\daemon\\8.0\\com.minimalmusicplayer.app-main-6:\\xml\\music_widget_info.xml"}, {"merged": "E:\\android_development_cache\\.gradle\\daemon\\8.0\\com.minimalmusicplayer.app-merged_res-4:\\layout_activity_directory_selection.xml.flat", "source": "E:\\android_development_cache\\.gradle\\daemon\\8.0\\com.minimalmusicplayer.app-main-6:\\layout\\activity_directory_selection.xml"}, {"merged": "E:\\android_development_cache\\.gradle\\daemon\\8.0\\com.minimalmusicplayer.app-merged_res-4:\\drawable_ic_play.xml.flat", "source": "E:\\android_development_cache\\.gradle\\daemon\\8.0\\com.minimalmusicplayer.app-main-6:\\drawable\\ic_play.xml"}, {"merged": "E:\\android_development_cache\\.gradle\\daemon\\8.0\\com.minimalmusicplayer.app-merged_res-4:\\drawable_ic_refresh.xml.flat", "source": "E:\\android_development_cache\\.gradle\\daemon\\8.0\\com.minimalmusicplayer.app-main-6:\\drawable\\ic_refresh.xml"}, {"merged": "E:\\android_development_cache\\.gradle\\daemon\\8.0\\com.minimalmusicplayer.app-merged_res-4:\\layout_music_widget.xml.flat", "source": "E:\\android_development_cache\\.gradle\\daemon\\8.0\\com.minimalmusicplayer.app-main-6:\\layout\\music_widget.xml"}, {"merged": "E:\\android_development_cache\\.gradle\\daemon\\8.0\\com.minimalmusicplayer.app-merged_res-4:\\drawable_ic_launcher_background.xml.flat", "source": "E:\\android_development_cache\\.gradle\\daemon\\8.0\\com.minimalmusicplayer.app-main-6:\\drawable\\ic_launcher_background.xml"}, {"merged": "E:\\android_development_cache\\.gradle\\daemon\\8.0\\com.minimalmusicplayer.app-merged_res-4:\\mipmap-hdpi_ic_launcher_round.png.flat", "source": "E:\\android_development_cache\\.gradle\\daemon\\8.0\\com.minimalmusicplayer.app-main-6:\\mipmap-hdpi\\ic_launcher_round.png"}, {"merged": "E:\\android_development_cache\\.gradle\\daemon\\8.0\\com.minimalmusicplayer.app-merged_res-4:\\layout_item_song.xml.flat", "source": "E:\\android_development_cache\\.gradle\\daemon\\8.0\\com.minimalmusicplayer.app-main-6:\\layout\\item_song.xml"}]