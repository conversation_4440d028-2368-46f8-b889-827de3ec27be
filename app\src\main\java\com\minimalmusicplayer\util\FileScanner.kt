package com.minimalmusicplayer.util

import android.content.Context
import android.media.MediaMetadataRetriever
import android.net.Uri
import android.provider.DocumentsContract
import android.provider.MediaStore
import androidx.documentfile.provider.DocumentFile
import com.minimalmusicplayer.model.Song
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.File

class FileScanner(private val context: Context) {
    
    private val supportedFormats = setOf("mp3", "flac", "ogg", "m4a", "aac", "wav", "wma")
    
    suspend fun scanDirectory(directoryUri: Uri): List<Song> = withContext(Dispatchers.IO) {
        val songs = mutableListOf<Song>()
        val documentFile = DocumentFile.fromTreeUri(context, directoryUri)
        
        documentFile?.let { dir ->
            scanDirectoryRecursive(dir, songs)
        }
        
        songs
    }
    
    private fun scanDirectoryRecursive(directory: DocumentFile, songs: MutableList<Song>) {
        directory.listFiles().forEach { file ->
            if (file.isDirectory) {
                scanDirectoryRecursive(file, songs)
            } else if (file.isFile && isAudioFile(file.name)) {
                extractSongInfo(file)?.let { song ->
                    songs.add(song)
                }
            }
        }
    }
    
    private fun isAudioFile(fileName: String?): Boolean {
        if (fileName == null) return false
        val extension = fileName.substringAfterLast('.', "").lowercase()
        return supportedFormats.contains(extension)
    }
    
    private fun extractSongInfo(documentFile: DocumentFile): Song? {
        return try {
            val uri = documentFile.uri
            val retriever = MediaMetadataRetriever()
            
            retriever.use {
                it.setDataSource(context, uri)
                
                val title = it.extractMetadata(MediaMetadataRetriever.METADATA_KEY_TITLE)
                    ?: documentFile.name?.substringBeforeLast('.') ?: "Unknown"
                
                val artist = it.extractMetadata(MediaMetadataRetriever.METADATA_KEY_ARTIST)
                    ?: "Unknown Artist"
                
                val album = it.extractMetadata(MediaMetadataRetriever.METADATA_KEY_ALBUM)
                    ?: "Unknown Album"
                
                val durationStr = it.extractMetadata(MediaMetadataRetriever.METADATA_KEY_DURATION)
                val duration = durationStr?.toLongOrNull() ?: 0L
                
                Song(
                    id = uri.hashCode().toLong(),
                    title = title,
                    artist = artist,
                    album = album,
                    duration = duration,
                    path = uri.toString(),
                    size = documentFile.length(),
                    dateModified = documentFile.lastModified()
                )
            }
        } catch (e: Exception) {
            e.printStackTrace()
            null
        }
    }
    
    suspend fun scanMediaStore(): List<Song> = withContext(Dispatchers.IO) {
        val songs = mutableListOf<Song>()
        val projection = arrayOf(
            MediaStore.Audio.Media._ID,
            MediaStore.Audio.Media.TITLE,
            MediaStore.Audio.Media.ARTIST,
            MediaStore.Audio.Media.ALBUM,
            MediaStore.Audio.Media.DURATION,
            MediaStore.Audio.Media.DATA,
            MediaStore.Audio.Media.SIZE,
            MediaStore.Audio.Media.DATE_MODIFIED
        )
        
        val selection = "${MediaStore.Audio.Media.IS_MUSIC} = 1"
        val sortOrder = "${MediaStore.Audio.Media.TITLE} ASC"
        
        context.contentResolver.query(
            MediaStore.Audio.Media.EXTERNAL_CONTENT_URI,
            projection,
            selection,
            null,
            sortOrder
        )?.use { cursor ->
            val idColumn = cursor.getColumnIndexOrThrow(MediaStore.Audio.Media._ID)
            val titleColumn = cursor.getColumnIndexOrThrow(MediaStore.Audio.Media.TITLE)
            val artistColumn = cursor.getColumnIndexOrThrow(MediaStore.Audio.Media.ARTIST)
            val albumColumn = cursor.getColumnIndexOrThrow(MediaStore.Audio.Media.ALBUM)
            val durationColumn = cursor.getColumnIndexOrThrow(MediaStore.Audio.Media.DURATION)
            val dataColumn = cursor.getColumnIndexOrThrow(MediaStore.Audio.Media.DATA)
            val sizeColumn = cursor.getColumnIndexOrThrow(MediaStore.Audio.Media.SIZE)
            val dateColumn = cursor.getColumnIndexOrThrow(MediaStore.Audio.Media.DATE_MODIFIED)
            
            while (cursor.moveToNext()) {
                val id = cursor.getLong(idColumn)
                val title = cursor.getString(titleColumn) ?: "Unknown"
                val artist = cursor.getString(artistColumn) ?: "Unknown Artist"
                val album = cursor.getString(albumColumn) ?: "Unknown Album"
                val duration = cursor.getLong(durationColumn)
                val path = cursor.getString(dataColumn) ?: ""
                val size = cursor.getLong(sizeColumn)
                val dateModified = cursor.getLong(dateColumn)
                
                // Check if file exists and is supported format
                if (File(path).exists() && isAudioFile(path)) {
                    songs.add(
                        Song(
                            id = id,
                            title = title,
                            artist = artist,
                            album = album,
                            duration = duration,
                            path = path,
                            size = size,
                            dateModified = dateModified
                        )
                    )
                }
            }
        }
        
        songs
    }
}
