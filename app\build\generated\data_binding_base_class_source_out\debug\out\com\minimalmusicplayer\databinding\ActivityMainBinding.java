// Generated by view binder compiler. Do not edit!
package com.minimalmusicplayer.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageButton;
import android.widget.LinearLayout;
import android.widget.ProgressBar;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.Toolbar;
import androidx.coordinatorlayout.widget.CoordinatorLayout;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.minimalmusicplayer.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityMainBinding implements ViewBinding {
  @NonNull
  private final CoordinatorLayout rootView;

  @NonNull
  public final ImageButton buttonNext;

  @NonNull
  public final ImageButton buttonPlayPause;

  @NonNull
  public final ImageButton buttonPrevious;

  @NonNull
  public final LinearLayout playerContainer;

  @NonNull
  public final ProgressBar progressBar;

  @NonNull
  public final RecyclerView recyclerViewSongs;

  @NonNull
  public final TextView textViewEmptyState;

  @NonNull
  public final TextView textViewSongArtist;

  @NonNull
  public final TextView textViewSongTitle;

  @NonNull
  public final Toolbar toolbar;

  private ActivityMainBinding(@NonNull CoordinatorLayout rootView, @NonNull ImageButton buttonNext,
      @NonNull ImageButton buttonPlayPause, @NonNull ImageButton buttonPrevious,
      @NonNull LinearLayout playerContainer, @NonNull ProgressBar progressBar,
      @NonNull RecyclerView recyclerViewSongs, @NonNull TextView textViewEmptyState,
      @NonNull TextView textViewSongArtist, @NonNull TextView textViewSongTitle,
      @NonNull Toolbar toolbar) {
    this.rootView = rootView;
    this.buttonNext = buttonNext;
    this.buttonPlayPause = buttonPlayPause;
    this.buttonPrevious = buttonPrevious;
    this.playerContainer = playerContainer;
    this.progressBar = progressBar;
    this.recyclerViewSongs = recyclerViewSongs;
    this.textViewEmptyState = textViewEmptyState;
    this.textViewSongArtist = textViewSongArtist;
    this.textViewSongTitle = textViewSongTitle;
    this.toolbar = toolbar;
  }

  @Override
  @NonNull
  public CoordinatorLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityMainBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityMainBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_main, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityMainBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.buttonNext;
      ImageButton buttonNext = ViewBindings.findChildViewById(rootView, id);
      if (buttonNext == null) {
        break missingId;
      }

      id = R.id.buttonPlayPause;
      ImageButton buttonPlayPause = ViewBindings.findChildViewById(rootView, id);
      if (buttonPlayPause == null) {
        break missingId;
      }

      id = R.id.buttonPrevious;
      ImageButton buttonPrevious = ViewBindings.findChildViewById(rootView, id);
      if (buttonPrevious == null) {
        break missingId;
      }

      id = R.id.playerContainer;
      LinearLayout playerContainer = ViewBindings.findChildViewById(rootView, id);
      if (playerContainer == null) {
        break missingId;
      }

      id = R.id.progressBar;
      ProgressBar progressBar = ViewBindings.findChildViewById(rootView, id);
      if (progressBar == null) {
        break missingId;
      }

      id = R.id.recyclerViewSongs;
      RecyclerView recyclerViewSongs = ViewBindings.findChildViewById(rootView, id);
      if (recyclerViewSongs == null) {
        break missingId;
      }

      id = R.id.textViewEmptyState;
      TextView textViewEmptyState = ViewBindings.findChildViewById(rootView, id);
      if (textViewEmptyState == null) {
        break missingId;
      }

      id = R.id.textViewSongArtist;
      TextView textViewSongArtist = ViewBindings.findChildViewById(rootView, id);
      if (textViewSongArtist == null) {
        break missingId;
      }

      id = R.id.textViewSongTitle;
      TextView textViewSongTitle = ViewBindings.findChildViewById(rootView, id);
      if (textViewSongTitle == null) {
        break missingId;
      }

      id = R.id.toolbar;
      Toolbar toolbar = ViewBindings.findChildViewById(rootView, id);
      if (toolbar == null) {
        break missingId;
      }

      return new ActivityMainBinding((CoordinatorLayout) rootView, buttonNext, buttonPlayPause,
          buttonPrevious, playerContainer, progressBar, recyclerViewSongs, textViewEmptyState,
          textViewSongArtist, textViewSongTitle, toolbar);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
