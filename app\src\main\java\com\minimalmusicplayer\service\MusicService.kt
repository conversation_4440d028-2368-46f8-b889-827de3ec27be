package com.minimalmusicplayer.service

import android.app.*
import android.content.Intent
import android.net.Uri
import android.os.Binder
import android.os.Build
import android.os.Bundle
import android.os.IBinder
import android.support.v4.media.MediaBrowserCompat
import android.support.v4.media.MediaMetadataCompat
import android.support.v4.media.session.MediaSessionCompat
import android.support.v4.media.session.PlaybackStateCompat
import androidx.core.app.NotificationCompat
import androidx.media.MediaBrowserServiceCompat
import com.google.android.exoplayer2.*
import com.google.android.exoplayer2.audio.AudioAttributes
import com.google.android.exoplayer2.ui.PlayerNotificationManager
import com.minimalmusicplayer.MainActivity
import com.minimalmusicplayer.R
import com.minimalmusicplayer.model.Song
import com.minimalmusicplayer.util.ErrorHandler
import com.minimalmusicplayer.util.PreferenceManager
import com.minimalmusicplayer.widget.MusicWidgetProvider

class MusicService : MediaBrowserServiceCompat() {
    
    private lateinit var exoPlayer: ExoPlayer
    private lateinit var mediaSession: MediaSessionCompat
    private lateinit var notificationManager: PlayerNotificationManager
    private lateinit var preferenceManager: PreferenceManager
    
    private var currentSong: Song? = null
    private var playlist: List<Song> = emptyList()
    private var currentIndex = 0
    private var isShuffleEnabled = false
    private var repeatMode = Player.REPEAT_MODE_OFF
    
    private val binder = MusicBinder()
    
    // Listeners
    private val playbackListeners = mutableSetOf<PlaybackListener>()
    
    interface PlaybackListener {
        fun onSongChanged(song: Song?)
        fun onPlaybackStateChanged(isPlaying: Boolean)
        fun onPositionChanged(position: Long, duration: Long)
    }
    
    inner class MusicBinder : Binder() {
        fun getService(): MusicService = this@MusicService
    }
    
    override fun onCreate() {
        super.onCreate()
        
        preferenceManager = PreferenceManager(this)
        initializePlayer()
        initializeMediaSession()
        initializeNotification()
    }
    
    private fun initializePlayer() {
        val audioAttributes = AudioAttributes.Builder()
            .setUsage(C.USAGE_MEDIA)
            .setContentType(C.AUDIO_CONTENT_TYPE_MUSIC)
            .build()
        
        exoPlayer = ExoPlayer.Builder(this)
            .setAudioAttributes(audioAttributes, true)
            .setHandleAudioBecomingNoisy(true)
            .build()
        
        exoPlayer.addListener(object : Player.Listener {
            override fun onPlaybackStateChanged(playbackState: Int) {
                updatePlaybackState()
                notifyPlaybackStateChanged()
            }

            override fun onMediaItemTransition(mediaItem: MediaItem?, reason: Int) {
                updateCurrentSong()
                notifySongChanged()
            }

            override fun onPositionDiscontinuity(
                oldPosition: Player.PositionInfo,
                newPosition: Player.PositionInfo,
                reason: Int
            ) {
                if (reason == Player.DISCONTINUITY_REASON_AUTO_TRANSITION) {
                    handleTrackCompletion()
                }
            }

            override fun onPlayerError(error: PlaybackException) {
                ErrorHandler.handlePlaybackError(this@MusicService, error)
                ErrorHandler.logError("MusicService", "Playback error", error)

                // Try to skip to next song on error
                if (currentIndex < playlist.size - 1) {
                    skipToNext()
                } else {
                    pause()
                }
            }
        })
    }
    
    private fun initializeMediaSession() {
        mediaSession = MediaSessionCompat(this, "MusicService")
        mediaSession.setCallback(object : MediaSessionCompat.Callback() {
            override fun onPlay() {
                play()
            }
            
            override fun onPause() {
                pause()
            }
            
            override fun onSkipToNext() {
                skipToNext()
            }
            
            override fun onSkipToPrevious() {
                skipToPrevious()
            }
            
            override fun onSeekTo(pos: Long) {
                seekTo(pos)
            }
            
            override fun onStop() {
                stop()
            }
        })
        
        sessionToken = mediaSession.sessionToken
        mediaSession.isActive = true
    }
    
    private fun initializeNotification() {
        createNotificationChannel()
        
        notificationManager = PlayerNotificationManager.Builder(
            this,
            NOTIFICATION_ID,
            NOTIFICATION_CHANNEL_ID
        )
            .setMediaDescriptionAdapter(object : PlayerNotificationManager.MediaDescriptionAdapter {
                override fun getCurrentContentTitle(player: Player): CharSequence {
                    return currentSong?.getDisplayTitle() ?: "Unknown"
                }
                
                override fun createCurrentContentIntent(player: Player): PendingIntent? {
                    val intent = Intent(this@MusicService, MainActivity::class.java)
                    return PendingIntent.getActivity(
                        this@MusicService,
                        0,
                        intent,
                        PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
                    )
                }
                
                override fun getCurrentContentText(player: Player): CharSequence? {
                    return currentSong?.getDisplayArtist()
                }
                
                override fun getCurrentLargeIcon(
                    player: Player,
                    callback: PlayerNotificationManager.BitmapCallback
                ): android.graphics.Bitmap? {
                    return null // We'll implement album art later if needed
                }
            })
            .setNotificationListener(object : PlayerNotificationManager.NotificationListener {
                override fun onNotificationCancelled(notificationId: Int, dismissedByUser: Boolean) {
                    stopForeground(true)
                    stopSelf()
                }
                
                override fun onNotificationPosted(
                    notificationId: Int,
                    notification: Notification,
                    ongoing: Boolean
                ) {
                    if (ongoing) {
                        startForeground(notificationId, notification)
                    } else {
                        stopForeground(false)
                    }
                }
            })
            .build()
        
        notificationManager.setPlayer(exoPlayer)
        notificationManager.setMediaSessionToken(mediaSession.sessionToken)
    }
    
    private fun createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channel = NotificationChannel(
                NOTIFICATION_CHANNEL_ID,
                "Music Playback",
                NotificationManager.IMPORTANCE_LOW
            ).apply {
                description = "Music playback controls"
                setShowBadge(false)
                lockscreenVisibility = Notification.VISIBILITY_PUBLIC
            }
            
            val notificationManager = getSystemService(NotificationManager::class.java)
            notificationManager.createNotificationChannel(channel)
        }
    }
    
    override fun onBind(intent: Intent?): IBinder {
        return binder
    }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        intent?.action?.let { action ->
            when (action) {
                "PLAY_PAUSE" -> {
                    if (isPlaying()) pause() else play()
                }
                "NEXT" -> skipToNext()
                "PREVIOUS" -> skipToPrevious()
            }
        }
        return START_STICKY
    }
    
    override fun onGetRoot(
        clientPackageName: String,
        clientUid: Int,
        rootHints: Bundle?
    ): BrowserRoot? {
        return BrowserRoot("root", null)
    }
    
    override fun onLoadChildren(
        parentId: String,
        result: Result<MutableList<MediaBrowserCompat.MediaItem>>
    ) {
        result.sendResult(mutableListOf())
    }
    
    // Public methods for controlling playback
    fun setPlaylist(songs: List<Song>, startIndex: Int = 0) {
        playlist = songs
        currentIndex = startIndex.coerceIn(0, playlist.size - 1)
        
        if (playlist.isNotEmpty()) {
            preparePlaylist()
            updateCurrentSong()
        }
    }
    
    private fun preparePlaylist() {
        val mediaItems = playlist.map { song ->
            MediaItem.fromUri(Uri.parse(song.path))
        }
        exoPlayer.setMediaItems(mediaItems, currentIndex, C.TIME_UNSET)
        exoPlayer.prepare()
    }
    
    fun play() {
        exoPlayer.play()
    }
    
    fun pause() {
        exoPlayer.pause()
    }
    
    fun stop() {
        exoPlayer.stop()
        stopForeground(true)
        stopSelf()
    }
    
    fun skipToNext() {
        if (currentIndex < playlist.size - 1) {
            currentIndex++
            exoPlayer.seekToNext()
        }
    }
    
    fun skipToPrevious() {
        if (currentIndex > 0) {
            currentIndex--
            exoPlayer.seekToPrevious()
        }
    }
    
    fun seekTo(position: Long) {
        exoPlayer.seekTo(position)
    }
    
    fun getCurrentSong(): Song? = currentSong
    fun isPlaying(): Boolean = exoPlayer.isPlaying
    fun getCurrentPosition(): Long = exoPlayer.currentPosition
    fun getDuration(): Long = exoPlayer.duration.takeIf { it != C.TIME_UNSET } ?: 0
    
    fun addPlaybackListener(listener: PlaybackListener) {
        playbackListeners.add(listener)
    }
    
    fun removePlaybackListener(listener: PlaybackListener) {
        playbackListeners.remove(listener)
    }
    
    private fun updateCurrentSong() {
        currentSong = if (currentIndex in playlist.indices) {
            playlist[currentIndex]
        } else null
        
        currentSong?.let { song ->
            preferenceManager.saveLastPlayedSong(song.path)
        }
    }
    
    private fun updatePlaybackState() {
        val state = when {
            exoPlayer.isPlaying -> PlaybackStateCompat.STATE_PLAYING
            exoPlayer.playbackState == Player.STATE_BUFFERING -> PlaybackStateCompat.STATE_BUFFERING
            else -> PlaybackStateCompat.STATE_PAUSED
        }
        
        val playbackState = PlaybackStateCompat.Builder()
            .setState(state, exoPlayer.currentPosition, 1.0f)
            .setActions(
                PlaybackStateCompat.ACTION_PLAY or
                PlaybackStateCompat.ACTION_PAUSE or
                PlaybackStateCompat.ACTION_SKIP_TO_NEXT or
                PlaybackStateCompat.ACTION_SKIP_TO_PREVIOUS or
                PlaybackStateCompat.ACTION_SEEK_TO
            )
            .build()
        
        mediaSession.setPlaybackState(playbackState)
    }
    
    private fun handleTrackCompletion() {
        when (repeatMode) {
            Player.REPEAT_MODE_ONE -> {
                // Song will repeat automatically
            }
            Player.REPEAT_MODE_ALL -> {
                if (currentIndex >= playlist.size - 1) {
                    currentIndex = 0
                } else {
                    currentIndex++
                }
            }
            else -> {
                if (currentIndex < playlist.size - 1) {
                    currentIndex++
                } else {
                    pause()
                }
            }
        }
    }
    
    private fun notifySongChanged() {
        playbackListeners.forEach { it.onSongChanged(currentSong) }
        updateWidget()
    }

    private fun notifyPlaybackStateChanged() {
        playbackListeners.forEach { it.onPlaybackStateChanged(exoPlayer.isPlaying) }
        updateWidget()
    }

    private fun updateWidget() {
        MusicWidgetProvider.updateWidget(
            this,
            currentSong?.getDisplayTitle(),
            currentSong?.getDisplayArtist(),
            exoPlayer.isPlaying
        )
    }
    
    override fun onDestroy() {
        super.onDestroy()
        exoPlayer.release()
        mediaSession.release()
        notificationManager.setPlayer(null)
    }
    
    companion object {
        private const val NOTIFICATION_ID = 1
        private const val NOTIFICATION_CHANNEL_ID = "music_playback_channel"
    }
}
