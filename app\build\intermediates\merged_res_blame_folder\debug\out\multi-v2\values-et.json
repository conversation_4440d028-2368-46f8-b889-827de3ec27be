{"logs": [{"outputFile": "com.minimalmusicplayer.app-mergeDebugResources-2:/values-et/values-et.xml", "map": [{"source": "E:\\android_development_cache\\.gradle\\caches\\transforms-3\\f6a56acf230789289efb8d310bea470b\\transformed\\exoplayer-core-2.19.1\\res\\values-et\\values-et.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,129,196,263,337,419,490,580,672", "endColumns": "73,66,66,73,81,70,89,91,76", "endOffsets": "124,191,258,332,414,485,575,667,744"}, "to": {"startLines": "91,92,93,94,95,96,97,98,99", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "7229,7303,7370,7437,7511,7593,7664,7754,7846", "endColumns": "73,66,66,73,81,70,89,91,76", "endOffsets": "7298,7365,7432,7506,7588,7659,7749,7841,7918"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\transforms-3\\44bdd8c9f94ff480fecdbcca682a189c\\transformed\\exoplayer-ui-2.19.1\\res\\values-et\\values-et.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,323,514,702,783,864,940,1031,1124,1194,1258,1342,1425,1490,1554,1617,1687,1807,1925,2044,2116,2200,2269,2338,2432,2526,2591,2657,2710,2770,2818,2879,2944,3014,3079,3145,3209,3269,3334,3399,3465,3517,3579,3655,3731", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,80,80,75,90,92,69,63,83,82,64,63,62,69,119,117,118,71,83,68,68,93,93,64,65,52,59,47,60,64,69,64,65,63,59,64,64,65,51,61,75,75,54", "endOffsets": "318,509,697,778,859,935,1026,1119,1189,1253,1337,1420,1485,1549,1612,1682,1802,1920,2039,2111,2195,2264,2333,2427,2521,2586,2652,2705,2765,2813,2874,2939,3009,3074,3140,3204,3264,3329,3394,3460,3512,3574,3650,3726,3781"}, "to": {"startLines": "2,11,15,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,418,609,5274,5355,5436,5512,5603,5696,5766,5830,5914,5997,6062,6126,6189,6259,6379,6497,6616,6688,6772,6841,6910,7004,7098,7163,7923,7976,8036,8084,8145,8210,8280,8345,8411,8475,8535,8600,8665,8731,8783,8845,8921,8997", "endLines": "10,14,18,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117", "endColumns": "17,12,12,80,80,75,90,92,69,63,83,82,64,63,62,69,119,117,118,71,83,68,68,93,93,64,65,52,59,47,60,64,69,64,65,63,59,64,64,65,51,61,75,75,54", "endOffsets": "413,604,792,5350,5431,5507,5598,5691,5761,5825,5909,5992,6057,6121,6184,6254,6374,6492,6611,6683,6767,6836,6905,6999,7093,7158,7224,7971,8031,8079,8140,8205,8275,8340,8406,8470,8530,8595,8660,8726,8778,8840,8916,8992,9047"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\transforms-3\\b61f7d3f27c328116375b3d38dc2a4b7\\transformed\\appcompat-1.6.1\\res\\values-et\\values-et.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,211,310,421,507,609,726,807,884,976,1070,1166,1268,1377,1471,1572,1666,1758,1851,1934,2045,2149,2248,2358,2460,2559,2725,2827", "endColumns": "105,98,110,85,101,116,80,76,91,93,95,101,108,93,100,93,91,92,82,110,103,98,109,101,98,165,101,82", "endOffsets": "206,305,416,502,604,721,802,879,971,1065,1161,1263,1372,1466,1567,1661,1753,1846,1929,2040,2144,2243,2353,2455,2554,2720,2822,2905"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,174", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "963,1069,1168,1279,1365,1467,1584,1665,1742,1834,1928,2024,2126,2235,2329,2430,2524,2616,2709,2792,2903,3007,3106,3216,3318,3417,3583,13470", "endColumns": "105,98,110,85,101,116,80,76,91,93,95,101,108,93,100,93,91,92,82,110,103,98,109,101,98,165,101,82", "endOffsets": "1064,1163,1274,1360,1462,1579,1660,1737,1829,1923,2019,2121,2230,2324,2425,2519,2611,2704,2787,2898,3002,3101,3211,3313,3412,3578,3680,13548"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\transforms-3\\d001c43b87f49a29f46bbdd20bd4ce3a\\transformed\\core-1.10.1\\res\\values-et\\values-et.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,252,350,453,559,664,784", "endColumns": "94,101,97,102,105,104,119,100", "endOffsets": "145,247,345,448,554,659,779,880"}, "to": {"startLines": "55,56,57,58,59,60,61,175", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4108,4203,4305,4403,4506,4612,4717,13553", "endColumns": "94,101,97,102,105,104,119,100", "endOffsets": "4198,4300,4398,4501,4607,4712,4832,13649"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\transforms-3\\1674ca777f183a0ca81338d078e0cd6f\\transformed\\material-1.9.0\\res\\values-et\\values-et.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,266,346,425,510,602,689,788,905,987,1051,1136,1204,1268,1355,1419,1483,1542,1614,1678,1732,1851,1911,1972,2026,2099,2232,2316,2409,2547,2627,2706,2794,2849,2900,2966,3039,3118,3204,3277,3352,3426,3498,3586,3663,3754,3846,3918,3992,4083,4137,4206,4289,4375,4437,4501,4564,4667,4771,4868,4973,5032,5087", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "endColumns": "12,79,78,84,91,86,98,116,81,63,84,67,63,86,63,63,58,71,63,53,118,59,60,53,72,132,83,92,137,79,78,87,54,50,65,72,78,85,72,74,73,71,87,76,90,91,71,73,90,53,68,82,85,61,63,62,102,103,96,104,58,54,80", "endOffsets": "261,341,420,505,597,684,783,900,982,1046,1131,1199,1263,1350,1414,1478,1537,1609,1673,1727,1846,1906,1967,2021,2094,2227,2311,2404,2542,2622,2701,2789,2844,2895,2961,3034,3113,3199,3272,3347,3421,3493,3581,3658,3749,3841,3913,3987,4078,4132,4201,4284,4370,4432,4496,4559,4662,4766,4863,4968,5027,5082,5163"}, "to": {"startLines": "19,50,51,52,53,54,62,63,64,66,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,172", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "797,3685,3765,3844,3929,4021,4837,4936,5053,5210,9139,9224,9292,9356,9443,9507,9571,9630,9702,9766,9820,9939,9999,10060,10114,10187,10320,10404,10497,10635,10715,10794,10882,10937,10988,11054,11127,11206,11292,11365,11440,11514,11586,11674,11751,11842,11934,12006,12080,12171,12225,12294,12377,12463,12525,12589,12652,12755,12859,12956,13061,13120,13251", "endLines": "22,50,51,52,53,54,62,63,64,66,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,172", "endColumns": "12,79,78,84,91,86,98,116,81,63,84,67,63,86,63,63,58,71,63,53,118,59,60,53,72,132,83,92,137,79,78,87,54,50,65,72,78,85,72,74,73,71,87,76,90,91,71,73,90,53,68,82,85,61,63,62,102,103,96,104,58,54,80", "endOffsets": "958,3760,3839,3924,4016,4103,4931,5048,5130,5269,9219,9287,9351,9438,9502,9566,9625,9697,9761,9815,9934,9994,10055,10109,10182,10315,10399,10492,10630,10710,10789,10877,10932,10983,11049,11122,11201,11287,11360,11435,11509,11581,11669,11746,11837,11929,12001,12075,12166,12220,12289,12372,12458,12520,12584,12647,12750,12854,12951,13056,13115,13170,13327"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\transforms-3\\550895908bcd254b169aeba9b6074ec2\\transformed\\preference-1.2.1\\res\\values-et\\values-et.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,180,267,343,481,650,733", "endColumns": "74,86,75,137,168,82,77", "endOffsets": "175,262,338,476,645,728,806"}, "to": {"startLines": "65,118,171,173,176,177,178", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "5135,9052,13175,13332,13654,13823,13906", "endColumns": "74,86,75,137,168,82,77", "endOffsets": "5205,9134,13246,13465,13818,13901,13979"}}]}]}