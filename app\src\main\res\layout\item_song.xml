<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="?attr/selectableItemBackground"
    android:orientation="horizontal"
    android:padding="16dp">

    <!-- Song info -->
    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:orientation="vertical">

        <TextView
            android:id="@+id/textViewTitle"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:maxLines="1"
            android:textColor="?attr/colorOnSurface"
            android:textSize="16sp"
            android:textStyle="bold"
            tools:text="Song Title" />

        <TextView
            android:id="@+id/textViewArtist"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:ellipsize="end"
            android:maxLines="1"
            android:textColor="?attr/colorOnSurfaceVariant"
            android:textSize="14sp"
            tools:text="Artist Name" />

    </LinearLayout>

    <!-- Duration -->
    <TextView
        android:id="@+id/textViewDuration"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_vertical"
        android:layout_marginStart="16dp"
        android:textColor="?attr/colorOnSurfaceVariant"
        android:textSize="12sp"
        tools:text="3:45" />

</LinearLayout>
