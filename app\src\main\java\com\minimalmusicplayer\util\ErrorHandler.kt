package com.minimalmusicplayer.util

import android.content.Context
import android.util.Log
import android.widget.Toast
import com.minimalmusicplayer.R

object ErrorHandler {
    
    private const val TAG = "MusicPlayerError"
    
    fun handleFileAccessError(context: Context, error: Throwable) {
        Log.e(TAG, "File access error", error)
        
        val message = when {
            error.message?.contains("permission", ignoreCase = true) == true -> {
                "Permission denied. Please check app permissions."
            }
            error.message?.contains("not found", ignoreCase = true) == true -> {
                "File not found. The music file may have been moved or deleted."
            }
            error.message?.contains("network", ignoreCase = true) == true -> {
                "Network error. Please check your connection."
            }
            else -> {
                "Unable to access music file: ${error.message}"
            }
        }
        
        Toast.makeText(context, message, Toast.LENGTH_LONG).show()
    }
    
    fun handlePlaybackError(context: Context, error: Throwable) {
        Log.e(TAG, "Playback error", error)
        
        val message = when {
            error.message?.contains("codec", ignoreCase = true) == true -> {
                "Unsupported audio format. This file cannot be played."
            }
            error.message?.contains("corrupt", ignoreCase = true) == true -> {
                "Corrupted audio file. Unable to play this track."
            }
            error.message?.contains("network", ignoreCase = true) == true -> {
                "Network error during playback. Please check your connection."
            }
            else -> {
                "Playback error: ${error.message}"
            }
        }
        
        Toast.makeText(context, message, Toast.LENGTH_LONG).show()
    }
    
    fun handleScanError(context: Context, error: Throwable) {
        Log.e(TAG, "Scan error", error)
        
        val message = when {
            error.message?.contains("permission", ignoreCase = true) == true -> {
                "Permission denied. Cannot scan music directories."
            }
            error.message?.contains("not found", ignoreCase = true) == true -> {
                "Directory not found. It may have been moved or deleted."
            }
            error.message?.contains("timeout", ignoreCase = true) == true -> {
                "Scan timeout. Try scanning smaller directories."
            }
            else -> {
                "Scan failed: ${error.message}"
            }
        }
        
        Toast.makeText(context, message, Toast.LENGTH_LONG).show()
    }
    
    fun handleServiceError(context: Context, error: Throwable) {
        Log.e(TAG, "Service error", error)
        
        val message = when {
            error.message?.contains("bind", ignoreCase = true) == true -> {
                "Unable to connect to music service. Please restart the app."
            }
            error.message?.contains("audio", ignoreCase = true) == true -> {
                "Audio system error. Please check your device's audio settings."
            }
            else -> {
                "Service error: ${error.message}"
            }
        }
        
        Toast.makeText(context, message, Toast.LENGTH_SHORT).show()
    }
    
    fun handleWidgetError(error: Throwable) {
        Log.e(TAG, "Widget error", error)
        // Widget errors are logged but not shown to user to avoid interruption
    }
    
    fun logError(tag: String, message: String, error: Throwable? = null) {
        if (error != null) {
            Log.e(TAG, "$tag: $message", error)
        } else {
            Log.e(TAG, "$tag: $message")
        }
    }
    
    fun logWarning(tag: String, message: String) {
        Log.w(TAG, "$tag: $message")
    }
    
    fun logInfo(tag: String, message: String) {
        Log.i(TAG, "$tag: $message")
    }
}
