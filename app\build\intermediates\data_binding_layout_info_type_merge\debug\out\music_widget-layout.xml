<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="music_widget" modulePackage="com.minimalmusicplayer" filePath="app\src\main\res\layout\music_widget.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/music_widget_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="77" endOffset="14"/></Target><Target id="@+id/widget_song_title" view="TextView"><Expressions/><location startLine="16" startOffset="8" endLine="25" endOffset="37"/></Target><Target id="@+id/widget_song_artist" view="TextView"><Expressions/><location startLine="27" startOffset="8" endLine="36" endOffset="37"/></Target><Target id="@+id/widget_btn_previous" view="ImageButton"><Expressions/><location startLine="47" startOffset="8" endLine="54" endOffset="49"/></Target><Target id="@+id/widget_btn_play_pause" view="ImageButton"><Expressions/><location startLine="56" startOffset="8" endLine="64" endOffset="49"/></Target><Target id="@+id/widget_btn_next" view="ImageButton"><Expressions/><location startLine="66" startOffset="8" endLine="73" endOffset="49"/></Target></Targets></Layout>