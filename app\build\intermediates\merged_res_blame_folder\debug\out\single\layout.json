[{"merged": "E:\\android_development_cache\\.gradle\\daemon\\8.0\\com.minimalmusicplayer.app-mergeDebugResources-3:\\layout\\activity_main.xml", "source": "E:\\android_development_cache\\.gradle\\daemon\\8.0\\com.minimalmusicplayer.app-main-6:\\layout\\activity_main.xml"}, {"merged": "E:\\android_development_cache\\.gradle\\daemon\\8.0\\com.minimalmusicplayer.app-mergeDebugResources-3:\\layout\\widget_music_player.xml", "source": "E:\\android_development_cache\\.gradle\\daemon\\8.0\\com.minimalmusicplayer.app-main-6:\\layout\\widget_music_player.xml"}, {"merged": "E:\\android_development_cache\\.gradle\\daemon\\8.0\\com.minimalmusicplayer.app-mergeDebugResources-3:\\layout\\item_song.xml", "source": "E:\\android_development_cache\\.gradle\\daemon\\8.0\\com.minimalmusicplayer.app-main-6:\\layout\\item_song.xml"}, {"merged": "E:\\android_development_cache\\.gradle\\daemon\\8.0\\com.minimalmusicplayer.app-mergeDebugResources-3:\\layout\\item_directory.xml", "source": "E:\\android_development_cache\\.gradle\\daemon\\8.0\\com.minimalmusicplayer.app-main-6:\\layout\\item_directory.xml"}, {"merged": "E:\\android_development_cache\\.gradle\\daemon\\8.0\\com.minimalmusicplayer.app-mergeDebugResources-3:\\layout\\activity_directory_selection.xml", "source": "E:\\android_development_cache\\.gradle\\daemon\\8.0\\com.minimalmusicplayer.app-main-6:\\layout\\activity_directory_selection.xml"}, {"merged": "com.minimalmusicplayer.app-mergeDebugResources-3:/layout/music_widget.xml", "source": "com.minimalmusicplayer.app-main-6:/layout/music_widget.xml"}]