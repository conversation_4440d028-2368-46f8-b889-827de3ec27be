package com.minimalmusicplayer

import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.content.pm.PackageManager
import android.graphics.BitmapFactory
import android.media.MediaPlayer
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.support.v4.media.MediaMetadataCompat
import android.support.v4.media.session.MediaSessionCompat
import android.support.v4.media.session.PlaybackStateCompat
import androidx.activity.result.contract.ActivityResultContracts
import androidx.appcompat.app.AppCompatActivity
import androidx.core.app.NotificationCompat
import androidx.documentfile.provider.DocumentFile
import androidx.lifecycle.lifecycleScope
import com.minimalmusicplayer.model.Song
import com.minimalmusicplayer.util.FileScanner
import com.minimalmusicplayer.util.PermissionHelper
import com.minimalmusicplayer.util.PreferenceManager
import com.minimalmusicplayer.widget.MusicWidgetProvider
import kotlinx.coroutines.launch

class MainActivity : AppCompatActivity() {

    private lateinit var fileScanner: FileScanner
    private lateinit var preferenceManager: PreferenceManager

    // UI components
    private lateinit var statusTextView: android.widget.TextView
    private lateinit var songListLayout: android.widget.LinearLayout
    private lateinit var playPauseButton: android.widget.Button
    private lateinit var currentSongTextView: android.widget.TextView

    // Music data
    private var songs = mutableListOf<Song>()
    private var currentSongIndex = -1
    private var currentSong: Song? = null

    // MediaPlayer for actual audio playback
    private var mediaPlayer: MediaPlayer? = null
    private var isCurrentlyPlaying = false

    // Media session and notification
    private lateinit var mediaSession: MediaSessionCompat
    private lateinit var notificationManager: NotificationManager

    companion object {
        const val NOTIFICATION_ID = 1
        const val CHANNEL_ID = "music_playback"
        const val ACTION_PLAY_PAUSE = "com.minimalmusicplayer.PLAY_PAUSE"
        const val ACTION_NEXT = "com.minimalmusicplayer.NEXT"
        const val ACTION_PREVIOUS = "com.minimalmusicplayer.PREVIOUS"
    }

    // Directory selection
    private val directoryPickerLauncher = registerForActivityResult(
        ActivityResultContracts.OpenDocumentTree()
    ) { uri ->
        uri?.let { selectedUri ->
            // Grant persistent permission
            contentResolver.takePersistableUriPermission(
                selectedUri,
                android.content.Intent.FLAG_GRANT_READ_URI_PERMISSION
            )
            scanSelectedDirectory(selectedUri)
        }
    }

    // Broadcast receiver for notification and widget controls
    private val mediaControlReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context?, intent: Intent?) {
            when (intent?.action) {
                ACTION_PLAY_PAUSE, "com.minimalmusicplayer.WIDGET_PLAY_PAUSE" -> togglePlayback()
                ACTION_NEXT, "com.minimalmusicplayer.WIDGET_NEXT" -> nextSong()
                ACTION_PREVIOUS, "com.minimalmusicplayer.WIDGET_PREVIOUS" -> previousSong()
            }
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        // Initialize components
        try {
            preferenceManager = PreferenceManager(this)
            fileScanner = FileScanner(this)
            initializeMediaSession()
            createNotificationChannel()
            registerMediaControlReceiver()
        } catch (e: Exception) {
            android.util.Log.e("MainActivity", "Error initializing components", e)
        }

        createMusicPlayerUI()
        checkPermissionsAndLoadMusic()
    }

    private fun createMusicPlayerUI() {
        // Main container with Spotify-like dark theme
        val mainContainer = android.widget.LinearLayout(this).apply {
            orientation = android.widget.LinearLayout.VERTICAL
            setBackgroundColor(getColor(R.color.spotify_dark_gray))
        }

        // Top header with greeting
        val headerContainer = createSpotifyHeader()

        // Main content scroll view
        val scrollView = android.widget.ScrollView(this).apply {
            layoutParams = android.widget.LinearLayout.LayoutParams(
                android.widget.LinearLayout.LayoutParams.MATCH_PARENT,
                0, 1f
            )
            setBackgroundColor(getColor(R.color.spotify_dark_gray))
        }

        val contentLayout = android.widget.LinearLayout(this).apply {
            orientation = android.widget.LinearLayout.VERTICAL
            setPadding(16, 0, 16, 100) // Bottom padding for mini player
        }

        // Quick access buttons (like Spotify)
        contentLayout.addView(createQuickAccessButtons())

        // Recently played section
        contentLayout.addView(createSectionHeader("Recently played"))
        contentLayout.addView(createRecentlyPlayedGrid())

        // Made for you section
        contentLayout.addView(createSectionHeader("Made for you"))
        contentLayout.addView(createMadeForYouSection())

        // Your library section
        contentLayout.addView(createSectionHeader("Your Library"))
        contentLayout.addView(createYourLibrarySection())

        scrollView.addView(contentLayout)

        // Bottom mini player (always visible)
        val miniPlayer = createMiniPlayer()

        // Bottom navigation
        val bottomNav = createBottomNavigation()

        mainContainer.addView(headerContainer)
        mainContainer.addView(scrollView)
        mainContainer.addView(miniPlayer)
        mainContainer.addView(bottomNav)

        setContentView(mainContainer)
    }

    private fun createSpotifyHeader(): android.view.View {
        val headerLayout = android.widget.LinearLayout(this).apply {
            orientation = android.widget.LinearLayout.VERTICAL
            setBackgroundColor(getColor(R.color.spotify_black))
            setPadding(16, 48, 16, 16)
        }

        // Greeting text
        val greetingText = android.widget.TextView(this).apply {
            text = "Good ${getTimeGreeting()}"
            textSize = 24f
            setTextColor(getColor(R.color.spotify_white))
            typeface = android.graphics.Typeface.DEFAULT_BOLD
        }

        // Status text
        statusTextView = android.widget.TextView(this).apply {
            text = "Ready to play your music"
            textSize = 14f
            setTextColor(getColor(R.color.spotify_light_gray))
            setPadding(0, 8, 0, 0)
        }

        headerLayout.addView(greetingText)
        headerLayout.addView(statusTextView)

        return headerLayout
    }

    private fun getTimeGreeting(): String {
        val hour = java.util.Calendar.getInstance().get(java.util.Calendar.HOUR_OF_DAY)
        return when (hour) {
            in 5..11 -> "morning"
            in 12..16 -> "afternoon"
            in 17..21 -> "evening"
            else -> "night"
        }
    }

    private fun createQuickAccessButtons(): android.view.View {
        val container = android.widget.LinearLayout(this).apply {
            orientation = android.widget.LinearLayout.VERTICAL
            setPadding(0, 16, 0, 24)
        }

        // First row
        val row1 = android.widget.LinearLayout(this).apply {
            orientation = android.widget.LinearLayout.HORIZONTAL
            setPadding(0, 0, 0, 8)
        }

        row1.addView(createQuickAccessButton("📁 Browse Files", true) { selectMusicFolder() })
        row1.addView(createSpacer(8))
        row1.addView(createQuickAccessButton("🔍 Scan Library", false) { scanForMusic() })

        // Second row
        val row2 = android.widget.LinearLayout(this).apply {
            orientation = android.widget.LinearLayout.HORIZONTAL
        }

        row2.addView(createQuickAccessButton("❤️ Liked Songs", false) { /* TODO */ })
        row2.addView(createSpacer(8))
        row2.addView(createQuickAccessButton("📱 Downloaded", false) { /* TODO */ })

        container.addView(row1)
        container.addView(row2)

        return container
    }

    private fun createQuickAccessButton(text: String, isPrimary: Boolean, onClick: () -> Unit): android.widget.Button {
        return android.widget.Button(this).apply {
            this.text = text
            textSize = 13f
            setPadding(16, 12, 16, 12)

            setBackgroundColor(
                if (isPrimary) getColor(R.color.spotify_green)
                else getColor(R.color.spotify_gray)
            )
            setTextColor(getColor(R.color.spotify_white))

            setOnClickListener { onClick() }

            layoutParams = android.widget.LinearLayout.LayoutParams(
                0, android.widget.LinearLayout.LayoutParams.WRAP_CONTENT, 1f
            )
        }
    }

    private fun createSectionHeader(title: String): android.view.View {
        return android.widget.TextView(this).apply {
            text = title
            textSize = 22f
            setTextColor(getColor(R.color.spotify_white))
            typeface = android.graphics.Typeface.DEFAULT_BOLD
            setPadding(0, 24, 0, 16)
        }
    }

    private fun createRecentlyPlayedGrid(): android.view.View {
        val gridContainer = android.widget.LinearLayout(this).apply {
            orientation = android.widget.LinearLayout.VERTICAL
            setPadding(0, 0, 0, 16)
        }

        val recentSongs = songs.take(6)

        if (recentSongs.isEmpty()) {
            gridContainer.addView(createEmptyStateCard("Start playing music to see your recent tracks"))
            return gridContainer
        }

        // Create 2x3 grid
        for (i in recentSongs.indices step 2) {
            val rowLayout = android.widget.LinearLayout(this).apply {
                orientation = android.widget.LinearLayout.HORIZONTAL
                setPadding(0, 0, 0, 8)
            }

            rowLayout.addView(createRecentPlayedCard(recentSongs[i], songs.indexOf(recentSongs[i])))

            if (i + 1 < recentSongs.size) {
                rowLayout.addView(createSpacer(8))
                rowLayout.addView(createRecentPlayedCard(recentSongs[i + 1], songs.indexOf(recentSongs[i + 1])))
            }

            gridContainer.addView(rowLayout)
        }

        return gridContainer
    }

    private fun createRecentPlayedCard(song: Song, index: Int): android.view.View {
        val cardLayout = android.widget.LinearLayout(this).apply {
            orientation = android.widget.LinearLayout.HORIZONTAL
            setBackgroundColor(getColor(R.color.spotify_gray))
            gravity = android.view.Gravity.CENTER_VERTICAL
            isClickable = true
            isFocusable = true
            setOnClickListener { playSong(index) }

            layoutParams = android.widget.LinearLayout.LayoutParams(
                0, 56, 1f
            )
        }

        // Album art placeholder
        val albumArt = android.widget.TextView(this).apply {
            text = "🎵"
            textSize = 16f
            gravity = android.view.Gravity.CENTER
            setBackgroundColor(getColor(R.color.spotify_black))
            setTextColor(getColor(R.color.spotify_white))
            layoutParams = android.widget.LinearLayout.LayoutParams(56, 56)
        }

        // Song title
        val titleView = android.widget.TextView(this).apply {
            text = song.title
            textSize = 13f
            setTextColor(getColor(R.color.spotify_white))
            typeface = android.graphics.Typeface.DEFAULT_BOLD
            maxLines = 1
            ellipsize = android.text.TextUtils.TruncateAt.END
            setPadding(12, 0, 12, 0)

            layoutParams = android.widget.LinearLayout.LayoutParams(
                0, android.widget.LinearLayout.LayoutParams.WRAP_CONTENT, 1f
            )
        }

        cardLayout.addView(albumArt)
        cardLayout.addView(titleView)

        return cardLayout
    }

    private fun createMadeForYouSection(): android.view.View {
        val horizontalScrollView = android.widget.HorizontalScrollView(this)
        val container = android.widget.LinearLayout(this).apply {
            orientation = android.widget.LinearLayout.HORIZONTAL
            setPadding(0, 0, 0, 16)
        }

        val playlists = listOf(
            "Daily Mix 1" to "Made for you",
            "Discover Weekly" to "Your weekly mixtape",
            "Release Radar" to "New music from artists you follow"
        )

        playlists.forEachIndexed { index, (title, subtitle) ->
            container.addView(createPlaylistCard(title, subtitle))
            if (index < playlists.size - 1) {
                container.addView(createSpacer(12))
            }
        }

        horizontalScrollView.addView(container)
        return horizontalScrollView
    }

    private fun createYourLibrarySection(): android.view.View {
        val container = android.widget.LinearLayout(this).apply {
            orientation = android.widget.LinearLayout.VERTICAL
            setPadding(0, 0, 0, 16)
        }

        if (songs.isEmpty()) {
            container.addView(createEmptyStateCard("No music in your library\nAdd music to get started"))
            return container
        }

        // Show first few songs as library items
        songs.take(5).forEachIndexed { index, song ->
            container.addView(createLibraryItem(song, index))
        }

        return container
    }

    private fun createPlaylistCard(title: String, subtitle: String): android.view.View {
        val cardLayout = android.widget.LinearLayout(this).apply {
            orientation = android.widget.LinearLayout.VERTICAL
            setBackgroundColor(getColor(R.color.music_card_background))
            setPadding(12, 12, 12, 12)
            isClickable = true
            isFocusable = true

            layoutParams = android.widget.LinearLayout.LayoutParams(140, 180)
        }

        // Playlist cover
        val coverView = android.widget.TextView(this).apply {
            text = "🎶"
            textSize = 32f
            gravity = android.view.Gravity.CENTER
            setBackgroundColor(getColor(R.color.spotify_green))
            setTextColor(getColor(R.color.spotify_white))
            layoutParams = android.widget.LinearLayout.LayoutParams(116, 116)
        }

        // Title
        val titleView = android.widget.TextView(this).apply {
            text = title
            textSize = 14f
            setTextColor(getColor(R.color.spotify_white))
            typeface = android.graphics.Typeface.DEFAULT_BOLD
            maxLines = 2
            ellipsize = android.text.TextUtils.TruncateAt.END
            setPadding(0, 8, 0, 4)
        }

        // Subtitle
        val subtitleView = android.widget.TextView(this).apply {
            text = subtitle
            textSize = 12f
            setTextColor(getColor(R.color.spotify_light_gray))
            maxLines = 2
            ellipsize = android.text.TextUtils.TruncateAt.END
        }

        cardLayout.addView(coverView)
        cardLayout.addView(titleView)
        cardLayout.addView(subtitleView)

        return cardLayout
    }

    private fun createLibraryItem(song: Song, index: Int): android.view.View {
        val itemLayout = android.widget.LinearLayout(this).apply {
            orientation = android.widget.LinearLayout.HORIZONTAL
            setPadding(0, 8, 0, 8)
            gravity = android.view.Gravity.CENTER_VERTICAL
            isClickable = true
            isFocusable = true
            setOnClickListener { playSong(index) }
        }

        // Album art
        val albumArt = android.widget.TextView(this).apply {
            text = "🎵"
            textSize = 16f
            gravity = android.view.Gravity.CENTER
            setBackgroundColor(getColor(R.color.spotify_gray))
            setTextColor(getColor(R.color.spotify_white))
            layoutParams = android.widget.LinearLayout.LayoutParams(48, 48)
        }

        // Song info
        val infoLayout = android.widget.LinearLayout(this).apply {
            orientation = android.widget.LinearLayout.VERTICAL
            setPadding(12, 0, 12, 0)
            layoutParams = android.widget.LinearLayout.LayoutParams(
                0, android.widget.LinearLayout.LayoutParams.WRAP_CONTENT, 1f
            )
        }

        val titleView = android.widget.TextView(this).apply {
            text = song.title
            textSize = 16f
            setTextColor(getColor(R.color.spotify_white))
            maxLines = 1
            ellipsize = android.text.TextUtils.TruncateAt.END
        }

        val artistView = android.widget.TextView(this).apply {
            text = song.artist
            textSize = 14f
            setTextColor(getColor(R.color.spotify_light_gray))
            maxLines = 1
            ellipsize = android.text.TextUtils.TruncateAt.END
            setPadding(0, 2, 0, 0)
        }

        // More options button
        val moreButton = android.widget.TextView(this).apply {
            text = "⋮"
            textSize = 20f
            setTextColor(getColor(R.color.spotify_light_gray))
            setPadding(12, 12, 12, 12)
        }

        infoLayout.addView(titleView)
        infoLayout.addView(artistView)

        itemLayout.addView(albumArt)
        itemLayout.addView(infoLayout)
        itemLayout.addView(moreButton)

        return itemLayout
    }

    private fun createEmptyStateCard(message: String): android.view.View {
        return android.widget.TextView(this).apply {
            text = message
            textSize = 16f
            setTextColor(getColor(R.color.spotify_light_gray))
            gravity = android.view.Gravity.CENTER
            setPadding(32, 48, 32, 48)
            setBackgroundColor(getColor(R.color.music_card_background))
        }
    }

    private fun createMiniPlayer(): android.view.View {
        val playerContainer = android.widget.LinearLayout(this).apply {
            orientation = android.widget.LinearLayout.HORIZONTAL
            setBackgroundColor(getColor(R.color.spotify_gray))
            setPadding(12, 8, 12, 8)
            gravity = android.view.Gravity.CENTER_VERTICAL
            elevation = 8f
        }

        // Album art
        val albumArt = android.widget.TextView(this).apply {
            text = "🎵"
            textSize = 16f
            gravity = android.view.Gravity.CENTER
            setBackgroundColor(getColor(R.color.spotify_black))
            setTextColor(getColor(R.color.spotify_white))
            layoutParams = android.widget.LinearLayout.LayoutParams(48, 48)
        }

        // Song info
        val infoLayout = android.widget.LinearLayout(this).apply {
            orientation = android.widget.LinearLayout.VERTICAL
            setPadding(12, 0, 12, 0)
            layoutParams = android.widget.LinearLayout.LayoutParams(
                0, android.widget.LinearLayout.LayoutParams.WRAP_CONTENT, 1f
            )
        }

        currentSongTextView = android.widget.TextView(this).apply {
            text = "No song playing"
            textSize = 14f
            setTextColor(getColor(R.color.spotify_white))
            maxLines = 1
            ellipsize = android.text.TextUtils.TruncateAt.END
        }

        val artistTextView = android.widget.TextView(this).apply {
            text = ""
            textSize = 12f
            setTextColor(getColor(R.color.spotify_light_gray))
            maxLines = 1
            ellipsize = android.text.TextUtils.TruncateAt.END
        }

        infoLayout.addView(currentSongTextView)
        infoLayout.addView(artistTextView)

        // Control buttons
        val controlsLayout = android.widget.LinearLayout(this).apply {
            orientation = android.widget.LinearLayout.HORIZONTAL
            gravity = android.view.Gravity.CENTER_VERTICAL
        }

        val prevButton = createMiniPlayerButton("⏮") { previousSong() }
        playPauseButton = createMiniPlayerButton("▶") { togglePlayback() }
        val nextButton = createMiniPlayerButton("⏭") { nextSong() }

        controlsLayout.addView(prevButton)
        controlsLayout.addView(playPauseButton)
        controlsLayout.addView(nextButton)

        playerContainer.addView(albumArt)
        playerContainer.addView(infoLayout)
        playerContainer.addView(controlsLayout)

        return playerContainer
    }

    private fun createMiniPlayerButton(text: String, onClick: () -> Unit): android.widget.Button {
        return android.widget.Button(this).apply {
            this.text = text
            textSize = 16f
            setBackgroundColor(android.graphics.Color.TRANSPARENT)
            setTextColor(getColor(R.color.spotify_white))
            setPadding(8, 8, 8, 8)
            setOnClickListener { onClick() }

            layoutParams = android.widget.LinearLayout.LayoutParams(40, 40)
        }
    }

    private fun createBottomNavigation(): android.view.View {
        val navContainer = android.widget.LinearLayout(this).apply {
            orientation = android.widget.LinearLayout.HORIZONTAL
            setBackgroundColor(getColor(R.color.spotify_black))
            setPadding(0, 8, 0, 8)
        }

        val navItems = listOf(
            "🏠" to "Home",
            "🔍" to "Search",
            "📚" to "Your Library",
            "⚙️" to "Settings"
        )

        navItems.forEachIndexed { index, (icon, label) ->
            navContainer.addView(createNavItem(icon, label, index == 0))
        }

        return navContainer
    }

    private fun createNavItem(icon: String, label: String, isSelected: Boolean): android.view.View {
        val itemLayout = android.widget.LinearLayout(this).apply {
            orientation = android.widget.LinearLayout.VERTICAL
            gravity = android.view.Gravity.CENTER
            setPadding(8, 8, 8, 8)
            isClickable = true
            isFocusable = true

            layoutParams = android.widget.LinearLayout.LayoutParams(
                0, android.widget.LinearLayout.LayoutParams.WRAP_CONTENT, 1f
            )
        }

        val iconView = android.widget.TextView(this).apply {
            text = icon
            textSize = 20f
            gravity = android.view.Gravity.CENTER
            setTextColor(
                if (isSelected) getColor(R.color.spotify_white)
                else getColor(R.color.spotify_light_gray)
            )
        }

        val labelView = android.widget.TextView(this).apply {
            text = label
            textSize = 10f
            gravity = android.view.Gravity.CENTER
            setTextColor(
                if (isSelected) getColor(R.color.spotify_white)
                else getColor(R.color.spotify_light_gray)
            )
            setPadding(0, 4, 0, 0)
        }

        itemLayout.addView(iconView)
        itemLayout.addView(labelView)

        return itemLayout
    }

    private fun createSpacer(width: Int): android.view.View {
        return android.view.View(this).apply {
            layoutParams = android.widget.LinearLayout.LayoutParams(width, 1)
        }
    }

    // Old methods removed - replaced with Spotify-style UI above

    // Remove old UI methods - they are replaced by the new design above

    private fun initializeMediaSession() {
        mediaSession = MediaSessionCompat(this, "MinimalMusicPlayer").apply {
            setCallback(object : MediaSessionCompat.Callback() {
                override fun onPlay() {
                    if (currentSong != null) {
                        togglePlayback()
                    }
                }

                override fun onPause() {
                    if (isCurrentlyPlaying) {
                        togglePlayback()
                    }
                }

                override fun onSkipToNext() {
                    nextSong()
                }

                override fun onSkipToPrevious() {
                    previousSong()
                }
            })
            isActive = true
        }
    }

    private fun createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channel = NotificationChannel(
                CHANNEL_ID,
                "Music Playback",
                NotificationManager.IMPORTANCE_LOW
            ).apply {
                description = "Controls for music playback"
                setShowBadge(false)
            }

            notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
            notificationManager.createNotificationChannel(channel)
        } else {
            notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
        }
    }

    private fun registerMediaControlReceiver() {
        val filter = IntentFilter().apply {
            addAction(ACTION_PLAY_PAUSE)
            addAction(ACTION_NEXT)
            addAction(ACTION_PREVIOUS)
            // Widget broadcasts
            addAction("com.minimalmusicplayer.WIDGET_PLAY_PAUSE")
            addAction("com.minimalmusicplayer.WIDGET_NEXT")
            addAction("com.minimalmusicplayer.WIDGET_PREVIOUS")
        }
        registerReceiver(mediaControlReceiver, filter)
    }

    private fun createMediaNotification() {
        val song = currentSong ?: return

        // Create pending intents for notification actions
        val playPauseIntent = PendingIntent.getBroadcast(
            this, 0, Intent(ACTION_PLAY_PAUSE),
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )

        val nextIntent = PendingIntent.getBroadcast(
            this, 1, Intent(ACTION_NEXT),
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )

        val previousIntent = PendingIntent.getBroadcast(
            this, 2, Intent(ACTION_PREVIOUS),
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )

        // Create content intent to open the app
        val contentIntent = PendingIntent.getActivity(
            this, 0, Intent(this, MainActivity::class.java),
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )

        // Build notification
        val notification = NotificationCompat.Builder(this, CHANNEL_ID)
            .setContentTitle(song.title)
            .setContentText(song.artist)
            .setSmallIcon(R.drawable.ic_music_note)
            .setContentIntent(contentIntent)
            .setDeleteIntent(PendingIntent.getBroadcast(
                this, 3, Intent(ACTION_PLAY_PAUSE),
                PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
            ))
            .setVisibility(NotificationCompat.VISIBILITY_PUBLIC)
            .setPriority(NotificationCompat.PRIORITY_LOW)
            .addAction(
                R.drawable.ic_skip_previous,
                "Previous",
                previousIntent
            )
            .addAction(
                if (isCurrentlyPlaying) R.drawable.ic_pause else R.drawable.ic_play,
                if (isCurrentlyPlaying) "Pause" else "Play",
                playPauseIntent
            )
            .addAction(
                R.drawable.ic_skip_next,
                "Next",
                nextIntent
            )
            .setStyle(androidx.media.app.NotificationCompat.MediaStyle()
                .setMediaSession(mediaSession.sessionToken)
                .setShowActionsInCompactView(0, 1, 2)
            )
            .build()

        notificationManager.notify(NOTIFICATION_ID, notification)
    }

    private fun updateMediaSession() {
        val song = currentSong ?: return

        val metadata = MediaMetadataCompat.Builder()
            .putString(MediaMetadataCompat.METADATA_KEY_TITLE, song.title)
            .putString(MediaMetadataCompat.METADATA_KEY_ARTIST, song.artist)
            .putString(MediaMetadataCompat.METADATA_KEY_ALBUM, song.album ?: "Unknown Album")
            .putLong(MediaMetadataCompat.METADATA_KEY_DURATION, song.duration)
            .build()

        mediaSession.setMetadata(metadata)

        val state = if (isCurrentlyPlaying) {
            PlaybackStateCompat.STATE_PLAYING
        } else {
            PlaybackStateCompat.STATE_PAUSED
        }

        val playbackState = PlaybackStateCompat.Builder()
            .setState(state, PlaybackStateCompat.PLAYBACK_POSITION_UNKNOWN, 1.0f)
            .setActions(
                PlaybackStateCompat.ACTION_PLAY or
                PlaybackStateCompat.ACTION_PAUSE or
                PlaybackStateCompat.ACTION_SKIP_TO_NEXT or
                PlaybackStateCompat.ACTION_SKIP_TO_PREVIOUS
            )
            .build()

        mediaSession.setPlaybackState(playbackState)
    }

    private fun updateWidget() {
        val song = currentSong
        MusicWidgetProvider.updateWidget(
            this,
            song?.title,
            song?.artist,
            isCurrentlyPlaying
        )
    }

    private fun checkPermissionsAndLoadMusic() {
        if (PermissionHelper.hasStoragePermission(this)) {
            statusTextView.text = "Ready to scan for music"
            scanForMusic()
        } else {
            statusTextView.text = "Storage permission required"
            PermissionHelper.requestStoragePermission(this)
        }
    }

    private fun scanForMusic() {
        statusTextView.text = "Scanning for music files..."

        lifecycleScope.launch {
            try {
                // Get music files from common directories
                val musicFiles = fileScanner.scanMediaStore()
                songs.clear()
                songs.addAll(musicFiles)

                updateSongList()
                statusTextView.text = "Found ${songs.size} music files"

            } catch (e: Exception) {
                statusTextView.text = "Error scanning: ${e.message}"
                android.util.Log.e("MainActivity", "Error scanning music", e)
            }
        }
    }

    private fun selectMusicFolder() {
        try {
            directoryPickerLauncher.launch(null)
        } catch (e: Exception) {
            statusTextView.text = "Error opening folder picker: ${e.message}"
            android.util.Log.e("MainActivity", "Error opening folder picker", e)
        }
    }

    private fun scanSelectedDirectory(directoryUri: Uri) {
        statusTextView.text = "Scanning selected folder..."

        lifecycleScope.launch {
            try {
                val musicFiles = fileScanner.scanDirectory(directoryUri)
                songs.clear()
                songs.addAll(musicFiles)

                updateSongList()
                statusTextView.text = "Found ${songs.size} music files in selected folder"

            } catch (e: Exception) {
                statusTextView.text = "Error scanning folder: ${e.message}"
                android.util.Log.e("MainActivity", "Error scanning selected directory", e)
            }
        }
    }

    private fun updateSongList() {
        updateSpotifyUI()
    }

    private fun updateSpotifyUI() {
        // Update mini player
        val song = currentSong
        if (song != null) {
            currentSongTextView.text = "${song.title} • ${song.artist}"
            playPauseButton.text = if (isCurrentlyPlaying) "⏸" else "▶"
        } else {
            currentSongTextView.text = "No song playing"
            playPauseButton.text = "▶"
        }

        // Update status in header
        statusTextView.text = when {
            song != null && isCurrentlyPlaying -> "Now playing: ${song.title}"
            song != null -> "Paused: ${song.title}"
            songs.isNotEmpty() -> "${songs.size} songs in your library"
            else -> "Add music to get started"
        }
    }

    private fun formatDuration(duration: Long): String {
        val minutes = duration / 1000 / 60
        val seconds = (duration / 1000) % 60
        return String.format("%d:%02d", minutes, seconds)
    }

    private fun playSong(index: Int) {
        if (index >= 0 && index < songs.size) {
            currentSongIndex = index
            currentSong = songs[index]

            try {
                // Stop current playback
                stopCurrentPlayback()

                // Create new MediaPlayer
                mediaPlayer = MediaPlayer().apply {
                    setDataSource(this@MainActivity, Uri.parse(currentSong!!.path))
                    setOnPreparedListener { mp ->
                        mp.start()
                        isCurrentlyPlaying = true
                        playPauseButton.text = "⏸️"
                        currentSongTextView.text = "Playing: ${currentSong?.title} - ${currentSong?.artist}"

                        // Update media session, notification and widget
                        updateMediaSession()
                        createMediaNotification()
                        updateWidget()
                        updateSpotifyUI()

                        android.widget.Toast.makeText(this@MainActivity, "♪ ${currentSong?.title}", android.widget.Toast.LENGTH_SHORT).show()
                    }
                    setOnCompletionListener {
                        // Auto play next song
                        nextSong()
                    }
                    setOnErrorListener { _, what, extra ->
                        android.widget.Toast.makeText(this@MainActivity, "Playback error: $what", android.widget.Toast.LENGTH_SHORT).show()
                        android.util.Log.e("MainActivity", "MediaPlayer error: what=$what, extra=$extra")
                        true
                    }
                    prepareAsync()
                }

            } catch (e: Exception) {
                android.widget.Toast.makeText(this, "Error playing song: ${e.message}", android.widget.Toast.LENGTH_SHORT).show()
                android.util.Log.e("MainActivity", "Error playing song", e)
            }
        }
    }

    private fun stopCurrentPlayback() {
        try {
            mediaPlayer?.let { mp ->
                if (mp.isPlaying) {
                    mp.stop()
                }
                mp.release()
            }
            mediaPlayer = null
            isCurrentlyPlaying = false

            // Clear notification when stopping
            notificationManager.cancel(NOTIFICATION_ID)
        } catch (e: Exception) {
            android.util.Log.e("MainActivity", "Error stopping playback", e)
        }
    }

    private fun togglePlayback() {
        try {
            mediaPlayer?.let { mp ->
                if (isCurrentlyPlaying) {
                    mp.pause()
                    isCurrentlyPlaying = false
                    playPauseButton.text = "▶️"

                    // Update media session, notification and widget
                    updateMediaSession()
                    createMediaNotification()
                    updateWidget()
                    updateSpotifyUI()

                    android.widget.Toast.makeText(this, "⏸ Paused", android.widget.Toast.LENGTH_SHORT).show()
                } else {
                    mp.start()
                    isCurrentlyPlaying = true
                    playPauseButton.text = "⏸"

                    // Update media session, notification and widget
                    updateMediaSession()
                    createMediaNotification()
                    updateWidget()
                    updateSpotifyUI()

                    android.widget.Toast.makeText(this, "▶ Playing", android.widget.Toast.LENGTH_SHORT).show()
                }
            } ?: run {
                if (currentSong != null) {
                    // Resume current song
                    playSong(currentSongIndex)
                } else {
                    android.widget.Toast.makeText(this, "No song selected", android.widget.Toast.LENGTH_SHORT).show()
                }
            }
        } catch (e: Exception) {
            android.widget.Toast.makeText(this, "Playback error: ${e.message}", android.widget.Toast.LENGTH_SHORT).show()
            android.util.Log.e("MainActivity", "Error toggling playback", e)
        }
    }

    private fun previousSong() {
        if (currentSongIndex > 0) {
            playSong(currentSongIndex - 1)
        }
    }

    private fun nextSong() {
        if (currentSongIndex < songs.size - 1) {
            playSong(currentSongIndex + 1)
        }
    }

    override fun onRequestPermissionsResult(
        requestCode: Int,
        permissions: Array<out String>,
        grantResults: IntArray
    ) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)

        if (requestCode == PermissionHelper.STORAGE_PERMISSION_REQUEST_CODE) {
            if (grantResults.isNotEmpty() && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                statusTextView.text = "Permission granted! Ready to scan."
                scanForMusic()
            } else {
                statusTextView.text = "Storage permission denied. Cannot access music files."
            }
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        stopCurrentPlayback()

        // Clean up media session and receiver
        try {
            mediaSession.release()
            unregisterReceiver(mediaControlReceiver)
            notificationManager.cancel(NOTIFICATION_ID)
        } catch (e: Exception) {
            android.util.Log.e("MainActivity", "Error cleaning up resources", e)
        }
    }

    override fun onPause() {
        super.onPause()
        // Optionally pause playback when app goes to background
        // mediaPlayer?.pause()
        // isCurrentlyPlaying = false
        // playPauseButton.text = "▶️"
    }
}
