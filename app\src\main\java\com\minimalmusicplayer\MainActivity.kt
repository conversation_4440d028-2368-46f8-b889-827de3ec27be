package com.minimalmusicplayer

import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.content.pm.PackageManager
import android.graphics.BitmapFactory
import android.media.MediaPlayer
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.support.v4.media.MediaMetadataCompat
import android.support.v4.media.session.MediaSessionCompat
import android.support.v4.media.session.PlaybackStateCompat
import androidx.activity.result.contract.ActivityResultContracts
import androidx.appcompat.app.AppCompatActivity
import androidx.core.app.NotificationCompat
import androidx.documentfile.provider.DocumentFile
import androidx.lifecycle.lifecycleScope
import com.minimalmusicplayer.model.Song
import com.minimalmusicplayer.util.FileScanner
import com.minimalmusicplayer.util.PermissionHelper
import com.minimalmusicplayer.util.PreferenceManager
import com.minimalmusicplayer.widget.MusicWidgetProvider
import kotlinx.coroutines.launch

class MainActivity : AppCompatActivity() {

    private lateinit var fileScanner: FileScanner
    private lateinit var preferenceManager: PreferenceManager

    // UI components
    private lateinit var statusTextView: android.widget.TextView
    private lateinit var songListLayout: android.widget.LinearLayout
    private lateinit var playPauseButton: android.widget.Button
    private lateinit var currentSongTextView: android.widget.TextView

    // Music data
    private var songs = mutableListOf<Song>()
    private var currentSongIndex = -1
    private var currentSong: Song? = null

    // MediaPlayer for actual audio playback
    private var mediaPlayer: MediaPlayer? = null
    private var isCurrentlyPlaying = false

    // Media session and notification
    private lateinit var mediaSession: MediaSessionCompat
    private lateinit var notificationManager: NotificationManager

    companion object {
        const val NOTIFICATION_ID = 1
        const val CHANNEL_ID = "music_playback"
        const val ACTION_PLAY_PAUSE = "com.minimalmusicplayer.PLAY_PAUSE"
        const val ACTION_NEXT = "com.minimalmusicplayer.NEXT"
        const val ACTION_PREVIOUS = "com.minimalmusicplayer.PREVIOUS"
    }

    // Directory selection
    private val directoryPickerLauncher = registerForActivityResult(
        ActivityResultContracts.OpenDocumentTree()
    ) { uri ->
        uri?.let { selectedUri ->
            // Grant persistent permission
            contentResolver.takePersistableUriPermission(
                selectedUri,
                android.content.Intent.FLAG_GRANT_READ_URI_PERMISSION
            )
            scanSelectedDirectory(selectedUri)
        }
    }

    // Broadcast receiver for notification and widget controls
    private val mediaControlReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context?, intent: Intent?) {
            when (intent?.action) {
                ACTION_PLAY_PAUSE, "com.minimalmusicplayer.WIDGET_PLAY_PAUSE" -> togglePlayback()
                ACTION_NEXT, "com.minimalmusicplayer.WIDGET_NEXT" -> nextSong()
                ACTION_PREVIOUS, "com.minimalmusicplayer.WIDGET_PREVIOUS" -> previousSong()
            }
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        // Initialize components
        try {
            preferenceManager = PreferenceManager(this)
            fileScanner = FileScanner(this)
            initializeMediaSession()
            createNotificationChannel()
            registerMediaControlReceiver()
        } catch (e: Exception) {
            android.util.Log.e("MainActivity", "Error initializing components", e)
        }

        createMusicPlayerUI()
        checkPermissionsAndLoadMusic()
    }

    private fun createMusicPlayerUI() {
        // Create main container with dark theme
        val mainContainer = android.widget.LinearLayout(this).apply {
            orientation = android.widget.LinearLayout.VERTICAL
            setBackgroundColor(getColor(R.color.music_background))
        }

        // Content scroll view
        val scrollView = android.widget.ScrollView(this).apply {
            layoutParams = android.widget.LinearLayout.LayoutParams(
                android.widget.LinearLayout.LayoutParams.MATCH_PARENT,
                0, 1f
            )
        }

        val contentLayout = android.widget.LinearLayout(this).apply {
            orientation = android.widget.LinearLayout.VERTICAL
            setPadding(20, 20, 20, 20)
        }

        // Recently Played section
        contentLayout.addView(createSectionTitle("Recently Played:"))
        contentLayout.addView(createRecentlyPlayedSection())

        // Recently Added section
        contentLayout.addView(createSectionTitle("Recently Added:"))
        contentLayout.addView(createRecentlyAddedSection())

        // Most Played section
        contentLayout.addView(createSectionTitle("Most Played:"))
        contentLayout.addView(createMostPlayedSection())

        // Action buttons
        contentLayout.addView(createActionButtons())

        // Status text
        statusTextView = android.widget.TextView(this).apply {
            text = "Ready to discover music"
            textSize = 14f
            setTextColor(getColor(R.color.music_on_surface_variant))
            gravity = android.view.Gravity.CENTER
            setPadding(20, 20, 20, 20)
        }
        contentLayout.addView(statusTextView)

        scrollView.addView(contentLayout)

        // Bottom navigation and player
        val bottomContainer = createBottomContainer()

        mainContainer.addView(scrollView)
        mainContainer.addView(bottomContainer)

        setContentView(mainContainer)
    }

    private fun createSectionTitle(title: String): android.view.View {
        return android.widget.TextView(this).apply {
            text = title
            textSize = 20f
            setTextColor(getColor(R.color.music_on_surface))
            typeface = android.graphics.Typeface.DEFAULT_BOLD
            setPadding(0, 20, 0, 16)
        }
    }

    private fun createRecentlyPlayedSection(): android.view.View {
        val horizontalScrollView = android.widget.HorizontalScrollView(this)
        val container = android.widget.LinearLayout(this).apply {
            orientation = android.widget.LinearLayout.HORIZONTAL
            setPadding(0, 0, 0, 20)
        }

        // Add recent songs as album cards
        val recentSongs = songs.take(5)
        if (recentSongs.isEmpty()) {
            container.addView(createEmptyCard("No recent music"))
        } else {
            recentSongs.forEachIndexed { index, song ->
                container.addView(createAlbumCard(song, index))
                if (index < recentSongs.size - 1) {
                    container.addView(createSpacer(16))
                }
            }
        }

        horizontalScrollView.addView(container)
        return horizontalScrollView
    }

    private fun createRecentlyAddedSection(): android.view.View {
        val horizontalScrollView = android.widget.HorizontalScrollView(this)
        val container = android.widget.LinearLayout(this).apply {
            orientation = android.widget.LinearLayout.HORIZONTAL
            setPadding(0, 0, 0, 20)
        }

        // Add recently added songs
        val recentlyAdded = songs.takeLast(5).reversed()
        if (recentlyAdded.isEmpty()) {
            container.addView(createEmptyCard("No music added"))
        } else {
            recentlyAdded.forEachIndexed { index, song ->
                container.addView(createAlbumCard(song, songs.indexOf(song)))
                if (index < recentlyAdded.size - 1) {
                    container.addView(createSpacer(16))
                }
            }
        }

        horizontalScrollView.addView(container)
        return horizontalScrollView
    }

    private fun createMostPlayedSection(): android.view.View {
        val container = android.widget.LinearLayout(this).apply {
            orientation = android.widget.LinearLayout.VERTICAL
            setPadding(0, 0, 0, 20)
        }

        // Show current playing song or first song
        val mostPlayedSong = if (currentSong != null) currentSong!! else songs.firstOrNull()
        if (mostPlayedSong != null) {
            container.addView(createCurrentPlayingCard(mostPlayedSong, currentSongIndex))
        } else {
            container.addView(createEmptyCard("No music to display"))
        }

        return container
    }

    private fun createAlbumCard(song: Song, index: Int): android.view.View {
        val cardLayout = android.widget.LinearLayout(this).apply {
            orientation = android.widget.LinearLayout.VERTICAL
            setBackgroundColor(getColor(R.color.music_surface))
            setPadding(12, 12, 12, 12)
            isClickable = true
            isFocusable = true
            setOnClickListener { playSong(index) }

            layoutParams = android.widget.LinearLayout.LayoutParams(120, 160)
        }

        // Album art placeholder
        val albumArt = android.widget.TextView(this).apply {
            text = "🎵"
            textSize = 32f
            gravity = android.view.Gravity.CENTER
            setBackgroundColor(getColor(R.color.music_surface_variant))
            layoutParams = android.widget.LinearLayout.LayoutParams(96, 96)
        }

        // Song title
        val titleView = android.widget.TextView(this).apply {
            text = song.title
            textSize = 12f
            setTextColor(getColor(R.color.music_on_surface))
            maxLines = 2
            ellipsize = android.text.TextUtils.TruncateAt.END
            setPadding(0, 8, 0, 2)
        }

        // Artist name
        val artistView = android.widget.TextView(this).apply {
            text = song.artist
            textSize = 10f
            setTextColor(getColor(R.color.music_on_surface_variant))
            maxLines = 1
            ellipsize = android.text.TextUtils.TruncateAt.END
        }

        cardLayout.addView(albumArt)
        cardLayout.addView(titleView)
        cardLayout.addView(artistView)

        return cardLayout
    }

    private fun createCurrentPlayingCard(song: Song, index: Int): android.view.View {
        val cardLayout = android.widget.LinearLayout(this).apply {
            orientation = android.widget.LinearLayout.HORIZONTAL
            setBackgroundColor(getColor(R.color.music_surface))
            setPadding(16, 16, 16, 16)
            gravity = android.view.Gravity.CENTER_VERTICAL
            isClickable = true
            isFocusable = true
            setOnClickListener { playSong(index) }
        }

        // Album art
        val albumArt = android.widget.TextView(this).apply {
            text = "🎵"
            textSize = 24f
            gravity = android.view.Gravity.CENTER
            setBackgroundColor(getColor(R.color.music_surface_variant))
            setPadding(16, 16, 16, 16)
            layoutParams = android.widget.LinearLayout.LayoutParams(64, 64)
        }

        // Song info
        val infoLayout = android.widget.LinearLayout(this).apply {
            orientation = android.widget.LinearLayout.VERTICAL
            setPadding(16, 0, 16, 0)
            layoutParams = android.widget.LinearLayout.LayoutParams(
                0, android.widget.LinearLayout.LayoutParams.WRAP_CONTENT, 1f
            )
        }

        val titleView = android.widget.TextView(this).apply {
            text = song.title
            textSize = 16f
            setTextColor(getColor(R.color.music_on_surface))
            typeface = android.graphics.Typeface.DEFAULT_BOLD
            maxLines = 1
            ellipsize = android.text.TextUtils.TruncateAt.END
        }

        val artistView = android.widget.TextView(this).apply {
            text = song.artist
            textSize = 14f
            setTextColor(getColor(R.color.music_on_surface_variant))
            maxLines = 1
            ellipsize = android.text.TextUtils.TruncateAt.END
            setPadding(0, 4, 0, 0)
        }

        // Play/pause button
        playPauseButton = android.widget.Button(this).apply {
            text = if (isCurrentlyPlaying && currentSongIndex == index) "⏸️" else "▶️"
            textSize = 16f
            setBackgroundColor(getColor(R.color.music_primary))
            setTextColor(getColor(R.color.white))
            setPadding(16, 16, 16, 16)
            setOnClickListener { togglePlayback() }

            layoutParams = android.widget.LinearLayout.LayoutParams(56, 56)
        }

        infoLayout.addView(titleView)
        infoLayout.addView(artistView)

        cardLayout.addView(albumArt)
        cardLayout.addView(infoLayout)
        cardLayout.addView(playPauseButton)

        return cardLayout
    }

    private fun createEmptyCard(message: String): android.view.View {
        return android.widget.TextView(this).apply {
            text = message
            textSize = 14f
            setTextColor(getColor(R.color.music_on_surface_variant))
            gravity = android.view.Gravity.CENTER
            setPadding(40, 40, 40, 40)
            setBackgroundColor(getColor(R.color.music_surface))
            layoutParams = android.widget.LinearLayout.LayoutParams(200, 160)
        }
    }

    private fun createSpacer(width: Int): android.view.View {
        return android.view.View(this).apply {
            layoutParams = android.widget.LinearLayout.LayoutParams(width, 1)
        }
    }

    private fun createActionButtons(): android.view.View {
        val buttonContainer = android.widget.LinearLayout(this).apply {
            orientation = android.widget.LinearLayout.HORIZONTAL
            gravity = android.view.Gravity.CENTER
            setPadding(0, 20, 0, 20)
        }

        val selectFolderButton = createModernButton("📁 Select Folder", true) {
            selectMusicFolder()
        }

        val scanAllButton = createModernButton("🔍 Scan All", false) {
            scanForMusic()
        }

        buttonContainer.addView(selectFolderButton)
        buttonContainer.addView(createSpacer(16))
        buttonContainer.addView(scanAllButton)

        return buttonContainer
    }

    private fun createModernButton(text: String, isPrimary: Boolean, onClick: () -> Unit): android.widget.Button {
        return android.widget.Button(this).apply {
            this.text = text
            textSize = 14f
            setPadding(24, 12, 24, 12)

            if (isPrimary) {
                setBackgroundColor(getColor(R.color.music_primary))
                setTextColor(getColor(R.color.white))
            } else {
                setBackgroundColor(getColor(R.color.music_secondary))
                setTextColor(getColor(R.color.white))
            }

            setOnClickListener { onClick() }

            layoutParams = android.widget.LinearLayout.LayoutParams(
                0, android.widget.LinearLayout.LayoutParams.WRAP_CONTENT, 1f
            )
        }
    }

    private fun createBottomContainer(): android.view.View {
        val container = android.widget.LinearLayout(this).apply {
            orientation = android.widget.LinearLayout.VERTICAL
            setBackgroundColor(getColor(R.color.music_surface))
        }

        // Current song info (mini player)
        currentSongTextView = android.widget.TextView(this).apply {
            text = "No song selected"
            textSize = 12f
            setTextColor(getColor(R.color.music_on_surface))
            gravity = android.view.Gravity.CENTER
            setPadding(20, 12, 20, 12)
            maxLines = 1
            ellipsize = android.text.TextUtils.TruncateAt.END
        }

        // Bottom navigation
        val bottomNav = createBottomNavigation()

        container.addView(currentSongTextView)
        container.addView(bottomNav)

        return container
    }

    private fun createBottomNavigation(): android.view.View {
        val navContainer = android.widget.LinearLayout(this).apply {
            orientation = android.widget.LinearLayout.HORIZONTAL
            setBackgroundColor(getColor(R.color.music_surface_variant))
            setPadding(0, 12, 0, 12)
        }

        // Home button
        val homeButton = createNavButton("🏠", "Home", true)

        // Library button
        val libraryButton = createNavButton("📚", "Library", false)

        // Music note button
        val musicButton = createNavButton("🎵", "Music", false)

        // Settings button
        val settingsButton = createNavButton("⚙️", "Settings", false)

        navContainer.addView(homeButton)
        navContainer.addView(libraryButton)
        navContainer.addView(musicButton)
        navContainer.addView(settingsButton)

        return navContainer
    }

    private fun createNavButton(icon: String, label: String, isSelected: Boolean): android.view.View {
        val buttonLayout = android.widget.LinearLayout(this).apply {
            orientation = android.widget.LinearLayout.VERTICAL
            gravity = android.view.Gravity.CENTER
            setPadding(8, 8, 8, 8)
            isClickable = true
            isFocusable = true

            layoutParams = android.widget.LinearLayout.LayoutParams(
                0, android.widget.LinearLayout.LayoutParams.WRAP_CONTENT, 1f
            )
        }

        val iconView = android.widget.TextView(this).apply {
            text = icon
            textSize = 20f
            gravity = android.view.Gravity.CENTER
        }

        val labelView = android.widget.TextView(this).apply {
            text = label
            textSize = 10f
            gravity = android.view.Gravity.CENTER
            setTextColor(
                if (isSelected) getColor(R.color.music_primary)
                else getColor(R.color.music_on_surface_variant)
            )
            setPadding(0, 4, 0, 0)
        }

        buttonLayout.addView(iconView)
        buttonLayout.addView(labelView)

        return buttonLayout
    }

    // Remove old UI methods - they are replaced by the new design above

    private fun initializeMediaSession() {
        mediaSession = MediaSessionCompat(this, "MinimalMusicPlayer").apply {
            setCallback(object : MediaSessionCompat.Callback() {
                override fun onPlay() {
                    if (currentSong != null) {
                        togglePlayback()
                    }
                }

                override fun onPause() {
                    if (isCurrentlyPlaying) {
                        togglePlayback()
                    }
                }

                override fun onSkipToNext() {
                    nextSong()
                }

                override fun onSkipToPrevious() {
                    previousSong()
                }
            })
            isActive = true
        }
    }

    private fun createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channel = NotificationChannel(
                CHANNEL_ID,
                "Music Playback",
                NotificationManager.IMPORTANCE_LOW
            ).apply {
                description = "Controls for music playback"
                setShowBadge(false)
            }

            notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
            notificationManager.createNotificationChannel(channel)
        } else {
            notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
        }
    }

    private fun registerMediaControlReceiver() {
        val filter = IntentFilter().apply {
            addAction(ACTION_PLAY_PAUSE)
            addAction(ACTION_NEXT)
            addAction(ACTION_PREVIOUS)
            // Widget broadcasts
            addAction("com.minimalmusicplayer.WIDGET_PLAY_PAUSE")
            addAction("com.minimalmusicplayer.WIDGET_NEXT")
            addAction("com.minimalmusicplayer.WIDGET_PREVIOUS")
        }
        registerReceiver(mediaControlReceiver, filter)
    }

    private fun createMediaNotification() {
        val song = currentSong ?: return

        // Create pending intents for notification actions
        val playPauseIntent = PendingIntent.getBroadcast(
            this, 0, Intent(ACTION_PLAY_PAUSE),
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )

        val nextIntent = PendingIntent.getBroadcast(
            this, 1, Intent(ACTION_NEXT),
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )

        val previousIntent = PendingIntent.getBroadcast(
            this, 2, Intent(ACTION_PREVIOUS),
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )

        // Create content intent to open the app
        val contentIntent = PendingIntent.getActivity(
            this, 0, Intent(this, MainActivity::class.java),
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )

        // Build notification
        val notification = NotificationCompat.Builder(this, CHANNEL_ID)
            .setContentTitle(song.title)
            .setContentText(song.artist)
            .setSmallIcon(R.drawable.ic_music_note)
            .setContentIntent(contentIntent)
            .setDeleteIntent(PendingIntent.getBroadcast(
                this, 3, Intent(ACTION_PLAY_PAUSE),
                PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
            ))
            .setVisibility(NotificationCompat.VISIBILITY_PUBLIC)
            .setPriority(NotificationCompat.PRIORITY_LOW)
            .addAction(
                R.drawable.ic_skip_previous,
                "Previous",
                previousIntent
            )
            .addAction(
                if (isCurrentlyPlaying) R.drawable.ic_pause else R.drawable.ic_play,
                if (isCurrentlyPlaying) "Pause" else "Play",
                playPauseIntent
            )
            .addAction(
                R.drawable.ic_skip_next,
                "Next",
                nextIntent
            )
            .setStyle(androidx.media.app.NotificationCompat.MediaStyle()
                .setMediaSession(mediaSession.sessionToken)
                .setShowActionsInCompactView(0, 1, 2)
            )
            .build()

        notificationManager.notify(NOTIFICATION_ID, notification)
    }

    private fun updateMediaSession() {
        val song = currentSong ?: return

        val metadata = MediaMetadataCompat.Builder()
            .putString(MediaMetadataCompat.METADATA_KEY_TITLE, song.title)
            .putString(MediaMetadataCompat.METADATA_KEY_ARTIST, song.artist)
            .putString(MediaMetadataCompat.METADATA_KEY_ALBUM, song.album ?: "Unknown Album")
            .putLong(MediaMetadataCompat.METADATA_KEY_DURATION, song.duration)
            .build()

        mediaSession.setMetadata(metadata)

        val state = if (isCurrentlyPlaying) {
            PlaybackStateCompat.STATE_PLAYING
        } else {
            PlaybackStateCompat.STATE_PAUSED
        }

        val playbackState = PlaybackStateCompat.Builder()
            .setState(state, PlaybackStateCompat.PLAYBACK_POSITION_UNKNOWN, 1.0f)
            .setActions(
                PlaybackStateCompat.ACTION_PLAY or
                PlaybackStateCompat.ACTION_PAUSE or
                PlaybackStateCompat.ACTION_SKIP_TO_NEXT or
                PlaybackStateCompat.ACTION_SKIP_TO_PREVIOUS
            )
            .build()

        mediaSession.setPlaybackState(playbackState)
    }

    private fun updateWidget() {
        val song = currentSong
        MusicWidgetProvider.updateWidget(
            this,
            song?.title,
            song?.artist,
            isCurrentlyPlaying
        )
    }

    private fun checkPermissionsAndLoadMusic() {
        if (PermissionHelper.hasStoragePermission(this)) {
            statusTextView.text = "Ready to scan for music"
            scanForMusic()
        } else {
            statusTextView.text = "Storage permission required"
            PermissionHelper.requestStoragePermission(this)
        }
    }

    private fun scanForMusic() {
        statusTextView.text = "Scanning for music files..."

        lifecycleScope.launch {
            try {
                // Get music files from common directories
                val musicFiles = fileScanner.scanMediaStore()
                songs.clear()
                songs.addAll(musicFiles)

                updateSongList()
                statusTextView.text = "Found ${songs.size} music files"

            } catch (e: Exception) {
                statusTextView.text = "Error scanning: ${e.message}"
                android.util.Log.e("MainActivity", "Error scanning music", e)
            }
        }
    }

    private fun selectMusicFolder() {
        try {
            directoryPickerLauncher.launch(null)
        } catch (e: Exception) {
            statusTextView.text = "Error opening folder picker: ${e.message}"
            android.util.Log.e("MainActivity", "Error opening folder picker", e)
        }
    }

    private fun scanSelectedDirectory(directoryUri: Uri) {
        statusTextView.text = "Scanning selected folder..."

        lifecycleScope.launch {
            try {
                val musicFiles = fileScanner.scanDirectory(directoryUri)
                songs.clear()
                songs.addAll(musicFiles)

                updateSongList()
                statusTextView.text = "Found ${songs.size} music files in selected folder"

            } catch (e: Exception) {
                statusTextView.text = "Error scanning folder: ${e.message}"
                android.util.Log.e("MainActivity", "Error scanning selected directory", e)
            }
        }
    }

    private fun updateSongList() {
        // Recreate the entire UI to reflect changes
        createMusicPlayerUI()
    }

    private fun formatDuration(duration: Long): String {
        val minutes = duration / 1000 / 60
        val seconds = (duration / 1000) % 60
        return String.format("%d:%02d", minutes, seconds)
    }

    private fun playSong(index: Int) {
        if (index >= 0 && index < songs.size) {
            currentSongIndex = index
            currentSong = songs[index]

            try {
                // Stop current playback
                stopCurrentPlayback()

                // Create new MediaPlayer
                mediaPlayer = MediaPlayer().apply {
                    setDataSource(this@MainActivity, Uri.parse(currentSong!!.path))
                    setOnPreparedListener { mp ->
                        mp.start()
                        isCurrentlyPlaying = true
                        playPauseButton.text = "⏸️"
                        currentSongTextView.text = "Playing: ${currentSong?.title} - ${currentSong?.artist}"

                        // Update media session, notification and widget
                        updateMediaSession()
                        createMediaNotification()
                        updateWidget()
                        updateSongList() // Refresh to show current song highlighting

                        android.widget.Toast.makeText(this@MainActivity, "♪ ${currentSong?.title}", android.widget.Toast.LENGTH_SHORT).show()
                    }
                    setOnCompletionListener {
                        // Auto play next song
                        nextSong()
                    }
                    setOnErrorListener { _, what, extra ->
                        android.widget.Toast.makeText(this@MainActivity, "Playback error: $what", android.widget.Toast.LENGTH_SHORT).show()
                        android.util.Log.e("MainActivity", "MediaPlayer error: what=$what, extra=$extra")
                        true
                    }
                    prepareAsync()
                }

            } catch (e: Exception) {
                android.widget.Toast.makeText(this, "Error playing song: ${e.message}", android.widget.Toast.LENGTH_SHORT).show()
                android.util.Log.e("MainActivity", "Error playing song", e)
            }
        }
    }

    private fun stopCurrentPlayback() {
        try {
            mediaPlayer?.let { mp ->
                if (mp.isPlaying) {
                    mp.stop()
                }
                mp.release()
            }
            mediaPlayer = null
            isCurrentlyPlaying = false

            // Clear notification when stopping
            notificationManager.cancel(NOTIFICATION_ID)
        } catch (e: Exception) {
            android.util.Log.e("MainActivity", "Error stopping playback", e)
        }
    }

    private fun togglePlayback() {
        try {
            mediaPlayer?.let { mp ->
                if (isCurrentlyPlaying) {
                    mp.pause()
                    isCurrentlyPlaying = false
                    playPauseButton.text = "▶️"

                    // Update media session, notification and widget
                    updateMediaSession()
                    createMediaNotification()
                    updateWidget()
                    updateSongList() // Refresh UI

                    android.widget.Toast.makeText(this, "⏸️ Paused", android.widget.Toast.LENGTH_SHORT).show()
                } else {
                    mp.start()
                    isCurrentlyPlaying = true
                    playPauseButton.text = "⏸️"

                    // Update media session, notification and widget
                    updateMediaSession()
                    createMediaNotification()
                    updateWidget()
                    updateSongList() // Refresh UI

                    android.widget.Toast.makeText(this, "▶️ Playing", android.widget.Toast.LENGTH_SHORT).show()
                }
            } ?: run {
                if (currentSong != null) {
                    // Resume current song
                    playSong(currentSongIndex)
                } else {
                    android.widget.Toast.makeText(this, "No song selected", android.widget.Toast.LENGTH_SHORT).show()
                }
            }
        } catch (e: Exception) {
            android.widget.Toast.makeText(this, "Playback error: ${e.message}", android.widget.Toast.LENGTH_SHORT).show()
            android.util.Log.e("MainActivity", "Error toggling playback", e)
        }
    }

    private fun previousSong() {
        if (currentSongIndex > 0) {
            playSong(currentSongIndex - 1)
        }
    }

    private fun nextSong() {
        if (currentSongIndex < songs.size - 1) {
            playSong(currentSongIndex + 1)
        }
    }

    override fun onRequestPermissionsResult(
        requestCode: Int,
        permissions: Array<out String>,
        grantResults: IntArray
    ) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)

        if (requestCode == PermissionHelper.STORAGE_PERMISSION_REQUEST_CODE) {
            if (grantResults.isNotEmpty() && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                statusTextView.text = "Permission granted! Ready to scan."
                scanForMusic()
            } else {
                statusTextView.text = "Storage permission denied. Cannot access music files."
            }
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        stopCurrentPlayback()

        // Clean up media session and receiver
        try {
            mediaSession.release()
            unregisterReceiver(mediaControlReceiver)
            notificationManager.cancel(NOTIFICATION_ID)
        } catch (e: Exception) {
            android.util.Log.e("MainActivity", "Error cleaning up resources", e)
        }
    }

    override fun onPause() {
        super.onPause()
        // Optionally pause playback when app goes to background
        // mediaPlayer?.pause()
        // isCurrentlyPlaying = false
        // playPauseButton.text = "▶️"
    }
}
