package com.minimalmusicplayer

import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.content.pm.PackageManager
import android.graphics.BitmapFactory
import android.media.MediaPlayer
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.support.v4.media.MediaMetadataCompat
import android.support.v4.media.session.MediaSessionCompat
import android.support.v4.media.session.PlaybackStateCompat
import androidx.activity.result.contract.ActivityResultContracts
import androidx.appcompat.app.AppCompatActivity
import androidx.core.app.NotificationCompat
import androidx.documentfile.provider.DocumentFile
import androidx.lifecycle.lifecycleScope
import com.minimalmusicplayer.model.Song
import com.minimalmusicplayer.util.FileScanner
import com.minimalmusicplayer.util.PermissionHelper
import com.minimalmusicplayer.util.PreferenceManager
import com.minimalmusicplayer.widget.MusicWidgetProvider
import kotlinx.coroutines.launch

class MainActivity : AppCompatActivity() {

    private lateinit var fileScanner: FileScanner
    private lateinit var preferenceManager: PreferenceManager

    // UI components
    private lateinit var statusTextView: android.widget.TextView
    private lateinit var songListLayout: android.widget.LinearLayout
    private lateinit var playPauseButton: android.widget.Button
    private lateinit var currentSongTextView: android.widget.TextView

    // Music data
    private var songs = mutableListOf<Song>()
    private var currentSongIndex = -1
    private var currentSong: Song? = null

    // MediaPlayer for actual audio playback
    private var mediaPlayer: MediaPlayer? = null
    private var isCurrentlyPlaying = false

    // Media session and notification
    private lateinit var mediaSession: MediaSessionCompat
    private lateinit var notificationManager: NotificationManager

    companion object {
        const val NOTIFICATION_ID = 1
        const val CHANNEL_ID = "music_playback"
        const val ACTION_PLAY_PAUSE = "com.minimalmusicplayer.PLAY_PAUSE"
        const val ACTION_NEXT = "com.minimalmusicplayer.NEXT"
        const val ACTION_PREVIOUS = "com.minimalmusicplayer.PREVIOUS"
    }

    // Directory selection
    private val directoryPickerLauncher = registerForActivityResult(
        ActivityResultContracts.OpenDocumentTree()
    ) { uri ->
        uri?.let { selectedUri ->
            // Grant persistent permission
            contentResolver.takePersistableUriPermission(
                selectedUri,
                android.content.Intent.FLAG_GRANT_READ_URI_PERMISSION
            )
            scanSelectedDirectory(selectedUri)
        }
    }

    // Broadcast receiver for notification and widget controls
    private val mediaControlReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context?, intent: Intent?) {
            when (intent?.action) {
                ACTION_PLAY_PAUSE, "com.minimalmusicplayer.PLAY_PAUSE" -> togglePlayback()
                ACTION_NEXT, "com.minimalmusicplayer.NEXT" -> nextSong()
                ACTION_PREVIOUS, "com.minimalmusicplayer.PREVIOUS" -> previousSong()
            }
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        // Initialize components
        try {
            preferenceManager = PreferenceManager(this)
            fileScanner = FileScanner(this)
            initializeMediaSession()
            createNotificationChannel()
            registerMediaControlReceiver()
        } catch (e: Exception) {
            android.util.Log.e("MainActivity", "Error initializing components", e)
        }

        createMusicPlayerUI()
        checkPermissionsAndLoadMusic()
    }

    private fun createMusicPlayerUI() {
        // Create main container with modern styling
        val mainContainer = android.widget.LinearLayout(this).apply {
            orientation = android.widget.LinearLayout.VERTICAL
            setBackgroundColor(getColor(R.color.music_background))
        }

        // Top app bar
        val appBar = createAppBar()

        // Content area
        val contentLayout = android.widget.LinearLayout(this).apply {
            orientation = android.widget.LinearLayout.VERTICAL
            setPadding(24, 16, 24, 16)
        }

        // Status card
        val statusCard = createStatusCard()

        // Action buttons
        val actionButtonsLayout = createActionButtons()

        // Song list container
        val songListContainer = createSongListContainer()

        // Bottom player
        val bottomPlayer = createBottomPlayer()

        // Add all components
        contentLayout.addView(statusCard)
        contentLayout.addView(actionButtonsLayout)
        contentLayout.addView(songListContainer)

        mainContainer.addView(appBar)
        mainContainer.addView(contentLayout)
        mainContainer.addView(bottomPlayer)

        setContentView(mainContainer)
    }

    private fun createAppBar(): android.view.View {
        return android.widget.LinearLayout(this).apply {
            orientation = android.widget.LinearLayout.HORIZONTAL
            setBackgroundColor(getColor(R.color.music_primary))
            setPadding(24, 48, 24, 24)
            gravity = android.view.Gravity.CENTER_VERTICAL

            val titleView = android.widget.TextView(this@MainActivity).apply {
                text = "🎵 Music Player"
                textSize = 24f
                setTextColor(getColor(R.color.white))
                typeface = android.graphics.Typeface.DEFAULT_BOLD
                layoutParams = android.widget.LinearLayout.LayoutParams(
                    0, android.widget.LinearLayout.LayoutParams.WRAP_CONTENT, 1f
                )
            }

            addView(titleView)
        }
    }

    private fun createStatusCard(): android.view.View {
        val cardLayout = android.widget.LinearLayout(this).apply {
            orientation = android.widget.LinearLayout.VERTICAL
            setBackgroundColor(getColor(R.color.music_surface))
            setPadding(20, 16, 20, 16)
            elevation = 4f
        }

        statusTextView = android.widget.TextView(this).apply {
            text = "Ready to discover music"
            textSize = 16f
            setTextColor(getColor(R.color.music_on_surface))
            gravity = android.view.Gravity.CENTER
        }

        cardLayout.addView(statusTextView)

        val cardContainer = android.widget.FrameLayout(this).apply {
            setPadding(0, 16, 0, 16)
            addView(cardLayout)
        }

        return cardContainer
    }

    private fun createActionButtons(): android.view.View {
        val buttonContainer = android.widget.LinearLayout(this).apply {
            orientation = android.widget.LinearLayout.HORIZONTAL
            gravity = android.view.Gravity.CENTER
            setPadding(0, 8, 0, 24)
        }

        val selectFolderButton = createModernButton("📁 Select Folder", true) {
            selectMusicFolder()
        }

        val scanAllButton = createModernButton("🔍 Scan All", false) {
            scanForMusic()
        }

        buttonContainer.addView(selectFolderButton)

        val spacer = android.view.View(this).apply {
            layoutParams = android.widget.LinearLayout.LayoutParams(24, 1)
        }
        buttonContainer.addView(spacer)

        buttonContainer.addView(scanAllButton)

        return buttonContainer
    }

    private fun createModernButton(text: String, isPrimary: Boolean, onClick: () -> Unit): android.widget.Button {
        return android.widget.Button(this).apply {
            this.text = text
            textSize = 14f
            setPadding(32, 16, 32, 16)

            if (isPrimary) {
                setBackgroundColor(getColor(R.color.music_primary))
                setTextColor(getColor(R.color.white))
            } else {
                setBackgroundColor(getColor(R.color.music_secondary))
                setTextColor(getColor(R.color.white))
            }

            elevation = 6f
            setOnClickListener { onClick() }

            layoutParams = android.widget.LinearLayout.LayoutParams(
                0, android.widget.LinearLayout.LayoutParams.WRAP_CONTENT, 1f
            )
        }
    }

    private fun createSongListContainer(): android.view.View {
        val container = android.widget.LinearLayout(this).apply {
            orientation = android.widget.LinearLayout.VERTICAL
            setBackgroundColor(getColor(R.color.music_surface))
            elevation = 2f
            setPadding(0, 16, 0, 16)
        }

        val headerText = android.widget.TextView(this).apply {
            text = "Your Music"
            textSize = 18f
            setTextColor(getColor(R.color.music_on_surface))
            typeface = android.graphics.Typeface.DEFAULT_BOLD
            setPadding(20, 0, 20, 16)
        }

        songListLayout = android.widget.LinearLayout(this).apply {
            orientation = android.widget.LinearLayout.VERTICAL
        }

        val scrollView = android.widget.ScrollView(this).apply {
            addView(songListLayout)
            layoutParams = android.widget.LinearLayout.LayoutParams(
                android.widget.LinearLayout.LayoutParams.MATCH_PARENT,
                0, 1f
            )
        }

        container.addView(headerText)
        container.addView(scrollView)

        return container
    }

    private fun createBottomPlayer(): android.view.View {
        val playerContainer = android.widget.LinearLayout(this).apply {
            orientation = android.widget.LinearLayout.VERTICAL
            setBackgroundColor(getColor(R.color.player_background))
            elevation = 8f
        }

        // Current song info
        currentSongTextView = android.widget.TextView(this).apply {
            text = "No song selected"
            textSize = 14f
            setTextColor(getColor(R.color.player_on_surface))
            gravity = android.view.Gravity.CENTER
            setPadding(20, 16, 20, 8)
            maxLines = 1
        }

        // Control buttons
        val controlsLayout = android.widget.LinearLayout(this).apply {
            orientation = android.widget.LinearLayout.HORIZONTAL
            gravity = android.view.Gravity.CENTER
            setPadding(20, 8, 20, 20)
        }

        val prevButton = createPlayerButton("⏮️") { previousSong() }
        playPauseButton = createPlayerButton("▶️") { togglePlayback() }
        val nextButton = createPlayerButton("⏭️") { nextSong() }

        controlsLayout.addView(prevButton)
        controlsLayout.addView(android.view.View(this).apply {
            layoutParams = android.widget.LinearLayout.LayoutParams(32, 1)
        })
        controlsLayout.addView(playPauseButton)
        controlsLayout.addView(android.view.View(this).apply {
            layoutParams = android.widget.LinearLayout.LayoutParams(32, 1)
        })
        controlsLayout.addView(nextButton)

        playerContainer.addView(currentSongTextView)
        playerContainer.addView(controlsLayout)

        return playerContainer
    }

    private fun createPlayerButton(text: String, onClick: () -> Unit): android.widget.Button {
        return android.widget.Button(this).apply {
            this.text = text
            textSize = 20f
            setBackgroundColor(getColor(R.color.player_accent))
            setTextColor(getColor(R.color.white))
            setPadding(20, 20, 20, 20)
            elevation = 4f
            setOnClickListener { onClick() }

            layoutParams = android.widget.LinearLayout.LayoutParams(
                80, 80
            )
        }
    }

    private fun initializeMediaSession() {
        mediaSession = MediaSessionCompat(this, "MinimalMusicPlayer").apply {
            setCallback(object : MediaSessionCompat.Callback() {
                override fun onPlay() {
                    if (currentSong != null) {
                        togglePlayback()
                    }
                }

                override fun onPause() {
                    if (isCurrentlyPlaying) {
                        togglePlayback()
                    }
                }

                override fun onSkipToNext() {
                    nextSong()
                }

                override fun onSkipToPrevious() {
                    previousSong()
                }
            })
            isActive = true
        }
    }

    private fun createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channel = NotificationChannel(
                CHANNEL_ID,
                "Music Playback",
                NotificationManager.IMPORTANCE_LOW
            ).apply {
                description = "Controls for music playback"
                setShowBadge(false)
            }

            notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
            notificationManager.createNotificationChannel(channel)
        } else {
            notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
        }
    }

    private fun registerMediaControlReceiver() {
        val filter = IntentFilter().apply {
            addAction(ACTION_PLAY_PAUSE)
            addAction(ACTION_NEXT)
            addAction(ACTION_PREVIOUS)
            // Also listen for widget broadcasts
            addAction("com.minimalmusicplayer.PLAY_PAUSE")
            addAction("com.minimalmusicplayer.NEXT")
            addAction("com.minimalmusicplayer.PREVIOUS")
        }
        registerReceiver(mediaControlReceiver, filter)
    }

    private fun createMediaNotification() {
        val song = currentSong ?: return

        // Create pending intents for notification actions
        val playPauseIntent = PendingIntent.getBroadcast(
            this, 0, Intent(ACTION_PLAY_PAUSE),
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )

        val nextIntent = PendingIntent.getBroadcast(
            this, 1, Intent(ACTION_NEXT),
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )

        val previousIntent = PendingIntent.getBroadcast(
            this, 2, Intent(ACTION_PREVIOUS),
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )

        // Create content intent to open the app
        val contentIntent = PendingIntent.getActivity(
            this, 0, Intent(this, MainActivity::class.java),
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )

        // Build notification
        val notification = NotificationCompat.Builder(this, CHANNEL_ID)
            .setContentTitle(song.title)
            .setContentText(song.artist)
            .setSmallIcon(R.drawable.ic_music_note)
            .setContentIntent(contentIntent)
            .setDeleteIntent(PendingIntent.getBroadcast(
                this, 3, Intent(ACTION_PLAY_PAUSE),
                PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
            ))
            .setVisibility(NotificationCompat.VISIBILITY_PUBLIC)
            .setPriority(NotificationCompat.PRIORITY_LOW)
            .addAction(
                R.drawable.ic_skip_previous,
                "Previous",
                previousIntent
            )
            .addAction(
                if (isCurrentlyPlaying) R.drawable.ic_pause else R.drawable.ic_play,
                if (isCurrentlyPlaying) "Pause" else "Play",
                playPauseIntent
            )
            .addAction(
                R.drawable.ic_skip_next,
                "Next",
                nextIntent
            )
            .setStyle(androidx.media.app.NotificationCompat.MediaStyle()
                .setMediaSession(mediaSession.sessionToken)
                .setShowActionsInCompactView(0, 1, 2)
            )
            .build()

        notificationManager.notify(NOTIFICATION_ID, notification)
    }

    private fun updateMediaSession() {
        val song = currentSong ?: return

        val metadata = MediaMetadataCompat.Builder()
            .putString(MediaMetadataCompat.METADATA_KEY_TITLE, song.title)
            .putString(MediaMetadataCompat.METADATA_KEY_ARTIST, song.artist)
            .putString(MediaMetadataCompat.METADATA_KEY_ALBUM, song.album ?: "Unknown Album")
            .putLong(MediaMetadataCompat.METADATA_KEY_DURATION, song.duration)
            .build()

        mediaSession.setMetadata(metadata)

        val state = if (isCurrentlyPlaying) {
            PlaybackStateCompat.STATE_PLAYING
        } else {
            PlaybackStateCompat.STATE_PAUSED
        }

        val playbackState = PlaybackStateCompat.Builder()
            .setState(state, PlaybackStateCompat.PLAYBACK_POSITION_UNKNOWN, 1.0f)
            .setActions(
                PlaybackStateCompat.ACTION_PLAY or
                PlaybackStateCompat.ACTION_PAUSE or
                PlaybackStateCompat.ACTION_SKIP_TO_NEXT or
                PlaybackStateCompat.ACTION_SKIP_TO_PREVIOUS
            )
            .build()

        mediaSession.setPlaybackState(playbackState)
    }

    private fun updateWidget() {
        val song = currentSong
        MusicWidgetProvider.updateWidget(
            this,
            song?.title,
            song?.artist,
            isCurrentlyPlaying
        )
    }

    private fun checkPermissionsAndLoadMusic() {
        if (PermissionHelper.hasStoragePermission(this)) {
            statusTextView.text = "Ready to scan for music"
            scanForMusic()
        } else {
            statusTextView.text = "Storage permission required"
            PermissionHelper.requestStoragePermission(this)
        }
    }

    private fun scanForMusic() {
        statusTextView.text = "Scanning for music files..."

        lifecycleScope.launch {
            try {
                // Get music files from common directories
                val musicFiles = fileScanner.scanMediaStore()
                songs.clear()
                songs.addAll(musicFiles)

                updateSongList()
                statusTextView.text = "Found ${songs.size} music files"

            } catch (e: Exception) {
                statusTextView.text = "Error scanning: ${e.message}"
                android.util.Log.e("MainActivity", "Error scanning music", e)
            }
        }
    }

    private fun selectMusicFolder() {
        try {
            directoryPickerLauncher.launch(null)
        } catch (e: Exception) {
            statusTextView.text = "Error opening folder picker: ${e.message}"
            android.util.Log.e("MainActivity", "Error opening folder picker", e)
        }
    }

    private fun scanSelectedDirectory(directoryUri: Uri) {
        statusTextView.text = "Scanning selected folder..."

        lifecycleScope.launch {
            try {
                val musicFiles = fileScanner.scanDirectory(directoryUri)
                songs.clear()
                songs.addAll(musicFiles)

                updateSongList()
                statusTextView.text = "Found ${songs.size} music files in selected folder"

            } catch (e: Exception) {
                statusTextView.text = "Error scanning folder: ${e.message}"
                android.util.Log.e("MainActivity", "Error scanning selected directory", e)
            }
        }
    }

    private fun updateSongList() {
        songListLayout.removeAllViews()

        if (songs.isEmpty()) {
            val emptyView = android.widget.TextView(this).apply {
                text = "No music files found\nTry selecting a different folder"
                textSize = 16f
                setTextColor(getColor(R.color.music_on_surface_variant))
                gravity = android.view.Gravity.CENTER
                setPadding(40, 60, 40, 60)
            }
            songListLayout.addView(emptyView)
            return
        }

        songs.forEachIndexed { index, song ->
            val songItemLayout = createSongItem(song, index)
            songListLayout.addView(songItemLayout)

            // Add divider except for last item
            if (index < songs.size - 1) {
                val divider = android.view.View(this).apply {
                    setBackgroundColor(getColor(R.color.divider))
                    layoutParams = android.widget.LinearLayout.LayoutParams(
                        android.widget.LinearLayout.LayoutParams.MATCH_PARENT, 1
                    ).apply {
                        setMargins(20, 0, 20, 0)
                    }
                }
                songListLayout.addView(divider)
            }
        }
    }

    private fun createSongItem(song: Song, index: Int): android.view.View {
        val itemLayout = android.widget.LinearLayout(this).apply {
            orientation = android.widget.LinearLayout.HORIZONTAL
            setPadding(20, 16, 20, 16)
            gravity = android.view.Gravity.CENTER_VERTICAL
            isClickable = true
            isFocusable = true

            // Highlight current song
            if (currentSongIndex == index) {
                setBackgroundColor(getColor(R.color.current_song_background))
            } else {
                setBackgroundColor(android.graphics.Color.TRANSPARENT)
            }

            setOnClickListener { playSong(index) }
        }

        // Music note icon
        val iconView = android.widget.TextView(this).apply {
            text = if (currentSongIndex == index && isCurrentlyPlaying) "🎵" else "🎶"
            textSize = 20f
            setPadding(0, 0, 16, 0)
            layoutParams = android.widget.LinearLayout.LayoutParams(
                android.widget.LinearLayout.LayoutParams.WRAP_CONTENT,
                android.widget.LinearLayout.LayoutParams.WRAP_CONTENT
            )
        }

        // Song info container
        val infoLayout = android.widget.LinearLayout(this).apply {
            orientation = android.widget.LinearLayout.VERTICAL
            layoutParams = android.widget.LinearLayout.LayoutParams(
                0, android.widget.LinearLayout.LayoutParams.WRAP_CONTENT, 1f
            )
        }

        // Song title
        val titleView = android.widget.TextView(this).apply {
            text = song.title
            textSize = 16f
            setTextColor(
                if (currentSongIndex == index) getColor(R.color.current_song_text)
                else getColor(R.color.music_on_surface)
            )
            typeface = if (currentSongIndex == index) android.graphics.Typeface.DEFAULT_BOLD
                      else android.graphics.Typeface.DEFAULT
            maxLines = 1
            ellipsize = android.text.TextUtils.TruncateAt.END
        }

        // Song artist
        val artistView = android.widget.TextView(this).apply {
            text = song.artist
            textSize = 14f
            setTextColor(getColor(R.color.music_on_surface_variant))
            maxLines = 1
            ellipsize = android.text.TextUtils.TruncateAt.END
            setPadding(0, 4, 0, 0)
        }

        // Duration
        val durationView = android.widget.TextView(this).apply {
            text = formatDuration(song.duration)
            textSize = 12f
            setTextColor(getColor(R.color.music_on_surface_variant))
            setPadding(16, 0, 0, 0)
        }

        infoLayout.addView(titleView)
        infoLayout.addView(artistView)

        itemLayout.addView(iconView)
        itemLayout.addView(infoLayout)
        itemLayout.addView(durationView)

        return itemLayout
    }

    private fun formatDuration(duration: Long): String {
        val minutes = duration / 1000 / 60
        val seconds = (duration / 1000) % 60
        return String.format("%d:%02d", minutes, seconds)
    }

    private fun playSong(index: Int) {
        if (index >= 0 && index < songs.size) {
            currentSongIndex = index
            currentSong = songs[index]

            try {
                // Stop current playback
                stopCurrentPlayback()

                // Create new MediaPlayer
                mediaPlayer = MediaPlayer().apply {
                    setDataSource(this@MainActivity, Uri.parse(currentSong!!.path))
                    setOnPreparedListener { mp ->
                        mp.start()
                        isCurrentlyPlaying = true
                        playPauseButton.text = "⏸️"
                        currentSongTextView.text = "Playing: ${currentSong?.title} - ${currentSong?.artist}"

                        // Update media session, notification and widget
                        updateMediaSession()
                        createMediaNotification()
                        updateWidget()
                        updateSongList() // Refresh to show current song highlighting

                        android.widget.Toast.makeText(this@MainActivity, "♪ ${currentSong?.title}", android.widget.Toast.LENGTH_SHORT).show()
                    }
                    setOnCompletionListener {
                        // Auto play next song
                        nextSong()
                    }
                    setOnErrorListener { _, what, extra ->
                        android.widget.Toast.makeText(this@MainActivity, "Playback error: $what", android.widget.Toast.LENGTH_SHORT).show()
                        android.util.Log.e("MainActivity", "MediaPlayer error: what=$what, extra=$extra")
                        true
                    }
                    prepareAsync()
                }

            } catch (e: Exception) {
                android.widget.Toast.makeText(this, "Error playing song: ${e.message}", android.widget.Toast.LENGTH_SHORT).show()
                android.util.Log.e("MainActivity", "Error playing song", e)
            }
        }
    }

    private fun stopCurrentPlayback() {
        try {
            mediaPlayer?.let { mp ->
                if (mp.isPlaying) {
                    mp.stop()
                }
                mp.release()
            }
            mediaPlayer = null
            isCurrentlyPlaying = false

            // Clear notification when stopping
            notificationManager.cancel(NOTIFICATION_ID)
        } catch (e: Exception) {
            android.util.Log.e("MainActivity", "Error stopping playback", e)
        }
    }

    private fun togglePlayback() {
        try {
            mediaPlayer?.let { mp ->
                if (isCurrentlyPlaying) {
                    mp.pause()
                    isCurrentlyPlaying = false
                    playPauseButton.text = "▶️"

                    // Update media session, notification and widget
                    updateMediaSession()
                    createMediaNotification()
                    updateWidget()
                    updateSongList() // Refresh UI

                    android.widget.Toast.makeText(this, "⏸️ Paused", android.widget.Toast.LENGTH_SHORT).show()
                } else {
                    mp.start()
                    isCurrentlyPlaying = true
                    playPauseButton.text = "⏸️"

                    // Update media session, notification and widget
                    updateMediaSession()
                    createMediaNotification()
                    updateWidget()
                    updateSongList() // Refresh UI

                    android.widget.Toast.makeText(this, "▶️ Playing", android.widget.Toast.LENGTH_SHORT).show()
                }
            } ?: run {
                if (currentSong != null) {
                    // Resume current song
                    playSong(currentSongIndex)
                } else {
                    android.widget.Toast.makeText(this, "No song selected", android.widget.Toast.LENGTH_SHORT).show()
                }
            }
        } catch (e: Exception) {
            android.widget.Toast.makeText(this, "Playback error: ${e.message}", android.widget.Toast.LENGTH_SHORT).show()
            android.util.Log.e("MainActivity", "Error toggling playback", e)
        }
    }

    private fun previousSong() {
        if (currentSongIndex > 0) {
            playSong(currentSongIndex - 1)
        }
    }

    private fun nextSong() {
        if (currentSongIndex < songs.size - 1) {
            playSong(currentSongIndex + 1)
        }
    }

    override fun onRequestPermissionsResult(
        requestCode: Int,
        permissions: Array<out String>,
        grantResults: IntArray
    ) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)

        if (requestCode == PermissionHelper.STORAGE_PERMISSION_REQUEST_CODE) {
            if (grantResults.isNotEmpty() && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                statusTextView.text = "Permission granted! Ready to scan."
                scanForMusic()
            } else {
                statusTextView.text = "Storage permission denied. Cannot access music files."
            }
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        stopCurrentPlayback()

        // Clean up media session and receiver
        try {
            mediaSession.release()
            unregisterReceiver(mediaControlReceiver)
            notificationManager.cancel(NOTIFICATION_ID)
        } catch (e: Exception) {
            android.util.Log.e("MainActivity", "Error cleaning up resources", e)
        }
    }

    override fun onPause() {
        super.onPause()
        // Optionally pause playback when app goes to background
        // mediaPlayer?.pause()
        // isCurrentlyPlaying = false
        // playPauseButton.text = "▶️"
    }
}
