package com.minimalmusicplayer

import android.content.pm.PackageManager
import android.media.MediaPlayer
import android.net.Uri
import android.os.Bundle
import androidx.activity.result.contract.ActivityResultContracts
import androidx.appcompat.app.AppCompatActivity
import androidx.documentfile.provider.DocumentFile
import androidx.lifecycle.lifecycleScope
import com.minimalmusicplayer.model.Song
import com.minimalmusicplayer.util.FileScanner
import com.minimalmusicplayer.util.PermissionHelper
import com.minimalmusicplayer.util.PreferenceManager
import kotlinx.coroutines.launch

class MainActivity : AppCompatActivity() {

    private lateinit var fileScanner: FileScanner
    private lateinit var preferenceManager: PreferenceManager

    // UI components
    private lateinit var statusTextView: android.widget.TextView
    private lateinit var songListLayout: android.widget.LinearLayout
    private lateinit var playPauseButton: android.widget.Button
    private lateinit var currentSongTextView: android.widget.TextView

    // Music data
    private var songs = mutableListOf<Song>()
    private var currentSongIndex = -1
    private var currentSong: Song? = null

    // MediaPlayer for actual audio playback
    private var mediaPlayer: MediaPlayer? = null
    private var isCurrentlyPlaying = false

    // Directory selection
    private val directoryPickerLauncher = registerForActivityResult(
        ActivityResultContracts.OpenDocumentTree()
    ) { uri ->
        uri?.let { selectedUri ->
            // Grant persistent permission
            contentResolver.takePersistableUriPermission(
                selectedUri,
                android.content.Intent.FLAG_GRANT_READ_URI_PERMISSION
            )
            scanSelectedDirectory(selectedUri)
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        // Initialize components
        try {
            preferenceManager = PreferenceManager(this)
            fileScanner = FileScanner(this)
        } catch (e: Exception) {
            android.util.Log.e("MainActivity", "Error initializing components", e)
        }

        createMusicPlayerUI()
        checkPermissionsAndLoadMusic()
    }

    private fun createMusicPlayerUI() {
        val mainLayout = android.widget.LinearLayout(this).apply {
            orientation = android.widget.LinearLayout.VERTICAL
            setPadding(16, 16, 16, 16)
        }

        // Title
        val titleView = android.widget.TextView(this).apply {
            text = "🎵 Minimal Music Player"
            textSize = 20f
            gravity = android.view.Gravity.CENTER
            setPadding(0, 0, 0, 32)
        }

        // Status text
        statusTextView = android.widget.TextView(this).apply {
            text = "Checking permissions..."
            textSize = 14f
            gravity = android.view.Gravity.CENTER
            setPadding(16, 16, 16, 16)
        }

        // Button layout
        val buttonLayout = android.widget.LinearLayout(this).apply {
            orientation = android.widget.LinearLayout.HORIZONTAL
        }

        // Select folder button
        val selectFolderButton = android.widget.Button(this).apply {
            text = "📁 Select Folder"
            setOnClickListener { selectMusicFolder() }
        }

        // Scan all button
        val scanAllButton = android.widget.Button(this).apply {
            text = "🔍 Scan All"
            setOnClickListener { scanForMusic() }
        }

        buttonLayout.addView(selectFolderButton)
        buttonLayout.addView(scanAllButton)

        // Song list (ScrollView with LinearLayout)
        songListLayout = android.widget.LinearLayout(this).apply {
            orientation = android.widget.LinearLayout.VERTICAL
        }

        val scrollView = android.widget.ScrollView(this).apply {
            addView(songListLayout)
        }

        // Player controls
        val controlsLayout = android.widget.LinearLayout(this).apply {
            orientation = android.widget.LinearLayout.HORIZONTAL
            gravity = android.view.Gravity.CENTER
        }

        playPauseButton = android.widget.Button(this).apply {
            text = "▶️"
            setOnClickListener { togglePlayback() }
        }

        val prevButton = android.widget.Button(this).apply {
            text = "⏮️"
            setOnClickListener { previousSong() }
        }

        val nextButton = android.widget.Button(this).apply {
            text = "⏭️"
            setOnClickListener { nextSong() }
        }

        controlsLayout.addView(prevButton)
        controlsLayout.addView(playPauseButton)
        controlsLayout.addView(nextButton)

        // Current song display
        currentSongTextView = android.widget.TextView(this).apply {
            text = "No song selected"
            textSize = 12f
            gravity = android.view.Gravity.CENTER
            setPadding(16, 8, 16, 8)
        }

        // Add all views to main layout
        mainLayout.addView(titleView)
        mainLayout.addView(statusTextView)
        mainLayout.addView(buttonLayout)
        mainLayout.addView(scrollView)
        mainLayout.addView(currentSongTextView)
        mainLayout.addView(controlsLayout)

        setContentView(mainLayout)
    }

    private fun checkPermissionsAndLoadMusic() {
        if (PermissionHelper.hasStoragePermission(this)) {
            statusTextView.text = "Ready to scan for music"
            scanForMusic()
        } else {
            statusTextView.text = "Storage permission required"
            PermissionHelper.requestStoragePermission(this)
        }
    }

    private fun scanForMusic() {
        statusTextView.text = "Scanning for music files..."

        lifecycleScope.launch {
            try {
                // Get music files from common directories
                val musicFiles = fileScanner.scanMediaStore()
                songs.clear()
                songs.addAll(musicFiles)

                updateSongList()
                statusTextView.text = "Found ${songs.size} music files"

            } catch (e: Exception) {
                statusTextView.text = "Error scanning: ${e.message}"
                android.util.Log.e("MainActivity", "Error scanning music", e)
            }
        }
    }

    private fun selectMusicFolder() {
        try {
            directoryPickerLauncher.launch(null)
        } catch (e: Exception) {
            statusTextView.text = "Error opening folder picker: ${e.message}"
            android.util.Log.e("MainActivity", "Error opening folder picker", e)
        }
    }

    private fun scanSelectedDirectory(directoryUri: Uri) {
        statusTextView.text = "Scanning selected folder..."

        lifecycleScope.launch {
            try {
                val musicFiles = fileScanner.scanDirectory(directoryUri)
                songs.clear()
                songs.addAll(musicFiles)

                updateSongList()
                statusTextView.text = "Found ${songs.size} music files in selected folder"

            } catch (e: Exception) {
                statusTextView.text = "Error scanning folder: ${e.message}"
                android.util.Log.e("MainActivity", "Error scanning selected directory", e)
            }
        }
    }

    private fun updateSongList() {
        songListLayout.removeAllViews()

        songs.forEachIndexed { index, song ->
            val songView = android.widget.TextView(this).apply {
                text = "${song.title} - ${song.artist}"
                textSize = 14f
                setPadding(16, 12, 16, 12)
                setBackgroundResource(android.R.drawable.list_selector_background)
                isClickable = true
                setOnClickListener {
                    playSong(index)
                }
            }
            songListLayout.addView(songView)
        }
    }

    private fun playSong(index: Int) {
        if (index >= 0 && index < songs.size) {
            currentSongIndex = index
            currentSong = songs[index]

            try {
                // Stop current playback
                stopCurrentPlayback()

                // Create new MediaPlayer
                mediaPlayer = MediaPlayer().apply {
                    setDataSource(this@MainActivity, Uri.parse(currentSong!!.path))
                    setOnPreparedListener { mp ->
                        mp.start()
                        isCurrentlyPlaying = true
                        playPauseButton.text = "⏸️"
                        currentSongTextView.text = "Playing: ${currentSong?.title} - ${currentSong?.artist}"
                        android.widget.Toast.makeText(this@MainActivity, "Playing: ${currentSong?.title}", android.widget.Toast.LENGTH_SHORT).show()
                    }
                    setOnCompletionListener {
                        // Auto play next song
                        nextSong()
                    }
                    setOnErrorListener { _, what, extra ->
                        android.widget.Toast.makeText(this@MainActivity, "Playback error: $what", android.widget.Toast.LENGTH_SHORT).show()
                        android.util.Log.e("MainActivity", "MediaPlayer error: what=$what, extra=$extra")
                        true
                    }
                    prepareAsync()
                }

            } catch (e: Exception) {
                android.widget.Toast.makeText(this, "Error playing song: ${e.message}", android.widget.Toast.LENGTH_SHORT).show()
                android.util.Log.e("MainActivity", "Error playing song", e)
            }
        }
    }

    private fun stopCurrentPlayback() {
        try {
            mediaPlayer?.let { mp ->
                if (mp.isPlaying) {
                    mp.stop()
                }
                mp.release()
            }
            mediaPlayer = null
            isCurrentlyPlaying = false
        } catch (e: Exception) {
            android.util.Log.e("MainActivity", "Error stopping playback", e)
        }
    }

    private fun togglePlayback() {
        try {
            mediaPlayer?.let { mp ->
                if (isCurrentlyPlaying) {
                    mp.pause()
                    isCurrentlyPlaying = false
                    playPauseButton.text = "▶️"
                    android.widget.Toast.makeText(this, "Paused", android.widget.Toast.LENGTH_SHORT).show()
                } else {
                    mp.start()
                    isCurrentlyPlaying = true
                    playPauseButton.text = "⏸️"
                    android.widget.Toast.makeText(this, "Playing", android.widget.Toast.LENGTH_SHORT).show()
                }
            } ?: run {
                if (currentSong != null) {
                    // Resume current song
                    playSong(currentSongIndex)
                } else {
                    android.widget.Toast.makeText(this, "No song selected", android.widget.Toast.LENGTH_SHORT).show()
                }
            }
        } catch (e: Exception) {
            android.widget.Toast.makeText(this, "Playback error: ${e.message}", android.widget.Toast.LENGTH_SHORT).show()
            android.util.Log.e("MainActivity", "Error toggling playback", e)
        }
    }

    private fun previousSong() {
        if (currentSongIndex > 0) {
            playSong(currentSongIndex - 1)
        }
    }

    private fun nextSong() {
        if (currentSongIndex < songs.size - 1) {
            playSong(currentSongIndex + 1)
        }
    }

    override fun onRequestPermissionsResult(
        requestCode: Int,
        permissions: Array<out String>,
        grantResults: IntArray
    ) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)

        if (requestCode == PermissionHelper.STORAGE_PERMISSION_REQUEST_CODE) {
            if (grantResults.isNotEmpty() && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                statusTextView.text = "Permission granted! Ready to scan."
                scanForMusic()
            } else {
                statusTextView.text = "Storage permission denied. Cannot access music files."
            }
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        stopCurrentPlayback()
    }

    override fun onPause() {
        super.onPause()
        // Optionally pause playback when app goes to background
        // mediaPlayer?.pause()
        // isCurrentlyPlaying = false
        // playPauseButton.text = "▶️"
    }
}
