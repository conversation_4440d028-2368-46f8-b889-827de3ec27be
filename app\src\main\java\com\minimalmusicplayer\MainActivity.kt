package com.minimalmusicplayer

import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.content.ServiceConnection
import android.content.pm.PackageManager
import android.net.Uri
import android.os.Bundle
import android.os.IBinder
import android.view.Menu
import android.view.MenuItem
import android.view.View
import android.widget.Toast
import androidx.activity.result.contract.ActivityResultContracts
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import com.minimalmusicplayer.adapter.SongAdapter
import com.minimalmusicplayer.databinding.ActivityMainBinding
import com.minimalmusicplayer.model.Song
import com.minimalmusicplayer.service.MusicService
import com.minimalmusicplayer.util.ErrorHandler
import com.minimalmusicplayer.util.FileScanner
import com.minimalmusicplayer.util.PermissionHelper
import com.minimalmusicplayer.util.PreferenceManager
import kotlinx.coroutines.launch

class MainActivity : AppCompatActivity(), MusicService.PlaybackListener {
    
    private lateinit var binding: ActivityMainBinding
    private lateinit var songAdapter: SongAdapter
    private lateinit var fileScanner: FileScanner
    private lateinit var preferenceManager: PreferenceManager
    
    private var musicService: MusicService? = null
    private var isServiceBound = false
    
    private var songs = listOf<Song>()
    private var currentSong: Song? = null
    
    private val directorySelectionLauncher = registerForActivityResult(
        ActivityResultContracts.StartActivityForResult()
    ) { result ->
        if (result.resultCode == RESULT_OK) {
            loadMusic()
        }
    }
    
    private val serviceConnection = object : ServiceConnection {
        override fun onServiceConnected(name: ComponentName?, service: IBinder?) {
            try {
                val binder = service as MusicService.MusicBinder
                musicService = binder.getService()
                isServiceBound = true

                musicService?.addPlaybackListener(this@MainActivity)
                updatePlaybackUI()
                ErrorHandler.logInfo("MainActivity", "Music service connected successfully")
            } catch (e: Exception) {
                ErrorHandler.handleServiceError(this@MainActivity, e)
                isServiceBound = false
            }
        }

        override fun onServiceDisconnected(name: ComponentName?) {
            musicService?.removePlaybackListener(this@MainActivity)
            musicService = null
            isServiceBound = false
            ErrorHandler.logWarning("MainActivity", "Music service disconnected")
        }
    }
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        // Create a layout with a button to test functionality
        val layout = android.widget.LinearLayout(this).apply {
            orientation = android.widget.LinearLayout.VERTICAL
            setPadding(48, 48, 48, 48)
        }

        val textView = android.widget.TextView(this).apply {
            text = "🎵 Minimal Music Player\n\n" +
                    "✅ App is working!\n\n" +
                    "This version is stable and ready for features."
            textSize = 16f
            gravity = android.view.Gravity.CENTER
        }

        val button = android.widget.Button(this).apply {
            text = "Test Button (Safe)"
            setOnClickListener {
                android.widget.Toast.makeText(
                    this@MainActivity,
                    "Button clicked! App is responsive.",
                    android.widget.Toast.LENGTH_SHORT
                ).show()
            }
        }

        layout.addView(textView)
        layout.addView(button)
        setContentView(layout)

        // Initialize basic components safely
        try {
            preferenceManager = PreferenceManager(this)
        } catch (e: Exception) {
            android.util.Log.e("MainActivity", "Error initializing preferences", e)
        }
    }

    // Override menu methods to prevent crashes
    override fun onCreateOptionsMenu(menu: android.view.Menu?): Boolean {
        // Don't inflate any menu to avoid crashes
        return false
    }

    override fun onOptionsItemSelected(item: android.view.MenuItem): Boolean {
        // Safe handling
        return super.onOptionsItemSelected(item)
    }

    private fun showWelcomeMessage() {
        try {
            val textView = findViewById<android.widget.TextView>(R.id.textViewEmptyState)
            textView?.apply {
                text = "🎵 Welcome to Minimal Music Player!\n\n" +
                        "This is a working version.\n\n" +
                        "Features will be added step by step to avoid crashes.\n\n" +
                        "Current status: Basic app structure is working."
                visibility = View.VISIBLE
            }

            // Hide other views for now
            findViewById<View>(R.id.recyclerViewSongs)?.visibility = View.GONE
            findViewById<View>(R.id.playerContainer)?.visibility = View.GONE

        } catch (e: Exception) {
            // Fallback to simple text view
            val textView = android.widget.TextView(this).apply {
                text = "Minimal Music Player is running!\n\nBasic version to test stability."
                setPadding(32, 32, 32, 32)
            }
            setContentView(textView)
        }
    }
    
    private fun setupToolbar() {
        setSupportActionBar(binding.toolbar)
        supportActionBar?.title = "Minimal Music Player"
    }
    
    private fun setupRecyclerView() {
        songAdapter = SongAdapter { song, position ->
            playSong(song, position)
        }
        
        binding.recyclerViewSongs.apply {
            layoutManager = LinearLayoutManager(this@MainActivity)
            adapter = songAdapter
        }
    }
    
    private fun setupPlaybackControls() {
        // Initially hide the player container
        binding.playerContainer.visibility = View.GONE

        binding.buttonPlayPause.setOnClickListener {
            togglePlayback()
        }

        binding.buttonPrevious.setOnClickListener {
            musicService?.skipToPrevious()
        }

        binding.buttonNext.setOnClickListener {
            musicService?.skipToNext()
        }

        binding.playerContainer.setOnClickListener {
            // Could expand to full player view in future
        }
    }
    
    private fun checkPermissionsAndLoadMusic() {
        if (PermissionHelper.hasStoragePermission(this)) {
            loadMusic()
        } else {
            requestPermissions()
        }
    }
    
    private fun requestPermissions() {
        if (PermissionHelper.shouldShowRationale(this)) {
            Toast.makeText(
                this,
                "Storage permission is required to access your music files",
                Toast.LENGTH_LONG
            ).show()
        }
        PermissionHelper.requestStoragePermission(this)
    }
    
    override fun onRequestPermissionsResult(
        requestCode: Int,
        permissions: Array<out String>,
        grantResults: IntArray
    ) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
        
        if (requestCode == PermissionHelper.STORAGE_PERMISSION_REQUEST_CODE) {
            if (PermissionHelper.isPermissionGranted(grantResults)) {
                loadMusic()
            } else {
                showPermissionDeniedMessage()
            }
        }
    }
    
    private fun showPermissionDeniedMessage() {
        binding.textViewEmptyState.apply {
            text = "📁 Storage Permission Required\n\n" +
                    "This app needs storage permission to access your music files.\n\n" +
                    "To grant permission:\n" +
                    "1. Go to Settings > Apps > Minimal Music Player\n" +
                    "2. Tap 'Permissions'\n" +
                    "3. Enable 'Files and media' or 'Storage'\n\n" +
                    "Then restart the app."
            visibility = View.VISIBLE
        }
        binding.recyclerViewSongs.visibility = View.GONE
        binding.playerContainer.visibility = View.GONE
    }
    
    private fun loadMusic() {
        binding.progressBar.visibility = View.VISIBLE
        
        lifecycleScope.launch {
            try {
                val selectedDirectories = preferenceManager.getSelectedDirectories()
                
                songs = if (selectedDirectories.isNotEmpty()) {
                    // Scan selected directories
                    val allSongs = mutableListOf<Song>()
                    for (directoryUri in selectedDirectories) {
                        val uri = Uri.parse(directoryUri)
                        val directorySongs = fileScanner.scanDirectory(uri)
                        allSongs.addAll(directorySongs)
                    }
                    allSongs.sortedBy { it.getDisplayTitle() }
                } else {
                    // Fallback to MediaStore
                    fileScanner.scanMediaStore()
                }
                
                binding.progressBar.visibility = View.GONE
                
                if (songs.isNotEmpty()) {
                    songAdapter.updateSongs(songs)
                    binding.recyclerViewSongs.visibility = View.VISIBLE
                    binding.textViewEmptyState.visibility = View.GONE
                    
                    // Start music service
                    startMusicService()
                } else {
                    showEmptyState()
                }
                
            } catch (e: Exception) {
                binding.progressBar.visibility = View.GONE
                ErrorHandler.handleScanError(this@MainActivity, e)
                showEmptyState()
            }
        }
    }
    
    private fun showEmptyState() {
        binding.textViewEmptyState.apply {
            text = if (preferenceManager.getSelectedDirectories().isEmpty()) {
                "Welcome to Minimal Music Player!\n\n" +
                "To get started:\n" +
                "1. Tap the folder icon in the toolbar\n" +
                "2. Select directories containing your music\n" +
                "3. Tap 'Scan All Directories'\n\n" +
                "Supported formats: MP3, FLAC, OGG, M4A, AAC, WAV, WMA"
            } else {
                "No music files found in selected directories.\n\n" +
                "Try:\n" +
                "• Adding more directories with the folder icon\n" +
                "• Checking if your music files are in supported formats\n" +
                "• Using the refresh option in the menu"
            }
            visibility = View.VISIBLE
        }
        binding.recyclerViewSongs.visibility = View.GONE
        binding.playerContainer.visibility = View.GONE
    }
    
    private fun startMusicService() {
        try {
            val intent = Intent(this, MusicService::class.java)
            startService(intent)
            bindService(intent, serviceConnection, Context.BIND_AUTO_CREATE)
        } catch (e: Exception) {
            ErrorHandler.handleServiceError(this, e)
        }
    }
    
    private fun playSong(song: Song, position: Int) {
        musicService?.let { service ->
            service.setPlaylist(songs, position)
            service.play()
            updateCurrentSong(song)
        }
    }
    
    private fun togglePlayback() {
        musicService?.let { service ->
            if (service.isPlaying()) {
                service.pause()
            } else {
                service.play()
            }
        }
    }
    
    private fun updateCurrentSong(song: Song?) {
        currentSong = song
        
        if (song != null) {
            binding.textViewSongTitle.text = song.getDisplayTitle()
            binding.textViewSongArtist.text = song.getDisplayArtist()
            binding.playerContainer.visibility = View.VISIBLE
        } else {
            binding.playerContainer.visibility = View.GONE
        }
        
        songAdapter.setCurrentSong(song)
    }
    
    private fun updatePlaybackUI() {
        musicService?.let { service ->
            val isPlaying = service.isPlaying()
            binding.buttonPlayPause.setImageResource(
                if (isPlaying) R.drawable.ic_pause else R.drawable.ic_play
            )
            
            updateCurrentSong(service.getCurrentSong())
        }
    }
    

    
    // MusicService.PlaybackListener implementation
    override fun onSongChanged(song: Song?) {
        runOnUiThread {
            updateCurrentSong(song)
        }
    }
    
    override fun onPlaybackStateChanged(isPlaying: Boolean) {
        runOnUiThread {
            binding.buttonPlayPause.setImageResource(
                if (isPlaying) R.drawable.ic_pause else R.drawable.ic_play
            )
        }
    }
    
    override fun onPositionChanged(position: Long, duration: Long) {
        // Could implement progress bar here
    }
    
    override fun onDestroy() {
        super.onDestroy()
        if (isServiceBound) {
            musicService?.removePlaybackListener(this)
            unbindService(serviceConnection)
        }
    }
}
