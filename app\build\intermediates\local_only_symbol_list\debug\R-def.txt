R_DEF: Internal format may change without notice
local
color accent
color black
color current_song_background
color current_song_text
color primary
color primary_dark
color primary_text
color purple_200
color purple_500
color purple_700
color secondary_text
color teal_200
color teal_700
color white
color widget_background
color widget_button_background
drawable ic_add
drawable ic_delete
drawable ic_folder
drawable ic_pause
drawable ic_play
drawable ic_refresh
drawable ic_skip_next
drawable ic_skip_previous
drawable widget_background
drawable widget_button_background
drawable widget_preview
id action_refresh
id action_select_directories
id buttonNext
id buttonPlayPause
id buttonPrevious
id buttonRemove
id buttonScanAll
id fabAddDirectory
id playerContainer
id progressBar
id recyclerViewDirectories
id recyclerViewSongs
id textViewArtist
id textViewDirectoryName
id textViewDirectoryPath
id textViewDuration
id textViewEmptyState
id textViewSongArtist
id textViewSongTitle
id textViewTitle
id toolbar
id widget_artist
id widget_container
id widget_next
id widget_play_pause
id widget_previous
id widget_song_title
layout activity_directory_selection
layout activity_main
layout item_directory
layout item_song
layout widget_music_player
menu main_menu
mipmap ic_launcher
mipmap ic_launcher_round
string action_refresh
string action_select_directories
string app_name
string directory_add_failed
string directory_added
string directory_remove_failed
string directory_removed
string empty_no_directories
string empty_no_songs
string empty_no_songs_directories
string no_song_playing
string notification_channel_description
string notification_channel_name
string permission_denied_message
string permission_storage_rationale
string scan_completed
string scan_failed
string unknown_album
string unknown_artist
string unknown_title
string widget_description
style Theme.MinimalMusicPlayer
xml backup_rules
xml data_extraction_rules
xml music_widget_info
