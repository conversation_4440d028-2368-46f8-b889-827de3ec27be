package com.minimalmusicplayer.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.minimalmusicplayer.databinding.ItemDirectoryBinding

class DirectoryAdapter(
    private val onRemoveClick: (String) -> Unit
) : RecyclerView.Adapter<DirectoryAdapter.DirectoryViewHolder>() {
    
    private var directories = listOf<DirectoryInfo>()
    
    data class DirectoryInfo(
        val uri: String,
        val displayName: String
    )
    
    fun updateDirectories(newDirectories: List<DirectoryInfo>) {
        directories = newDirectories
        notifyDataSetChanged()
    }
    
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): DirectoryViewHolder {
        val binding = ItemDirectoryBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
        return DirectoryViewHolder(binding)
    }
    
    override fun onBindViewHolder(holder: <PERSON><PERSON><PERSON>wHolder, position: Int) {
        holder.bind(directories[position])
    }
    
    override fun getItemCount(): Int = directories.size
    
    inner class DirectoryViewHolder(
        private val binding: ItemDirectoryBinding
    ) : RecyclerView.ViewHolder(binding.root) {
        
        fun bind(directoryInfo: DirectoryInfo) {
            binding.textViewDirectoryName.text = directoryInfo.displayName
            binding.textViewDirectoryPath.text = directoryInfo.uri
            
            binding.buttonRemove.setOnClickListener {
                onRemoveClick(directoryInfo.uri)
            }
        }
    }
}
