package com.minimalmusicplayer.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.RecyclerView
import com.minimalmusicplayer.R
import com.minimalmusicplayer.databinding.ItemSongBinding
import com.minimalmusicplayer.model.Song

class SongAdapter(
    private val onSongClick: (Song, Int) -> Unit
) : RecyclerView.Adapter<SongAdapter.SongViewHolder>() {
    
    private var songs = listOf<Song>()
    private var currentSong: Song? = null
    
    fun updateSongs(newSongs: List<Song>) {
        songs = newSongs
        notifyDataSetChanged()
    }
    
    fun setCurrentSong(song: Song?) {
        val oldCurrentIndex = songs.indexOfFirst { it.path == currentSong?.path }
        val newCurrentIndex = songs.indexOfFirst { it.path == song?.path }
        
        currentSong = song
        
        if (oldCurrentIndex != -1) {
            notifyItemChanged(oldCurrentIndex)
        }
        if (newCurrentIndex != -1) {
            notifyItemChanged(newCurrentIndex)
        }
    }
    
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): SongViewHolder {
        val binding = ItemSongBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
        return SongViewHolder(binding)
    }
    
    override fun onBindViewHolder(holder: SongViewHolder, position: Int) {
        holder.bind(songs[position], position)
    }
    
    override fun getItemCount(): Int = songs.size
    
    inner class SongViewHolder(
        private val binding: ItemSongBinding
    ) : RecyclerView.ViewHolder(binding.root) {
        
        fun bind(song: Song, position: Int) {
            binding.textViewTitle.text = song.getDisplayTitle()
            binding.textViewArtist.text = song.getDisplayArtist()
            binding.textViewDuration.text = song.getDurationString()
            
            // Highlight current song
            val isCurrentSong = song.path == currentSong?.path
            val backgroundColor = if (isCurrentSong) {
                ContextCompat.getColor(binding.root.context, R.color.current_song_background)
            } else {
                ContextCompat.getColor(binding.root.context, android.R.color.transparent)
            }
            binding.root.setBackgroundColor(backgroundColor)
            
            val textColor = if (isCurrentSong) {
                ContextCompat.getColor(binding.root.context, R.color.current_song_text)
            } else {
                ContextCompat.getColor(binding.root.context, R.color.primary_text)
            }
            binding.textViewTitle.setTextColor(textColor)
            binding.textViewArtist.setTextColor(textColor)
            
            binding.root.setOnClickListener {
                onSongClick(song, position)
            }
        }
    }
}
