  Manifest android  R android  
permission android.Manifest  READ_EXTERNAL_STORAGE android.Manifest.permission  READ_MEDIA_AUDIO android.Manifest.permission  color 	android.R  transparent android.R.color  Activity android.app  AudioAttributes android.app  BrowserRoot android.app  Build android.app  C android.app  ErrorHandler android.app  	ExoPlayer android.app  Intent android.app  MainActivity android.app  	MediaItem android.app  MediaSessionCompat android.app  MusicWidgetProvider android.app  NOTIFICATION_CHANNEL_ID android.app  NOTIFICATION_ID android.app  Notification android.app  NotificationChannel android.app  NotificationManager android.app  
PendingIntent android.app  PlaybackException android.app  PlaybackStateCompat android.app  Player android.app  PlayerNotificationManager android.app  PreferenceManager android.app  START_STICKY android.app  Uri android.app  android android.app  apply android.app  coerceIn android.app  currentIndex android.app  currentSong android.app  	emptyList android.app  forEach android.app  handleTrackCompletion android.app  indices android.app  
isNotEmpty android.app  java android.app  let android.app  map android.app  
mutableListOf android.app  mutableSetOf android.app  notifyPlaybackStateChanged android.app  notifySongChanged android.app  pause android.app  play android.app  playlist android.app  seekTo android.app  
skipToNext android.app  skipToPrevious android.app  startForeground android.app  stop android.app  stopForeground android.app  stopSelf android.app  takeIf android.app  updateCurrentSong android.app  updatePlaybackState android.app  Activity android.app.Activity  !ActivityDirectorySelectionBinding android.app.Activity  ActivityMainBinding android.app.Activity  ActivityResultContracts android.app.Activity  Array android.app.Activity  Boolean android.app.Activity  Bundle android.app.Activity  
ComponentName android.app.Activity  Context android.app.Activity  DirectoryAdapter android.app.Activity  DirectorySelectionActivity android.app.Activity  DocumentsContract android.app.Activity  ErrorHandler android.app.Activity  	Exception android.app.Activity  FileScanner android.app.Activity  IBinder android.app.Activity  Int android.app.Activity  IntArray android.app.Activity  Intent android.app.Activity  LinearLayoutManager android.app.Activity  Long android.app.Activity  Menu android.app.Activity  MenuItem android.app.Activity  MusicService android.app.Activity  PermissionHelper android.app.Activity  PreferenceManager android.app.Activity  R android.app.Activity  	RESULT_OK android.app.Activity  ServiceConnection android.app.Activity  Song android.app.Activity  SongAdapter android.app.Activity  String android.app.Activity  Toast android.app.Activity  Uri android.app.Activity  View android.app.Activity  apply android.app.Activity  bindService android.app.Activity  binding android.app.Activity  checkPermissionsAndLoadMusic android.app.Activity  directoryAdapter android.app.Activity  fileScanner android.app.Activity  finish android.app.Activity  getDirectoryDisplayName android.app.Activity  handleDirectorySelection android.app.Activity  ifEmpty android.app.Activity  invoke android.app.Activity  
isNotEmpty android.app.Activity  isServiceBound android.app.Activity  java android.app.Activity  launch android.app.Activity  let android.app.Activity  lifecycleScope android.app.Activity  listOf android.app.Activity  	loadMusic android.app.Activity  loadSelectedDirectories android.app.Activity  map android.app.Activity  musicService android.app.Activity  
mutableListOf android.app.Activity  
onBackPressed android.app.Activity  onCreate android.app.Activity  	onDestroy android.app.Activity  onOptionsItemSelected android.app.Activity  onRequestPermissionsResult android.app.Activity  openDirectoryPicker android.app.Activity  playSong android.app.Activity  
plusAssign android.app.Activity  preferenceManager android.app.Activity  registerForActivityResult android.app.Activity  removeDirectory android.app.Activity  requestPermissions android.app.Activity  
runOnUiThread android.app.Activity  scanAllDirectories android.app.Activity  setContentView android.app.Activity  	setResult android.app.Activity  setSupportActionBar android.app.Activity  setupClickListeners android.app.Activity  setupPlaybackControls android.app.Activity  setupRecyclerView android.app.Activity  setupToolbar android.app.Activity  showEmptyState android.app.Activity  showPermissionDeniedMessage android.app.Activity  songAdapter android.app.Activity  songs android.app.Activity  sortedBy android.app.Activity  split android.app.Activity  startMusicService android.app.Activity  startService android.app.Activity  toMutableSet android.app.Activity  togglePlayback android.app.Activity  
unbindService android.app.Activity  updateCurrentSong android.app.Activity  updatePlaybackUI android.app.Activity  VISIBILITY_PUBLIC android.app.Notification  Notification android.app.NotificationChannel  apply android.app.NotificationChannel  description android.app.NotificationChannel  getAPPLY android.app.NotificationChannel  getApply android.app.NotificationChannel  getDESCRIPTION android.app.NotificationChannel  getDescription android.app.NotificationChannel  getLOCKSCREENVisibility android.app.NotificationChannel  getLockscreenVisibility android.app.NotificationChannel  lockscreenVisibility android.app.NotificationChannel  setDescription android.app.NotificationChannel  setLockscreenVisibility android.app.NotificationChannel  setShowBadge android.app.NotificationChannel  IMPORTANCE_LOW android.app.NotificationManager  createNotificationChannel android.app.NotificationManager  FLAG_IMMUTABLE android.app.PendingIntent  FLAG_UPDATE_CURRENT android.app.PendingIntent  getActivity android.app.PendingIntent  getBroadcast android.app.PendingIntent  AudioAttributes android.app.Service  Binder android.app.Service  Boolean android.app.Service  BrowserRoot android.app.Service  Build android.app.Service  Bundle android.app.Service  C android.app.Service  CharSequence android.app.Service  ErrorHandler android.app.Service  	ExoPlayer android.app.Service  IBinder android.app.Service  Int android.app.Service  Intent android.app.Service  List android.app.Service  Long android.app.Service  MainActivity android.app.Service  MediaBrowserCompat android.app.Service  	MediaItem android.app.Service  MediaSessionCompat android.app.Service  MusicService android.app.Service  MusicWidgetProvider android.app.Service  MutableList android.app.Service  NOTIFICATION_CHANNEL_ID android.app.Service  NOTIFICATION_ID android.app.Service  Notification android.app.Service  NotificationChannel android.app.Service  NotificationManager android.app.Service  
PendingIntent android.app.Service  PlaybackException android.app.Service  PlaybackStateCompat android.app.Service  Player android.app.Service  PlayerNotificationManager android.app.Service  PreferenceManager android.app.Service  START_STICKY android.app.Service  Song android.app.Service  String android.app.Service  Uri android.app.Service  addPlaybackListener android.app.Service  android android.app.Service  apply android.app.Service  coerceIn android.app.Service  createNotificationChannel android.app.Service  currentIndex android.app.Service  currentSong android.app.Service  	emptyList android.app.Service  getCurrentSong android.app.Service  getSystemService android.app.Service  handleTrackCompletion android.app.Service  indices android.app.Service  initializeMediaSession android.app.Service  initializeNotification android.app.Service  initializePlayer android.app.Service  invoke android.app.Service  
isNotEmpty android.app.Service  	isPlaying android.app.Service  java android.app.Service  let android.app.Service  map android.app.Service  
mutableListOf android.app.Service  mutableSetOf android.app.Service  notifyPlaybackStateChanged android.app.Service  notifySongChanged android.app.Service  onCreate android.app.Service  	onDestroy android.app.Service  pause android.app.Service  play android.app.Service  playlist android.app.Service  preparePlaylist android.app.Service  removePlaybackListener android.app.Service  seekTo android.app.Service  setPlaylist android.app.Service  
skipToNext android.app.Service  skipToPrevious android.app.Service  startForeground android.app.Service  stop android.app.Service  stopForeground android.app.Service  stopSelf android.app.Service  takeIf android.app.Service  updateCurrentSong android.app.Service  updatePlaybackState android.app.Service  updateWidget android.app.Service  AppWidgetManager android.appwidget  AppWidgetProvider android.appwidget  getAppWidgetIds "android.appwidget.AppWidgetManager  getInstance "android.appwidget.AppWidgetManager  updateAppWidget "android.appwidget.AppWidgetManager  ACTION_NEXT #android.appwidget.AppWidgetProvider  ACTION_PLAY_PAUSE #android.appwidget.AppWidgetProvider  ACTION_PREVIOUS #android.appwidget.AppWidgetProvider  AppWidgetManager #android.appwidget.AppWidgetProvider  Boolean #android.appwidget.AppWidgetProvider  
ComponentName #android.appwidget.AppWidgetProvider  Context #android.appwidget.AppWidgetProvider  ErrorHandler #android.appwidget.AppWidgetProvider  	Exception #android.appwidget.AppWidgetProvider  Int #android.appwidget.AppWidgetProvider  IntArray #android.appwidget.AppWidgetProvider  Intent #android.appwidget.AppWidgetProvider  MainActivity #android.appwidget.AppWidgetProvider  MusicService #android.appwidget.AppWidgetProvider  MusicWidgetProvider #android.appwidget.AppWidgetProvider  
PendingIntent #android.appwidget.AppWidgetProvider  R #android.appwidget.AppWidgetProvider  RemoteViews #android.appwidget.AppWidgetProvider  String #android.appwidget.AppWidgetProvider  apply #android.appwidget.AppWidgetProvider  java #android.appwidget.AppWidgetProvider  	onReceive #android.appwidget.AppWidgetProvider  sendMusicServiceCommand #android.appwidget.AppWidgetProvider  updateAppWidget #android.appwidget.AppWidgetProvider  
ComponentName android.content  ContentResolver android.content  Context android.content  Intent android.content  ServiceConnection android.content  SharedPreferences android.content  ACTION_NEXT !android.content.BroadcastReceiver  ACTION_PLAY_PAUSE !android.content.BroadcastReceiver  ACTION_PREVIOUS !android.content.BroadcastReceiver  AppWidgetManager !android.content.BroadcastReceiver  Boolean !android.content.BroadcastReceiver  
ComponentName !android.content.BroadcastReceiver  Context !android.content.BroadcastReceiver  ErrorHandler !android.content.BroadcastReceiver  	Exception !android.content.BroadcastReceiver  Int !android.content.BroadcastReceiver  IntArray !android.content.BroadcastReceiver  Intent !android.content.BroadcastReceiver  MainActivity !android.content.BroadcastReceiver  MusicService !android.content.BroadcastReceiver  MusicWidgetProvider !android.content.BroadcastReceiver  
PendingIntent !android.content.BroadcastReceiver  R !android.content.BroadcastReceiver  RemoteViews !android.content.BroadcastReceiver  String !android.content.BroadcastReceiver  apply !android.content.BroadcastReceiver  java !android.content.BroadcastReceiver  	onReceive !android.content.BroadcastReceiver  sendMusicServiceCommand !android.content.BroadcastReceiver  updateAppWidget !android.content.BroadcastReceiver  query android.content.ContentResolver  releasePersistableUriPermission android.content.ContentResolver  takePersistableUriPermission android.content.ContentResolver  Activity android.content.Context  !ActivityDirectorySelectionBinding android.content.Context  ActivityMainBinding android.content.Context  ActivityResultContracts android.content.Context  Array android.content.Context  AudioAttributes android.content.Context  BIND_AUTO_CREATE android.content.Context  Binder android.content.Context  Boolean android.content.Context  BrowserRoot android.content.Context  Build android.content.Context  Bundle android.content.Context  C android.content.Context  CharSequence android.content.Context  
ComponentName android.content.Context  Context android.content.Context  DirectoryAdapter android.content.Context  DirectorySelectionActivity android.content.Context  DocumentsContract android.content.Context  ErrorHandler android.content.Context  	Exception android.content.Context  	ExoPlayer android.content.Context  FileScanner android.content.Context  IBinder android.content.Context  Int android.content.Context  IntArray android.content.Context  Intent android.content.Context  LinearLayoutManager android.content.Context  List android.content.Context  Long android.content.Context  MODE_PRIVATE android.content.Context  MainActivity android.content.Context  MediaBrowserCompat android.content.Context  	MediaItem android.content.Context  MediaSessionCompat android.content.Context  Menu android.content.Context  MenuItem android.content.Context  MusicService android.content.Context  MusicWidgetProvider android.content.Context  MutableList android.content.Context  NOTIFICATION_CHANNEL_ID android.content.Context  NOTIFICATION_ID android.content.Context  Notification android.content.Context  NotificationChannel android.content.Context  NotificationManager android.content.Context  
PendingIntent android.content.Context  PermissionHelper android.content.Context  PlaybackException android.content.Context  PlaybackStateCompat android.content.Context  Player android.content.Context  PlayerNotificationManager android.content.Context  PreferenceManager android.content.Context  R android.content.Context  	RESULT_OK android.content.Context  START_STICKY android.content.Context  ServiceConnection android.content.Context  Song android.content.Context  SongAdapter android.content.Context  String android.content.Context  Toast android.content.Context  Uri android.content.Context  View android.content.Context  addPlaybackListener android.content.Context  android android.content.Context  apply android.content.Context  bindService android.content.Context  binding android.content.Context  checkPermissionsAndLoadMusic android.content.Context  coerceIn android.content.Context  contentResolver android.content.Context  createNotificationChannel android.content.Context  currentIndex android.content.Context  currentSong android.content.Context  directoryAdapter android.content.Context  	emptyList android.content.Context  fileScanner android.content.Context  finish android.content.Context  getCONTENTResolver android.content.Context  getContentResolver android.content.Context  getCurrentSong android.content.Context  getDirectoryDisplayName android.content.Context  getPACKAGEName android.content.Context  getPackageName android.content.Context  getSharedPreferences android.content.Context  getSystemService android.content.Context  handleDirectorySelection android.content.Context  handleTrackCompletion android.content.Context  ifEmpty android.content.Context  indices android.content.Context  initializeMediaSession android.content.Context  initializeNotification android.content.Context  initializePlayer android.content.Context  invoke android.content.Context  
isNotEmpty android.content.Context  	isPlaying android.content.Context  isServiceBound android.content.Context  java android.content.Context  launch android.content.Context  let android.content.Context  lifecycleScope android.content.Context  listOf android.content.Context  	loadMusic android.content.Context  loadSelectedDirectories android.content.Context  map android.content.Context  musicService android.content.Context  
mutableListOf android.content.Context  mutableSetOf android.content.Context  notifyPlaybackStateChanged android.content.Context  notifySongChanged android.content.Context  
onBackPressed android.content.Context  onCreate android.content.Context  	onDestroy android.content.Context  onOptionsItemSelected android.content.Context  onRequestPermissionsResult android.content.Context  openDirectoryPicker android.content.Context  packageName android.content.Context  pause android.content.Context  play android.content.Context  playSong android.content.Context  playlist android.content.Context  
plusAssign android.content.Context  preferenceManager android.content.Context  preparePlaylist android.content.Context  registerForActivityResult android.content.Context  removeDirectory android.content.Context  removePlaybackListener android.content.Context  requestPermissions android.content.Context  
runOnUiThread android.content.Context  scanAllDirectories android.content.Context  seekTo android.content.Context  setContentResolver android.content.Context  setContentView android.content.Context  setPackageName android.content.Context  setPlaylist android.content.Context  	setResult android.content.Context  setSupportActionBar android.content.Context  setupClickListeners android.content.Context  setupPlaybackControls android.content.Context  setupRecyclerView android.content.Context  setupToolbar android.content.Context  showEmptyState android.content.Context  showPermissionDeniedMessage android.content.Context  
skipToNext android.content.Context  skipToPrevious android.content.Context  songAdapter android.content.Context  songs android.content.Context  sortedBy android.content.Context  split android.content.Context  startForeground android.content.Context  startMusicService android.content.Context  startService android.content.Context  stop android.content.Context  stopForeground android.content.Context  stopSelf android.content.Context  takeIf android.content.Context  toMutableSet android.content.Context  togglePlayback android.content.Context  
unbindService android.content.Context  updateCurrentSong android.content.Context  updatePlaybackState android.content.Context  updatePlaybackUI android.content.Context  updateWidget android.content.Context  Activity android.content.ContextWrapper  !ActivityDirectorySelectionBinding android.content.ContextWrapper  ActivityMainBinding android.content.ContextWrapper  ActivityResultContracts android.content.ContextWrapper  Array android.content.ContextWrapper  AudioAttributes android.content.ContextWrapper  Binder android.content.ContextWrapper  Boolean android.content.ContextWrapper  BrowserRoot android.content.ContextWrapper  Build android.content.ContextWrapper  Bundle android.content.ContextWrapper  C android.content.ContextWrapper  CharSequence android.content.ContextWrapper  
ComponentName android.content.ContextWrapper  Context android.content.ContextWrapper  DirectoryAdapter android.content.ContextWrapper  DirectorySelectionActivity android.content.ContextWrapper  DocumentsContract android.content.ContextWrapper  ErrorHandler android.content.ContextWrapper  	Exception android.content.ContextWrapper  	ExoPlayer android.content.ContextWrapper  FileScanner android.content.ContextWrapper  IBinder android.content.ContextWrapper  Int android.content.ContextWrapper  IntArray android.content.ContextWrapper  Intent android.content.ContextWrapper  LinearLayoutManager android.content.ContextWrapper  List android.content.ContextWrapper  Long android.content.ContextWrapper  MainActivity android.content.ContextWrapper  MediaBrowserCompat android.content.ContextWrapper  	MediaItem android.content.ContextWrapper  MediaSessionCompat android.content.ContextWrapper  Menu android.content.ContextWrapper  MenuItem android.content.ContextWrapper  MusicService android.content.ContextWrapper  MusicWidgetProvider android.content.ContextWrapper  MutableList android.content.ContextWrapper  NOTIFICATION_CHANNEL_ID android.content.ContextWrapper  NOTIFICATION_ID android.content.ContextWrapper  Notification android.content.ContextWrapper  NotificationChannel android.content.ContextWrapper  NotificationManager android.content.ContextWrapper  
PendingIntent android.content.ContextWrapper  PermissionHelper android.content.ContextWrapper  PlaybackException android.content.ContextWrapper  PlaybackStateCompat android.content.ContextWrapper  Player android.content.ContextWrapper  PlayerNotificationManager android.content.ContextWrapper  PreferenceManager android.content.ContextWrapper  R android.content.ContextWrapper  	RESULT_OK android.content.ContextWrapper  START_STICKY android.content.ContextWrapper  ServiceConnection android.content.ContextWrapper  Song android.content.ContextWrapper  SongAdapter android.content.ContextWrapper  String android.content.ContextWrapper  Toast android.content.ContextWrapper  Uri android.content.ContextWrapper  View android.content.ContextWrapper  addPlaybackListener android.content.ContextWrapper  android android.content.ContextWrapper  apply android.content.ContextWrapper  bindService android.content.ContextWrapper  binding android.content.ContextWrapper  checkPermissionsAndLoadMusic android.content.ContextWrapper  coerceIn android.content.ContextWrapper  createNotificationChannel android.content.ContextWrapper  currentIndex android.content.ContextWrapper  currentSong android.content.ContextWrapper  directoryAdapter android.content.ContextWrapper  	emptyList android.content.ContextWrapper  fileScanner android.content.ContextWrapper  finish android.content.ContextWrapper  getCurrentSong android.content.ContextWrapper  getDirectoryDisplayName android.content.ContextWrapper  getSystemService android.content.ContextWrapper  handleDirectorySelection android.content.ContextWrapper  handleTrackCompletion android.content.ContextWrapper  ifEmpty android.content.ContextWrapper  indices android.content.ContextWrapper  initializeMediaSession android.content.ContextWrapper  initializeNotification android.content.ContextWrapper  initializePlayer android.content.ContextWrapper  invoke android.content.ContextWrapper  
isNotEmpty android.content.ContextWrapper  	isPlaying android.content.ContextWrapper  isServiceBound android.content.ContextWrapper  java android.content.ContextWrapper  launch android.content.ContextWrapper  let android.content.ContextWrapper  lifecycleScope android.content.ContextWrapper  listOf android.content.ContextWrapper  	loadMusic android.content.ContextWrapper  loadSelectedDirectories android.content.ContextWrapper  map android.content.ContextWrapper  musicService android.content.ContextWrapper  
mutableListOf android.content.ContextWrapper  mutableSetOf android.content.ContextWrapper  notifyPlaybackStateChanged android.content.ContextWrapper  notifySongChanged android.content.ContextWrapper  
onBackPressed android.content.ContextWrapper  onCreate android.content.ContextWrapper  	onDestroy android.content.ContextWrapper  onOptionsItemSelected android.content.ContextWrapper  onRequestPermissionsResult android.content.ContextWrapper  openDirectoryPicker android.content.ContextWrapper  pause android.content.ContextWrapper  play android.content.ContextWrapper  playSong android.content.ContextWrapper  playlist android.content.ContextWrapper  
plusAssign android.content.ContextWrapper  preferenceManager android.content.ContextWrapper  preparePlaylist android.content.ContextWrapper  registerForActivityResult android.content.ContextWrapper  removeDirectory android.content.ContextWrapper  removePlaybackListener android.content.ContextWrapper  requestPermissions android.content.ContextWrapper  
runOnUiThread android.content.ContextWrapper  scanAllDirectories android.content.ContextWrapper  seekTo android.content.ContextWrapper  setContentView android.content.ContextWrapper  setPlaylist android.content.ContextWrapper  	setResult android.content.ContextWrapper  setSupportActionBar android.content.ContextWrapper  setupClickListeners android.content.ContextWrapper  setupPlaybackControls android.content.ContextWrapper  setupRecyclerView android.content.ContextWrapper  setupToolbar android.content.ContextWrapper  showEmptyState android.content.ContextWrapper  showPermissionDeniedMessage android.content.ContextWrapper  
skipToNext android.content.ContextWrapper  skipToPrevious android.content.ContextWrapper  songAdapter android.content.ContextWrapper  songs android.content.ContextWrapper  sortedBy android.content.ContextWrapper  split android.content.ContextWrapper  startForeground android.content.ContextWrapper  startMusicService android.content.ContextWrapper  startService android.content.ContextWrapper  stop android.content.ContextWrapper  stopForeground android.content.ContextWrapper  stopSelf android.content.ContextWrapper  takeIf android.content.ContextWrapper  toMutableSet android.content.ContextWrapper  togglePlayback android.content.ContextWrapper  
unbindService android.content.ContextWrapper  updateCurrentSong android.content.ContextWrapper  updatePlaybackState android.content.ContextWrapper  updatePlaybackUI android.content.ContextWrapper  updateWidget android.content.ContextWrapper  ACTION_NEXT android.content.Intent  ACTION_OPEN_DOCUMENT_TREE android.content.Intent  ACTION_PLAY_PAUSE android.content.Intent  ACTION_PREVIOUS android.content.Intent  %FLAG_GRANT_PERSISTABLE_URI_PERMISSION android.content.Intent  FLAG_GRANT_READ_URI_PERMISSION android.content.Intent  Intent android.content.Intent  action android.content.Intent  apply android.content.Intent  data android.content.Intent  flags android.content.Intent  	getACTION android.content.Intent  getAPPLY android.content.Intent  	getAction android.content.Intent  getApply android.content.Intent  getDATA android.content.Intent  getData android.content.Intent  getFLAGS android.content.Intent  getFlags android.content.Intent  	setAction android.content.Intent  setData android.content.Intent  setFlags android.content.Intent  edit !android.content.SharedPreferences  
getBoolean !android.content.SharedPreferences  getInt !android.content.SharedPreferences  getLong !android.content.SharedPreferences  	getString !android.content.SharedPreferences  getStringSet !android.content.SharedPreferences  apply (android.content.SharedPreferences.Editor  
putBoolean (android.content.SharedPreferences.Editor  putInt (android.content.SharedPreferences.Editor  putLong (android.content.SharedPreferences.Editor  	putString (android.content.SharedPreferences.Editor  putStringSet (android.content.SharedPreferences.Editor  PackageManager android.content.pm  PERMISSION_GRANTED !android.content.pm.PackageManager  Cursor android.database  getColumnIndexOrThrow android.database.Cursor  getLong android.database.Cursor  	getString android.database.Cursor  getUSE android.database.Cursor  getUse android.database.Cursor  
moveToNext android.database.Cursor  use android.database.Cursor  Bitmap android.graphics  MediaMetadataRetriever 
android.media  METADATA_KEY_ALBUM $android.media.MediaMetadataRetriever  METADATA_KEY_ARTIST $android.media.MediaMetadataRetriever  METADATA_KEY_DURATION $android.media.MediaMetadataRetriever  METADATA_KEY_TITLE $android.media.MediaMetadataRetriever  extractMetadata $android.media.MediaMetadataRetriever  getUSE $android.media.MediaMetadataRetriever  getUse $android.media.MediaMetadataRetriever  
setDataSource $android.media.MediaMetadataRetriever  use $android.media.MediaMetadataRetriever  Uri android.net  getLET android.net.Uri  getLet android.net.Uri  hashCode android.net.Uri  let android.net.Uri  parse android.net.Uri  toString android.net.Uri  Binder 
android.os  Build 
android.os  Bundle 
android.os  IBinder 
android.os  
Parcelable 
android.os  MusicService android.os.Binder  
getService android.os.Binder  VERSION android.os.Build  
VERSION_CODES android.os.Build  SDK_INT android.os.Build.VERSION  O android.os.Build.VERSION_CODES  TIRAMISU android.os.Build.VERSION_CODES  DocumentsContract android.provider  
MediaStore android.provider  getTreeDocumentId "android.provider.DocumentsContract  Audio android.provider.MediaStore  Media !android.provider.MediaStore.Audio  ALBUM 'android.provider.MediaStore.Audio.Media  ARTIST 'android.provider.MediaStore.Audio.Media  DATA 'android.provider.MediaStore.Audio.Media  
DATE_MODIFIED 'android.provider.MediaStore.Audio.Media  DURATION 'android.provider.MediaStore.Audio.Media  EXTERNAL_CONTENT_URI 'android.provider.MediaStore.Audio.Media  IS_MUSIC 'android.provider.MediaStore.Audio.Media  SIZE 'android.provider.MediaStore.Audio.Media  TITLE 'android.provider.MediaStore.Audio.Media  _ID 'android.provider.MediaStore.Audio.Media  MediaBrowserCompat android.support.v4.media  MediaMetadataCompat android.support.v4.media  	MediaItem +android.support.v4.media.MediaBrowserCompat  MediaSessionCompat  android.support.v4.media.session  PlaybackStateCompat  android.support.v4.media.session  Callback 3android.support.v4.media.session.MediaSessionCompat  Token 3android.support.v4.media.session.MediaSessionCompat  getISActive 3android.support.v4.media.session.MediaSessionCompat  getIsActive 3android.support.v4.media.session.MediaSessionCompat  getSESSIONToken 3android.support.v4.media.session.MediaSessionCompat  getSessionToken 3android.support.v4.media.session.MediaSessionCompat  isActive 3android.support.v4.media.session.MediaSessionCompat  release 3android.support.v4.media.session.MediaSessionCompat  sessionToken 3android.support.v4.media.session.MediaSessionCompat  	setActive 3android.support.v4.media.session.MediaSessionCompat  setCallback 3android.support.v4.media.session.MediaSessionCompat  setPlaybackState 3android.support.v4.media.session.MediaSessionCompat  setSessionToken 3android.support.v4.media.session.MediaSessionCompat  Long <android.support.v4.media.session.MediaSessionCompat.Callback  pause <android.support.v4.media.session.MediaSessionCompat.Callback  play <android.support.v4.media.session.MediaSessionCompat.Callback  seekTo <android.support.v4.media.session.MediaSessionCompat.Callback  
skipToNext <android.support.v4.media.session.MediaSessionCompat.Callback  skipToPrevious <android.support.v4.media.session.MediaSessionCompat.Callback  stop <android.support.v4.media.session.MediaSessionCompat.Callback  ACTION_PAUSE 4android.support.v4.media.session.PlaybackStateCompat  ACTION_PLAY 4android.support.v4.media.session.PlaybackStateCompat  ACTION_SEEK_TO 4android.support.v4.media.session.PlaybackStateCompat  ACTION_SKIP_TO_NEXT 4android.support.v4.media.session.PlaybackStateCompat  ACTION_SKIP_TO_PREVIOUS 4android.support.v4.media.session.PlaybackStateCompat  Builder 4android.support.v4.media.session.PlaybackStateCompat  STATE_BUFFERING 4android.support.v4.media.session.PlaybackStateCompat  STATE_PAUSED 4android.support.v4.media.session.PlaybackStateCompat  
STATE_PLAYING 4android.support.v4.media.session.PlaybackStateCompat  build <android.support.v4.media.session.PlaybackStateCompat.Builder  
setActions <android.support.v4.media.session.PlaybackStateCompat.Builder  setState <android.support.v4.media.session.PlaybackStateCompat.Builder  Log android.util  e android.util.Log  i android.util.Log  w android.util.Log  LayoutInflater android.view  Menu android.view  MenuInflater android.view  MenuItem android.view  View android.view  	ViewGroup android.view  Activity  android.view.ContextThemeWrapper  !ActivityDirectorySelectionBinding  android.view.ContextThemeWrapper  ActivityMainBinding  android.view.ContextThemeWrapper  ActivityResultContracts  android.view.ContextThemeWrapper  Array  android.view.ContextThemeWrapper  Boolean  android.view.ContextThemeWrapper  Bundle  android.view.ContextThemeWrapper  
ComponentName  android.view.ContextThemeWrapper  Context  android.view.ContextThemeWrapper  DirectoryAdapter  android.view.ContextThemeWrapper  DirectorySelectionActivity  android.view.ContextThemeWrapper  DocumentsContract  android.view.ContextThemeWrapper  ErrorHandler  android.view.ContextThemeWrapper  	Exception  android.view.ContextThemeWrapper  FileScanner  android.view.ContextThemeWrapper  IBinder  android.view.ContextThemeWrapper  Int  android.view.ContextThemeWrapper  IntArray  android.view.ContextThemeWrapper  Intent  android.view.ContextThemeWrapper  LinearLayoutManager  android.view.ContextThemeWrapper  Long  android.view.ContextThemeWrapper  Menu  android.view.ContextThemeWrapper  MenuItem  android.view.ContextThemeWrapper  MusicService  android.view.ContextThemeWrapper  PermissionHelper  android.view.ContextThemeWrapper  PreferenceManager  android.view.ContextThemeWrapper  R  android.view.ContextThemeWrapper  	RESULT_OK  android.view.ContextThemeWrapper  ServiceConnection  android.view.ContextThemeWrapper  Song  android.view.ContextThemeWrapper  SongAdapter  android.view.ContextThemeWrapper  String  android.view.ContextThemeWrapper  Toast  android.view.ContextThemeWrapper  Uri  android.view.ContextThemeWrapper  View  android.view.ContextThemeWrapper  apply  android.view.ContextThemeWrapper  bindService  android.view.ContextThemeWrapper  binding  android.view.ContextThemeWrapper  checkPermissionsAndLoadMusic  android.view.ContextThemeWrapper  directoryAdapter  android.view.ContextThemeWrapper  fileScanner  android.view.ContextThemeWrapper  finish  android.view.ContextThemeWrapper  getDirectoryDisplayName  android.view.ContextThemeWrapper  handleDirectorySelection  android.view.ContextThemeWrapper  ifEmpty  android.view.ContextThemeWrapper  invoke  android.view.ContextThemeWrapper  
isNotEmpty  android.view.ContextThemeWrapper  isServiceBound  android.view.ContextThemeWrapper  java  android.view.ContextThemeWrapper  launch  android.view.ContextThemeWrapper  let  android.view.ContextThemeWrapper  lifecycleScope  android.view.ContextThemeWrapper  listOf  android.view.ContextThemeWrapper  	loadMusic  android.view.ContextThemeWrapper  loadSelectedDirectories  android.view.ContextThemeWrapper  map  android.view.ContextThemeWrapper  musicService  android.view.ContextThemeWrapper  
mutableListOf  android.view.ContextThemeWrapper  
onBackPressed  android.view.ContextThemeWrapper  onCreate  android.view.ContextThemeWrapper  	onDestroy  android.view.ContextThemeWrapper  onOptionsItemSelected  android.view.ContextThemeWrapper  onRequestPermissionsResult  android.view.ContextThemeWrapper  openDirectoryPicker  android.view.ContextThemeWrapper  playSong  android.view.ContextThemeWrapper  
plusAssign  android.view.ContextThemeWrapper  preferenceManager  android.view.ContextThemeWrapper  registerForActivityResult  android.view.ContextThemeWrapper  removeDirectory  android.view.ContextThemeWrapper  requestPermissions  android.view.ContextThemeWrapper  
runOnUiThread  android.view.ContextThemeWrapper  scanAllDirectories  android.view.ContextThemeWrapper  setContentView  android.view.ContextThemeWrapper  	setResult  android.view.ContextThemeWrapper  setSupportActionBar  android.view.ContextThemeWrapper  setupClickListeners  android.view.ContextThemeWrapper  setupPlaybackControls  android.view.ContextThemeWrapper  setupRecyclerView  android.view.ContextThemeWrapper  setupToolbar  android.view.ContextThemeWrapper  showEmptyState  android.view.ContextThemeWrapper  showPermissionDeniedMessage  android.view.ContextThemeWrapper  songAdapter  android.view.ContextThemeWrapper  songs  android.view.ContextThemeWrapper  sortedBy  android.view.ContextThemeWrapper  split  android.view.ContextThemeWrapper  startMusicService  android.view.ContextThemeWrapper  startService  android.view.ContextThemeWrapper  toMutableSet  android.view.ContextThemeWrapper  togglePlayback  android.view.ContextThemeWrapper  
unbindService  android.view.ContextThemeWrapper  updateCurrentSong  android.view.ContextThemeWrapper  updatePlaybackUI  android.view.ContextThemeWrapper  from android.view.LayoutInflater  inflate android.view.MenuInflater  	getITEMId android.view.MenuItem  	getItemId android.view.MenuItem  itemId android.view.MenuItem  	setItemId android.view.MenuItem  GONE android.view.View  VISIBLE android.view.View  apply android.view.View  setBackgroundColor android.view.View  setImageResource android.view.View  setOnClickListener android.view.View  setTextColor android.view.View  <SAM-CONSTRUCTOR> !android.view.View.OnClickListener  apply android.view.ViewGroup  context android.view.ViewGroup  
getCONTEXT android.view.ViewGroup  
getContext android.view.ViewGroup  setBackgroundColor android.view.ViewGroup  
setContext android.view.ViewGroup  setOnClickListener android.view.ViewGroup  LinearLayout android.widget  RemoteViews android.widget  TextView android.widget  Toast android.widget  setOnClickListener android.widget.Button  setImageResource android.widget.ImageButton  setOnClickListener android.widget.ImageButton  setImageResource android.widget.ImageView  setOnClickListener android.widget.ImageView  context android.widget.LinearLayout  
getCONTEXT android.widget.LinearLayout  
getContext android.widget.LinearLayout  
getVISIBILITY android.widget.LinearLayout  
getVisibility android.widget.LinearLayout  setBackgroundColor android.widget.LinearLayout  
setContext android.widget.LinearLayout  setOnClickListener android.widget.LinearLayout  
setVisibility android.widget.LinearLayout  
visibility android.widget.LinearLayout  
getVISIBILITY android.widget.ProgressBar  
getVisibility android.widget.ProgressBar  
setVisibility android.widget.ProgressBar  
visibility android.widget.ProgressBar  setImageViewResource android.widget.RemoteViews  setOnClickPendingIntent android.widget.RemoteViews  setTextViewText android.widget.RemoteViews  View android.widget.TextView  apply android.widget.TextView  getAPPLY android.widget.TextView  getApply android.widget.TextView  getPREFERENCEManager android.widget.TextView  getPreferenceManager android.widget.TextView  getTEXT android.widget.TextView  getText android.widget.TextView  
getVISIBILITY android.widget.TextView  
getVisibility android.widget.TextView  preferenceManager android.widget.TextView  setOnClickListener android.widget.TextView  setText android.widget.TextView  setTextColor android.widget.TextView  
setVisibility android.widget.TextView  text android.widget.TextView  
visibility android.widget.TextView  LENGTH_LONG android.widget.Toast  LENGTH_SHORT android.widget.Toast  makeText android.widget.Toast  show android.widget.Toast  Activity #androidx.activity.ComponentActivity  !ActivityDirectorySelectionBinding #androidx.activity.ComponentActivity  ActivityMainBinding #androidx.activity.ComponentActivity  ActivityResultContracts #androidx.activity.ComponentActivity  Array #androidx.activity.ComponentActivity  Boolean #androidx.activity.ComponentActivity  Bundle #androidx.activity.ComponentActivity  
ComponentName #androidx.activity.ComponentActivity  Context #androidx.activity.ComponentActivity  DirectoryAdapter #androidx.activity.ComponentActivity  DirectorySelectionActivity #androidx.activity.ComponentActivity  DocumentsContract #androidx.activity.ComponentActivity  ErrorHandler #androidx.activity.ComponentActivity  	Exception #androidx.activity.ComponentActivity  FileScanner #androidx.activity.ComponentActivity  IBinder #androidx.activity.ComponentActivity  Int #androidx.activity.ComponentActivity  IntArray #androidx.activity.ComponentActivity  Intent #androidx.activity.ComponentActivity  LinearLayoutManager #androidx.activity.ComponentActivity  Long #androidx.activity.ComponentActivity  Menu #androidx.activity.ComponentActivity  MenuItem #androidx.activity.ComponentActivity  MusicService #androidx.activity.ComponentActivity  PermissionHelper #androidx.activity.ComponentActivity  PreferenceManager #androidx.activity.ComponentActivity  R #androidx.activity.ComponentActivity  	RESULT_OK #androidx.activity.ComponentActivity  ServiceConnection #androidx.activity.ComponentActivity  Song #androidx.activity.ComponentActivity  SongAdapter #androidx.activity.ComponentActivity  String #androidx.activity.ComponentActivity  Toast #androidx.activity.ComponentActivity  Uri #androidx.activity.ComponentActivity  View #androidx.activity.ComponentActivity  apply #androidx.activity.ComponentActivity  bindService #androidx.activity.ComponentActivity  binding #androidx.activity.ComponentActivity  checkPermissionsAndLoadMusic #androidx.activity.ComponentActivity  directoryAdapter #androidx.activity.ComponentActivity  fileScanner #androidx.activity.ComponentActivity  finish #androidx.activity.ComponentActivity  getDirectoryDisplayName #androidx.activity.ComponentActivity  handleDirectorySelection #androidx.activity.ComponentActivity  ifEmpty #androidx.activity.ComponentActivity  invoke #androidx.activity.ComponentActivity  
isNotEmpty #androidx.activity.ComponentActivity  isServiceBound #androidx.activity.ComponentActivity  java #androidx.activity.ComponentActivity  launch #androidx.activity.ComponentActivity  let #androidx.activity.ComponentActivity  lifecycleScope #androidx.activity.ComponentActivity  listOf #androidx.activity.ComponentActivity  	loadMusic #androidx.activity.ComponentActivity  loadSelectedDirectories #androidx.activity.ComponentActivity  map #androidx.activity.ComponentActivity  musicService #androidx.activity.ComponentActivity  
mutableListOf #androidx.activity.ComponentActivity  
onBackPressed #androidx.activity.ComponentActivity  onCreate #androidx.activity.ComponentActivity  	onDestroy #androidx.activity.ComponentActivity  onOptionsItemSelected #androidx.activity.ComponentActivity  onRequestPermissionsResult #androidx.activity.ComponentActivity  openDirectoryPicker #androidx.activity.ComponentActivity  playSong #androidx.activity.ComponentActivity  
plusAssign #androidx.activity.ComponentActivity  preferenceManager #androidx.activity.ComponentActivity  registerForActivityResult #androidx.activity.ComponentActivity  removeDirectory #androidx.activity.ComponentActivity  requestPermissions #androidx.activity.ComponentActivity  
runOnUiThread #androidx.activity.ComponentActivity  scanAllDirectories #androidx.activity.ComponentActivity  setContentView #androidx.activity.ComponentActivity  	setResult #androidx.activity.ComponentActivity  setSupportActionBar #androidx.activity.ComponentActivity  setupClickListeners #androidx.activity.ComponentActivity  setupPlaybackControls #androidx.activity.ComponentActivity  setupRecyclerView #androidx.activity.ComponentActivity  setupToolbar #androidx.activity.ComponentActivity  showEmptyState #androidx.activity.ComponentActivity  showPermissionDeniedMessage #androidx.activity.ComponentActivity  songAdapter #androidx.activity.ComponentActivity  songs #androidx.activity.ComponentActivity  sortedBy #androidx.activity.ComponentActivity  split #androidx.activity.ComponentActivity  startMusicService #androidx.activity.ComponentActivity  startService #androidx.activity.ComponentActivity  toMutableSet #androidx.activity.ComponentActivity  togglePlayback #androidx.activity.ComponentActivity  
unbindService #androidx.activity.ComponentActivity  updateCurrentSong #androidx.activity.ComponentActivity  updatePlaybackUI #androidx.activity.ComponentActivity  ActivityResult androidx.activity.result  ActivityResultLauncher androidx.activity.result  data 'androidx.activity.result.ActivityResult  getDATA 'androidx.activity.result.ActivityResult  getData 'androidx.activity.result.ActivityResult  
getRESULTCode 'androidx.activity.result.ActivityResult  
getResultCode 'androidx.activity.result.ActivityResult  
resultCode 'androidx.activity.result.ActivityResult  setData 'androidx.activity.result.ActivityResult  
setResultCode 'androidx.activity.result.ActivityResult  <SAM-CONSTRUCTOR> /androidx.activity.result.ActivityResultCallback  launch /androidx.activity.result.ActivityResultLauncher  ActivityResultContracts !androidx.activity.result.contract  StartActivityForResult 9androidx.activity.result.contract.ActivityResultContracts  invoke Zandroidx.activity.result.contract.ActivityResultContracts.StartActivityForResult.Companion  	ActionBar androidx.appcompat.app  AppCompatActivity androidx.appcompat.app  apply  androidx.appcompat.app.ActionBar  getAPPLY  androidx.appcompat.app.ActionBar  getApply  androidx.appcompat.app.ActionBar  getTITLE  androidx.appcompat.app.ActionBar  getTitle  androidx.appcompat.app.ActionBar  setDisplayHomeAsUpEnabled  androidx.appcompat.app.ActionBar  setTitle  androidx.appcompat.app.ActionBar  title  androidx.appcompat.app.ActionBar  Activity (androidx.appcompat.app.AppCompatActivity  !ActivityDirectorySelectionBinding (androidx.appcompat.app.AppCompatActivity  ActivityMainBinding (androidx.appcompat.app.AppCompatActivity  ActivityResultContracts (androidx.appcompat.app.AppCompatActivity  Array (androidx.appcompat.app.AppCompatActivity  Boolean (androidx.appcompat.app.AppCompatActivity  Bundle (androidx.appcompat.app.AppCompatActivity  
ComponentName (androidx.appcompat.app.AppCompatActivity  Context (androidx.appcompat.app.AppCompatActivity  DirectoryAdapter (androidx.appcompat.app.AppCompatActivity  DirectorySelectionActivity (androidx.appcompat.app.AppCompatActivity  DocumentsContract (androidx.appcompat.app.AppCompatActivity  ErrorHandler (androidx.appcompat.app.AppCompatActivity  	Exception (androidx.appcompat.app.AppCompatActivity  FileScanner (androidx.appcompat.app.AppCompatActivity  IBinder (androidx.appcompat.app.AppCompatActivity  Int (androidx.appcompat.app.AppCompatActivity  IntArray (androidx.appcompat.app.AppCompatActivity  Intent (androidx.appcompat.app.AppCompatActivity  LinearLayoutManager (androidx.appcompat.app.AppCompatActivity  Long (androidx.appcompat.app.AppCompatActivity  Menu (androidx.appcompat.app.AppCompatActivity  MenuItem (androidx.appcompat.app.AppCompatActivity  MusicService (androidx.appcompat.app.AppCompatActivity  PermissionHelper (androidx.appcompat.app.AppCompatActivity  PreferenceManager (androidx.appcompat.app.AppCompatActivity  R (androidx.appcompat.app.AppCompatActivity  	RESULT_OK (androidx.appcompat.app.AppCompatActivity  ServiceConnection (androidx.appcompat.app.AppCompatActivity  Song (androidx.appcompat.app.AppCompatActivity  SongAdapter (androidx.appcompat.app.AppCompatActivity  String (androidx.appcompat.app.AppCompatActivity  Toast (androidx.appcompat.app.AppCompatActivity  Uri (androidx.appcompat.app.AppCompatActivity  View (androidx.appcompat.app.AppCompatActivity  apply (androidx.appcompat.app.AppCompatActivity  bindService (androidx.appcompat.app.AppCompatActivity  binding (androidx.appcompat.app.AppCompatActivity  checkPermissionsAndLoadMusic (androidx.appcompat.app.AppCompatActivity  directoryAdapter (androidx.appcompat.app.AppCompatActivity  fileScanner (androidx.appcompat.app.AppCompatActivity  finish (androidx.appcompat.app.AppCompatActivity  getDirectoryDisplayName (androidx.appcompat.app.AppCompatActivity  handleDirectorySelection (androidx.appcompat.app.AppCompatActivity  ifEmpty (androidx.appcompat.app.AppCompatActivity  invoke (androidx.appcompat.app.AppCompatActivity  
isNotEmpty (androidx.appcompat.app.AppCompatActivity  isServiceBound (androidx.appcompat.app.AppCompatActivity  java (androidx.appcompat.app.AppCompatActivity  launch (androidx.appcompat.app.AppCompatActivity  let (androidx.appcompat.app.AppCompatActivity  lifecycleScope (androidx.appcompat.app.AppCompatActivity  listOf (androidx.appcompat.app.AppCompatActivity  	loadMusic (androidx.appcompat.app.AppCompatActivity  loadSelectedDirectories (androidx.appcompat.app.AppCompatActivity  map (androidx.appcompat.app.AppCompatActivity  musicService (androidx.appcompat.app.AppCompatActivity  
mutableListOf (androidx.appcompat.app.AppCompatActivity  
onBackPressed (androidx.appcompat.app.AppCompatActivity  onCreate (androidx.appcompat.app.AppCompatActivity  	onDestroy (androidx.appcompat.app.AppCompatActivity  onOptionsItemSelected (androidx.appcompat.app.AppCompatActivity  onRequestPermissionsResult (androidx.appcompat.app.AppCompatActivity  openDirectoryPicker (androidx.appcompat.app.AppCompatActivity  playSong (androidx.appcompat.app.AppCompatActivity  
plusAssign (androidx.appcompat.app.AppCompatActivity  preferenceManager (androidx.appcompat.app.AppCompatActivity  registerForActivityResult (androidx.appcompat.app.AppCompatActivity  removeDirectory (androidx.appcompat.app.AppCompatActivity  requestPermissions (androidx.appcompat.app.AppCompatActivity  
runOnUiThread (androidx.appcompat.app.AppCompatActivity  scanAllDirectories (androidx.appcompat.app.AppCompatActivity  setContentView (androidx.appcompat.app.AppCompatActivity  	setResult (androidx.appcompat.app.AppCompatActivity  setSupportActionBar (androidx.appcompat.app.AppCompatActivity  setupClickListeners (androidx.appcompat.app.AppCompatActivity  setupPlaybackControls (androidx.appcompat.app.AppCompatActivity  setupRecyclerView (androidx.appcompat.app.AppCompatActivity  setupToolbar (androidx.appcompat.app.AppCompatActivity  showEmptyState (androidx.appcompat.app.AppCompatActivity  showPermissionDeniedMessage (androidx.appcompat.app.AppCompatActivity  songAdapter (androidx.appcompat.app.AppCompatActivity  songs (androidx.appcompat.app.AppCompatActivity  sortedBy (androidx.appcompat.app.AppCompatActivity  split (androidx.appcompat.app.AppCompatActivity  startMusicService (androidx.appcompat.app.AppCompatActivity  startService (androidx.appcompat.app.AppCompatActivity  toMutableSet (androidx.appcompat.app.AppCompatActivity  togglePlayback (androidx.appcompat.app.AppCompatActivity  
unbindService (androidx.appcompat.app.AppCompatActivity  updateCurrentSong (androidx.appcompat.app.AppCompatActivity  updatePlaybackUI (androidx.appcompat.app.AppCompatActivity  Toolbar androidx.appcompat.widget  setOnClickListener )androidx.appcompat.widget.AppCompatButton  ActivityCompat androidx.core.app  NotificationCompat androidx.core.app  requestPermissions  androidx.core.app.ActivityCompat  $shouldShowRequestPermissionRationale  androidx.core.app.ActivityCompat  Activity #androidx.core.app.ComponentActivity  !ActivityDirectorySelectionBinding #androidx.core.app.ComponentActivity  ActivityMainBinding #androidx.core.app.ComponentActivity  ActivityResultContracts #androidx.core.app.ComponentActivity  Array #androidx.core.app.ComponentActivity  Boolean #androidx.core.app.ComponentActivity  Bundle #androidx.core.app.ComponentActivity  
ComponentName #androidx.core.app.ComponentActivity  Context #androidx.core.app.ComponentActivity  DirectoryAdapter #androidx.core.app.ComponentActivity  DirectorySelectionActivity #androidx.core.app.ComponentActivity  DocumentsContract #androidx.core.app.ComponentActivity  ErrorHandler #androidx.core.app.ComponentActivity  	Exception #androidx.core.app.ComponentActivity  FileScanner #androidx.core.app.ComponentActivity  IBinder #androidx.core.app.ComponentActivity  Int #androidx.core.app.ComponentActivity  IntArray #androidx.core.app.ComponentActivity  Intent #androidx.core.app.ComponentActivity  LinearLayoutManager #androidx.core.app.ComponentActivity  Long #androidx.core.app.ComponentActivity  Menu #androidx.core.app.ComponentActivity  MenuItem #androidx.core.app.ComponentActivity  MusicService #androidx.core.app.ComponentActivity  PermissionHelper #androidx.core.app.ComponentActivity  PreferenceManager #androidx.core.app.ComponentActivity  R #androidx.core.app.ComponentActivity  	RESULT_OK #androidx.core.app.ComponentActivity  ServiceConnection #androidx.core.app.ComponentActivity  Song #androidx.core.app.ComponentActivity  SongAdapter #androidx.core.app.ComponentActivity  String #androidx.core.app.ComponentActivity  Toast #androidx.core.app.ComponentActivity  Uri #androidx.core.app.ComponentActivity  View #androidx.core.app.ComponentActivity  apply #androidx.core.app.ComponentActivity  bindService #androidx.core.app.ComponentActivity  binding #androidx.core.app.ComponentActivity  checkPermissionsAndLoadMusic #androidx.core.app.ComponentActivity  directoryAdapter #androidx.core.app.ComponentActivity  fileScanner #androidx.core.app.ComponentActivity  finish #androidx.core.app.ComponentActivity  getDirectoryDisplayName #androidx.core.app.ComponentActivity  handleDirectorySelection #androidx.core.app.ComponentActivity  ifEmpty #androidx.core.app.ComponentActivity  invoke #androidx.core.app.ComponentActivity  
isNotEmpty #androidx.core.app.ComponentActivity  isServiceBound #androidx.core.app.ComponentActivity  java #androidx.core.app.ComponentActivity  launch #androidx.core.app.ComponentActivity  let #androidx.core.app.ComponentActivity  lifecycleScope #androidx.core.app.ComponentActivity  listOf #androidx.core.app.ComponentActivity  	loadMusic #androidx.core.app.ComponentActivity  loadSelectedDirectories #androidx.core.app.ComponentActivity  map #androidx.core.app.ComponentActivity  musicService #androidx.core.app.ComponentActivity  
mutableListOf #androidx.core.app.ComponentActivity  
onBackPressed #androidx.core.app.ComponentActivity  onCreate #androidx.core.app.ComponentActivity  	onDestroy #androidx.core.app.ComponentActivity  onOptionsItemSelected #androidx.core.app.ComponentActivity  onRequestPermissionsResult #androidx.core.app.ComponentActivity  openDirectoryPicker #androidx.core.app.ComponentActivity  playSong #androidx.core.app.ComponentActivity  
plusAssign #androidx.core.app.ComponentActivity  preferenceManager #androidx.core.app.ComponentActivity  registerForActivityResult #androidx.core.app.ComponentActivity  removeDirectory #androidx.core.app.ComponentActivity  requestPermissions #androidx.core.app.ComponentActivity  
runOnUiThread #androidx.core.app.ComponentActivity  scanAllDirectories #androidx.core.app.ComponentActivity  setContentView #androidx.core.app.ComponentActivity  	setResult #androidx.core.app.ComponentActivity  setSupportActionBar #androidx.core.app.ComponentActivity  setupClickListeners #androidx.core.app.ComponentActivity  setupPlaybackControls #androidx.core.app.ComponentActivity  setupRecyclerView #androidx.core.app.ComponentActivity  setupToolbar #androidx.core.app.ComponentActivity  showEmptyState #androidx.core.app.ComponentActivity  showPermissionDeniedMessage #androidx.core.app.ComponentActivity  songAdapter #androidx.core.app.ComponentActivity  songs #androidx.core.app.ComponentActivity  sortedBy #androidx.core.app.ComponentActivity  split #androidx.core.app.ComponentActivity  startMusicService #androidx.core.app.ComponentActivity  startService #androidx.core.app.ComponentActivity  toMutableSet #androidx.core.app.ComponentActivity  togglePlayback #androidx.core.app.ComponentActivity  
unbindService #androidx.core.app.ComponentActivity  updateCurrentSong #androidx.core.app.ComponentActivity  updatePlaybackUI #androidx.core.app.ComponentActivity  
ContextCompat androidx.core.content  checkSelfPermission #androidx.core.content.ContextCompat  getColor #androidx.core.content.ContextCompat  DocumentFile androidx.documentfile.provider  equals +androidx.documentfile.provider.DocumentFile  exists +androidx.documentfile.provider.DocumentFile  fromTreeUri +androidx.documentfile.provider.DocumentFile  getISDirectory +androidx.documentfile.provider.DocumentFile  	getISFile +androidx.documentfile.provider.DocumentFile  getIsDirectory +androidx.documentfile.provider.DocumentFile  	getIsFile +androidx.documentfile.provider.DocumentFile  getNAME +androidx.documentfile.provider.DocumentFile  getName +androidx.documentfile.provider.DocumentFile  getURI +androidx.documentfile.provider.DocumentFile  getUri +androidx.documentfile.provider.DocumentFile  isDirectory +androidx.documentfile.provider.DocumentFile  isFile +androidx.documentfile.provider.DocumentFile  lastModified +androidx.documentfile.provider.DocumentFile  length +androidx.documentfile.provider.DocumentFile  	listFiles +androidx.documentfile.provider.DocumentFile  name +androidx.documentfile.provider.DocumentFile  setDirectory +androidx.documentfile.provider.DocumentFile  setFile +androidx.documentfile.provider.DocumentFile  setName +androidx.documentfile.provider.DocumentFile  setUri +androidx.documentfile.provider.DocumentFile  uri +androidx.documentfile.provider.DocumentFile  Activity &androidx.fragment.app.FragmentActivity  !ActivityDirectorySelectionBinding &androidx.fragment.app.FragmentActivity  ActivityMainBinding &androidx.fragment.app.FragmentActivity  ActivityResultContracts &androidx.fragment.app.FragmentActivity  Array &androidx.fragment.app.FragmentActivity  Boolean &androidx.fragment.app.FragmentActivity  Bundle &androidx.fragment.app.FragmentActivity  
ComponentName &androidx.fragment.app.FragmentActivity  Context &androidx.fragment.app.FragmentActivity  DirectoryAdapter &androidx.fragment.app.FragmentActivity  DirectorySelectionActivity &androidx.fragment.app.FragmentActivity  DocumentsContract &androidx.fragment.app.FragmentActivity  ErrorHandler &androidx.fragment.app.FragmentActivity  	Exception &androidx.fragment.app.FragmentActivity  FileScanner &androidx.fragment.app.FragmentActivity  IBinder &androidx.fragment.app.FragmentActivity  Int &androidx.fragment.app.FragmentActivity  IntArray &androidx.fragment.app.FragmentActivity  Intent &androidx.fragment.app.FragmentActivity  LinearLayoutManager &androidx.fragment.app.FragmentActivity  Long &androidx.fragment.app.FragmentActivity  Menu &androidx.fragment.app.FragmentActivity  MenuItem &androidx.fragment.app.FragmentActivity  MusicService &androidx.fragment.app.FragmentActivity  PermissionHelper &androidx.fragment.app.FragmentActivity  PreferenceManager &androidx.fragment.app.FragmentActivity  R &androidx.fragment.app.FragmentActivity  	RESULT_OK &androidx.fragment.app.FragmentActivity  ServiceConnection &androidx.fragment.app.FragmentActivity  Song &androidx.fragment.app.FragmentActivity  SongAdapter &androidx.fragment.app.FragmentActivity  String &androidx.fragment.app.FragmentActivity  Toast &androidx.fragment.app.FragmentActivity  Uri &androidx.fragment.app.FragmentActivity  View &androidx.fragment.app.FragmentActivity  apply &androidx.fragment.app.FragmentActivity  bindService &androidx.fragment.app.FragmentActivity  binding &androidx.fragment.app.FragmentActivity  checkPermissionsAndLoadMusic &androidx.fragment.app.FragmentActivity  directoryAdapter &androidx.fragment.app.FragmentActivity  fileScanner &androidx.fragment.app.FragmentActivity  finish &androidx.fragment.app.FragmentActivity  getDirectoryDisplayName &androidx.fragment.app.FragmentActivity  handleDirectorySelection &androidx.fragment.app.FragmentActivity  ifEmpty &androidx.fragment.app.FragmentActivity  invoke &androidx.fragment.app.FragmentActivity  
isNotEmpty &androidx.fragment.app.FragmentActivity  isServiceBound &androidx.fragment.app.FragmentActivity  java &androidx.fragment.app.FragmentActivity  launch &androidx.fragment.app.FragmentActivity  let &androidx.fragment.app.FragmentActivity  lifecycleScope &androidx.fragment.app.FragmentActivity  listOf &androidx.fragment.app.FragmentActivity  	loadMusic &androidx.fragment.app.FragmentActivity  loadSelectedDirectories &androidx.fragment.app.FragmentActivity  map &androidx.fragment.app.FragmentActivity  musicService &androidx.fragment.app.FragmentActivity  
mutableListOf &androidx.fragment.app.FragmentActivity  
onBackPressed &androidx.fragment.app.FragmentActivity  onCreate &androidx.fragment.app.FragmentActivity  	onDestroy &androidx.fragment.app.FragmentActivity  onOptionsItemSelected &androidx.fragment.app.FragmentActivity  onRequestPermissionsResult &androidx.fragment.app.FragmentActivity  openDirectoryPicker &androidx.fragment.app.FragmentActivity  playSong &androidx.fragment.app.FragmentActivity  
plusAssign &androidx.fragment.app.FragmentActivity  preferenceManager &androidx.fragment.app.FragmentActivity  registerForActivityResult &androidx.fragment.app.FragmentActivity  removeDirectory &androidx.fragment.app.FragmentActivity  requestPermissions &androidx.fragment.app.FragmentActivity  
runOnUiThread &androidx.fragment.app.FragmentActivity  scanAllDirectories &androidx.fragment.app.FragmentActivity  setContentView &androidx.fragment.app.FragmentActivity  	setResult &androidx.fragment.app.FragmentActivity  setSupportActionBar &androidx.fragment.app.FragmentActivity  setupClickListeners &androidx.fragment.app.FragmentActivity  setupPlaybackControls &androidx.fragment.app.FragmentActivity  setupRecyclerView &androidx.fragment.app.FragmentActivity  setupToolbar &androidx.fragment.app.FragmentActivity  showEmptyState &androidx.fragment.app.FragmentActivity  showPermissionDeniedMessage &androidx.fragment.app.FragmentActivity  songAdapter &androidx.fragment.app.FragmentActivity  songs &androidx.fragment.app.FragmentActivity  sortedBy &androidx.fragment.app.FragmentActivity  split &androidx.fragment.app.FragmentActivity  startMusicService &androidx.fragment.app.FragmentActivity  startService &androidx.fragment.app.FragmentActivity  toMutableSet &androidx.fragment.app.FragmentActivity  togglePlayback &androidx.fragment.app.FragmentActivity  
unbindService &androidx.fragment.app.FragmentActivity  updateCurrentSong &androidx.fragment.app.FragmentActivity  updatePlaybackUI &androidx.fragment.app.FragmentActivity  LifecycleCoroutineScope androidx.lifecycle  lifecycleScope androidx.lifecycle  	getLAUNCH *androidx.lifecycle.LifecycleCoroutineScope  	getLaunch *androidx.lifecycle.LifecycleCoroutineScope  launch *androidx.lifecycle.LifecycleCoroutineScope  MediaBrowserServiceCompat androidx.media  AudioAttributes (androidx.media.MediaBrowserServiceCompat  Binder (androidx.media.MediaBrowserServiceCompat  Boolean (androidx.media.MediaBrowserServiceCompat  BrowserRoot (androidx.media.MediaBrowserServiceCompat  Build (androidx.media.MediaBrowserServiceCompat  Bundle (androidx.media.MediaBrowserServiceCompat  C (androidx.media.MediaBrowserServiceCompat  CharSequence (androidx.media.MediaBrowserServiceCompat  ErrorHandler (androidx.media.MediaBrowserServiceCompat  	ExoPlayer (androidx.media.MediaBrowserServiceCompat  IBinder (androidx.media.MediaBrowserServiceCompat  Int (androidx.media.MediaBrowserServiceCompat  Intent (androidx.media.MediaBrowserServiceCompat  List (androidx.media.MediaBrowserServiceCompat  Long (androidx.media.MediaBrowserServiceCompat  MainActivity (androidx.media.MediaBrowserServiceCompat  MediaBrowserCompat (androidx.media.MediaBrowserServiceCompat  	MediaItem (androidx.media.MediaBrowserServiceCompat  MediaSessionCompat (androidx.media.MediaBrowserServiceCompat  MusicService (androidx.media.MediaBrowserServiceCompat  MusicWidgetProvider (androidx.media.MediaBrowserServiceCompat  MutableList (androidx.media.MediaBrowserServiceCompat  NOTIFICATION_CHANNEL_ID (androidx.media.MediaBrowserServiceCompat  NOTIFICATION_ID (androidx.media.MediaBrowserServiceCompat  Notification (androidx.media.MediaBrowserServiceCompat  NotificationChannel (androidx.media.MediaBrowserServiceCompat  NotificationManager (androidx.media.MediaBrowserServiceCompat  
PendingIntent (androidx.media.MediaBrowserServiceCompat  PlaybackException (androidx.media.MediaBrowserServiceCompat  PlaybackStateCompat (androidx.media.MediaBrowserServiceCompat  Player (androidx.media.MediaBrowserServiceCompat  PlayerNotificationManager (androidx.media.MediaBrowserServiceCompat  PreferenceManager (androidx.media.MediaBrowserServiceCompat  Result (androidx.media.MediaBrowserServiceCompat  START_STICKY (androidx.media.MediaBrowserServiceCompat  Song (androidx.media.MediaBrowserServiceCompat  String (androidx.media.MediaBrowserServiceCompat  Uri (androidx.media.MediaBrowserServiceCompat  addPlaybackListener (androidx.media.MediaBrowserServiceCompat  android (androidx.media.MediaBrowserServiceCompat  apply (androidx.media.MediaBrowserServiceCompat  coerceIn (androidx.media.MediaBrowserServiceCompat  createNotificationChannel (androidx.media.MediaBrowserServiceCompat  currentIndex (androidx.media.MediaBrowserServiceCompat  currentSong (androidx.media.MediaBrowserServiceCompat  	emptyList (androidx.media.MediaBrowserServiceCompat  getCurrentSong (androidx.media.MediaBrowserServiceCompat  getSystemService (androidx.media.MediaBrowserServiceCompat  handleTrackCompletion (androidx.media.MediaBrowserServiceCompat  indices (androidx.media.MediaBrowserServiceCompat  initializeMediaSession (androidx.media.MediaBrowserServiceCompat  initializeNotification (androidx.media.MediaBrowserServiceCompat  initializePlayer (androidx.media.MediaBrowserServiceCompat  invoke (androidx.media.MediaBrowserServiceCompat  
isNotEmpty (androidx.media.MediaBrowserServiceCompat  	isPlaying (androidx.media.MediaBrowserServiceCompat  java (androidx.media.MediaBrowserServiceCompat  let (androidx.media.MediaBrowserServiceCompat  map (androidx.media.MediaBrowserServiceCompat  
mutableListOf (androidx.media.MediaBrowserServiceCompat  mutableSetOf (androidx.media.MediaBrowserServiceCompat  notifyPlaybackStateChanged (androidx.media.MediaBrowserServiceCompat  notifySongChanged (androidx.media.MediaBrowserServiceCompat  onCreate (androidx.media.MediaBrowserServiceCompat  	onDestroy (androidx.media.MediaBrowserServiceCompat  pause (androidx.media.MediaBrowserServiceCompat  play (androidx.media.MediaBrowserServiceCompat  playlist (androidx.media.MediaBrowserServiceCompat  preparePlaylist (androidx.media.MediaBrowserServiceCompat  removePlaybackListener (androidx.media.MediaBrowserServiceCompat  seekTo (androidx.media.MediaBrowserServiceCompat  setPlaylist (androidx.media.MediaBrowserServiceCompat  
skipToNext (androidx.media.MediaBrowserServiceCompat  skipToPrevious (androidx.media.MediaBrowserServiceCompat  startForeground (androidx.media.MediaBrowserServiceCompat  stop (androidx.media.MediaBrowserServiceCompat  stopForeground (androidx.media.MediaBrowserServiceCompat  stopSelf (androidx.media.MediaBrowserServiceCompat  takeIf (androidx.media.MediaBrowserServiceCompat  updateCurrentSong (androidx.media.MediaBrowserServiceCompat  updatePlaybackState (androidx.media.MediaBrowserServiceCompat  updateWidget (androidx.media.MediaBrowserServiceCompat  
sendResult /androidx.media.MediaBrowserServiceCompat.Result  LinearLayoutManager androidx.recyclerview.widget  RecyclerView androidx.recyclerview.widget  Adapter )androidx.recyclerview.widget.RecyclerView  
LayoutManager )androidx.recyclerview.widget.RecyclerView  LinearLayoutManager )androidx.recyclerview.widget.RecyclerView  
ViewHolder )androidx.recyclerview.widget.RecyclerView  adapter )androidx.recyclerview.widget.RecyclerView  apply )androidx.recyclerview.widget.RecyclerView  directoryAdapter )androidx.recyclerview.widget.RecyclerView  
getADAPTER )androidx.recyclerview.widget.RecyclerView  getAPPLY )androidx.recyclerview.widget.RecyclerView  
getAdapter )androidx.recyclerview.widget.RecyclerView  getApply )androidx.recyclerview.widget.RecyclerView  getDIRECTORYAdapter )androidx.recyclerview.widget.RecyclerView  getDirectoryAdapter )androidx.recyclerview.widget.RecyclerView  getLAYOUTManager )androidx.recyclerview.widget.RecyclerView  getLayoutManager )androidx.recyclerview.widget.RecyclerView  getSONGAdapter )androidx.recyclerview.widget.RecyclerView  getSongAdapter )androidx.recyclerview.widget.RecyclerView  
getVISIBILITY )androidx.recyclerview.widget.RecyclerView  
getVisibility )androidx.recyclerview.widget.RecyclerView  
layoutManager )androidx.recyclerview.widget.RecyclerView  
setAdapter )androidx.recyclerview.widget.RecyclerView  setLayoutManager )androidx.recyclerview.widget.RecyclerView  
setVisibility )androidx.recyclerview.widget.RecyclerView  songAdapter )androidx.recyclerview.widget.RecyclerView  
visibility )androidx.recyclerview.widget.RecyclerView  
ContextCompat 1androidx.recyclerview.widget.RecyclerView.Adapter  Int 1androidx.recyclerview.widget.RecyclerView.Adapter  ItemDirectoryBinding 1androidx.recyclerview.widget.RecyclerView.Adapter  ItemSongBinding 1androidx.recyclerview.widget.RecyclerView.Adapter  LayoutInflater 1androidx.recyclerview.widget.RecyclerView.Adapter  List 1androidx.recyclerview.widget.RecyclerView.Adapter  R 1androidx.recyclerview.widget.RecyclerView.Adapter  RecyclerView 1androidx.recyclerview.widget.RecyclerView.Adapter  Song 1androidx.recyclerview.widget.RecyclerView.Adapter  String 1androidx.recyclerview.widget.RecyclerView.Adapter  Unit 1androidx.recyclerview.widget.RecyclerView.Adapter  	ViewGroup 1androidx.recyclerview.widget.RecyclerView.Adapter  android 1androidx.recyclerview.widget.RecyclerView.Adapter  currentSong 1androidx.recyclerview.widget.RecyclerView.Adapter  indexOfFirst 1androidx.recyclerview.widget.RecyclerView.Adapter  invoke 1androidx.recyclerview.widget.RecyclerView.Adapter  listOf 1androidx.recyclerview.widget.RecyclerView.Adapter  notifyDataSetChanged 1androidx.recyclerview.widget.RecyclerView.Adapter  notifyItemChanged 1androidx.recyclerview.widget.RecyclerView.Adapter  
onRemoveClick 1androidx.recyclerview.widget.RecyclerView.Adapter  onSongClick 1androidx.recyclerview.widget.RecyclerView.Adapter  setCurrentSong 1androidx.recyclerview.widget.RecyclerView.Adapter  updateDirectories 1androidx.recyclerview.widget.RecyclerView.Adapter  updateSongs 1androidx.recyclerview.widget.RecyclerView.Adapter  
ContextCompat 4androidx.recyclerview.widget.RecyclerView.ViewHolder  
DirectoryInfo 4androidx.recyclerview.widget.RecyclerView.ViewHolder  Int 4androidx.recyclerview.widget.RecyclerView.ViewHolder  ItemDirectoryBinding 4androidx.recyclerview.widget.RecyclerView.ViewHolder  ItemSongBinding 4androidx.recyclerview.widget.RecyclerView.ViewHolder  R 4androidx.recyclerview.widget.RecyclerView.ViewHolder  Song 4androidx.recyclerview.widget.RecyclerView.ViewHolder  android 4androidx.recyclerview.widget.RecyclerView.ViewHolder  bind 4androidx.recyclerview.widget.RecyclerView.ViewHolder  currentSong 4androidx.recyclerview.widget.RecyclerView.ViewHolder  invoke 4androidx.recyclerview.widget.RecyclerView.ViewHolder  
onRemoveClick 4androidx.recyclerview.widget.RecyclerView.ViewHolder  onSongClick 4androidx.recyclerview.widget.RecyclerView.ViewHolder  AudioAttributes com.google.android.exoplayer2  BrowserRoot com.google.android.exoplayer2  Build com.google.android.exoplayer2  C com.google.android.exoplayer2  ErrorHandler com.google.android.exoplayer2  	ExoPlayer com.google.android.exoplayer2  Intent com.google.android.exoplayer2  MainActivity com.google.android.exoplayer2  	MediaItem com.google.android.exoplayer2  MediaSessionCompat com.google.android.exoplayer2  MusicWidgetProvider com.google.android.exoplayer2  NOTIFICATION_CHANNEL_ID com.google.android.exoplayer2  NOTIFICATION_ID com.google.android.exoplayer2  Notification com.google.android.exoplayer2  NotificationChannel com.google.android.exoplayer2  NotificationManager com.google.android.exoplayer2  
PendingIntent com.google.android.exoplayer2  PlaybackException com.google.android.exoplayer2  PlaybackStateCompat com.google.android.exoplayer2  Player com.google.android.exoplayer2  PlayerNotificationManager com.google.android.exoplayer2  PreferenceManager com.google.android.exoplayer2  START_STICKY com.google.android.exoplayer2  Uri com.google.android.exoplayer2  android com.google.android.exoplayer2  apply com.google.android.exoplayer2  coerceIn com.google.android.exoplayer2  currentIndex com.google.android.exoplayer2  currentSong com.google.android.exoplayer2  	emptyList com.google.android.exoplayer2  forEach com.google.android.exoplayer2  handleTrackCompletion com.google.android.exoplayer2  indices com.google.android.exoplayer2  
isNotEmpty com.google.android.exoplayer2  java com.google.android.exoplayer2  let com.google.android.exoplayer2  map com.google.android.exoplayer2  
mutableListOf com.google.android.exoplayer2  mutableSetOf com.google.android.exoplayer2  notifyPlaybackStateChanged com.google.android.exoplayer2  notifySongChanged com.google.android.exoplayer2  pause com.google.android.exoplayer2  play com.google.android.exoplayer2  playlist com.google.android.exoplayer2  seekTo com.google.android.exoplayer2  
skipToNext com.google.android.exoplayer2  skipToPrevious com.google.android.exoplayer2  startForeground com.google.android.exoplayer2  stop com.google.android.exoplayer2  stopForeground com.google.android.exoplayer2  stopSelf com.google.android.exoplayer2  takeIf com.google.android.exoplayer2  updateCurrentSong com.google.android.exoplayer2  updatePlaybackState com.google.android.exoplayer2  AUDIO_CONTENT_TYPE_MUSIC com.google.android.exoplayer2.C  
TIME_UNSET com.google.android.exoplayer2.C  USAGE_MEDIA com.google.android.exoplayer2.C  Builder 'com.google.android.exoplayer2.ExoPlayer  addListener 'com.google.android.exoplayer2.ExoPlayer  currentPosition 'com.google.android.exoplayer2.ExoPlayer  duration 'com.google.android.exoplayer2.ExoPlayer  getCURRENTPosition 'com.google.android.exoplayer2.ExoPlayer  getCurrentPosition 'com.google.android.exoplayer2.ExoPlayer  getDURATION 'com.google.android.exoplayer2.ExoPlayer  getDuration 'com.google.android.exoplayer2.ExoPlayer  getISPlaying 'com.google.android.exoplayer2.ExoPlayer  getIsPlaying 'com.google.android.exoplayer2.ExoPlayer  getPLAYBACKState 'com.google.android.exoplayer2.ExoPlayer  getPlaybackState 'com.google.android.exoplayer2.ExoPlayer  	isPlaying 'com.google.android.exoplayer2.ExoPlayer  pause 'com.google.android.exoplayer2.ExoPlayer  play 'com.google.android.exoplayer2.ExoPlayer  
playbackState 'com.google.android.exoplayer2.ExoPlayer  prepare 'com.google.android.exoplayer2.ExoPlayer  release 'com.google.android.exoplayer2.ExoPlayer  seekTo 'com.google.android.exoplayer2.ExoPlayer  
seekToNext 'com.google.android.exoplayer2.ExoPlayer  seekToPrevious 'com.google.android.exoplayer2.ExoPlayer  setCurrentPosition 'com.google.android.exoplayer2.ExoPlayer  setDuration 'com.google.android.exoplayer2.ExoPlayer  
setMediaItems 'com.google.android.exoplayer2.ExoPlayer  setPlaybackState 'com.google.android.exoplayer2.ExoPlayer  
setPlaying 'com.google.android.exoplayer2.ExoPlayer  stop 'com.google.android.exoplayer2.ExoPlayer  build /com.google.android.exoplayer2.ExoPlayer.Builder  setAudioAttributes /com.google.android.exoplayer2.ExoPlayer.Builder  setHandleAudioBecomingNoisy /com.google.android.exoplayer2.ExoPlayer.Builder  fromUri 'com.google.android.exoplayer2.MediaItem  $DISCONTINUITY_REASON_AUTO_TRANSITION $com.google.android.exoplayer2.Player  Listener $com.google.android.exoplayer2.Player  PositionInfo $com.google.android.exoplayer2.Player  REPEAT_MODE_ALL $com.google.android.exoplayer2.Player  REPEAT_MODE_OFF $com.google.android.exoplayer2.Player  REPEAT_MODE_ONE $com.google.android.exoplayer2.Player  STATE_BUFFERING $com.google.android.exoplayer2.Player  AudioAttributes #com.google.android.exoplayer2.audio  Builder 3com.google.android.exoplayer2.audio.AudioAttributes  build ;com.google.android.exoplayer2.audio.AudioAttributes.Builder  setContentType ;com.google.android.exoplayer2.audio.AudioAttributes.Builder  setUsage ;com.google.android.exoplayer2.audio.AudioAttributes.Builder  PlayerNotificationManager  com.google.android.exoplayer2.ui  BitmapCallback :com.google.android.exoplayer2.ui.PlayerNotificationManager  Builder :com.google.android.exoplayer2.ui.PlayerNotificationManager  MediaDescriptionAdapter :com.google.android.exoplayer2.ui.PlayerNotificationManager  NotificationListener :com.google.android.exoplayer2.ui.PlayerNotificationManager  setMediaSessionToken :com.google.android.exoplayer2.ui.PlayerNotificationManager  	setPlayer :com.google.android.exoplayer2.ui.PlayerNotificationManager  build Bcom.google.android.exoplayer2.ui.PlayerNotificationManager.Builder  setMediaDescriptionAdapter Bcom.google.android.exoplayer2.ui.PlayerNotificationManager.Builder  setNotificationListener Bcom.google.android.exoplayer2.ui.PlayerNotificationManager.Builder  getISEnabled 1com.google.android.material.button.MaterialButton  getIsEnabled 1com.google.android.material.button.MaterialButton  
getVISIBILITY 1com.google.android.material.button.MaterialButton  
getVisibility 1com.google.android.material.button.MaterialButton  	isEnabled 1com.google.android.material.button.MaterialButton  
setEnabled 1com.google.android.material.button.MaterialButton  setOnClickListener 1com.google.android.material.button.MaterialButton  
setVisibility 1com.google.android.material.button.MaterialButton  
visibility 1com.google.android.material.button.MaterialButton  setOnClickListener Ecom.google.android.material.floatingactionbutton.FloatingActionButton  setOnClickListener ?com.google.android.material.internal.VisibilityAwareImageButton  Activity com.minimalmusicplayer  !ActivityDirectorySelectionBinding com.minimalmusicplayer  ActivityMainBinding com.minimalmusicplayer  ActivityResultContracts com.minimalmusicplayer  Array com.minimalmusicplayer  Boolean com.minimalmusicplayer  Context com.minimalmusicplayer  DirectoryAdapter com.minimalmusicplayer  DirectorySelectionActivity com.minimalmusicplayer  DocumentsContract com.minimalmusicplayer  ErrorHandler com.minimalmusicplayer  	Exception com.minimalmusicplayer  FileScanner com.minimalmusicplayer  Int com.minimalmusicplayer  IntArray com.minimalmusicplayer  Intent com.minimalmusicplayer  LinearLayoutManager com.minimalmusicplayer  Long com.minimalmusicplayer  MainActivity com.minimalmusicplayer  MusicService com.minimalmusicplayer  PermissionHelper com.minimalmusicplayer  PreferenceManager com.minimalmusicplayer  R com.minimalmusicplayer  	RESULT_OK com.minimalmusicplayer  SongAdapter com.minimalmusicplayer  String com.minimalmusicplayer  Toast com.minimalmusicplayer  Uri com.minimalmusicplayer  View com.minimalmusicplayer  apply com.minimalmusicplayer  binding com.minimalmusicplayer  directoryAdapter com.minimalmusicplayer  fileScanner com.minimalmusicplayer  finish com.minimalmusicplayer  ifEmpty com.minimalmusicplayer  
isNotEmpty com.minimalmusicplayer  isServiceBound com.minimalmusicplayer  java com.minimalmusicplayer  launch com.minimalmusicplayer  let com.minimalmusicplayer  lifecycleScope com.minimalmusicplayer  listOf com.minimalmusicplayer  map com.minimalmusicplayer  musicService com.minimalmusicplayer  
mutableListOf com.minimalmusicplayer  
plusAssign com.minimalmusicplayer  preferenceManager com.minimalmusicplayer  	setResult com.minimalmusicplayer  showEmptyState com.minimalmusicplayer  songAdapter com.minimalmusicplayer  songs com.minimalmusicplayer  sortedBy com.minimalmusicplayer  split com.minimalmusicplayer  startMusicService com.minimalmusicplayer  toMutableSet com.minimalmusicplayer  updatePlaybackUI com.minimalmusicplayer  Activity 1com.minimalmusicplayer.DirectorySelectionActivity  !ActivityDirectorySelectionBinding 1com.minimalmusicplayer.DirectorySelectionActivity  ActivityResultContracts 1com.minimalmusicplayer.DirectorySelectionActivity  Boolean 1com.minimalmusicplayer.DirectorySelectionActivity  Bundle 1com.minimalmusicplayer.DirectorySelectionActivity  DirectoryAdapter 1com.minimalmusicplayer.DirectorySelectionActivity  DocumentsContract 1com.minimalmusicplayer.DirectorySelectionActivity  	Exception 1com.minimalmusicplayer.DirectorySelectionActivity  FileScanner 1com.minimalmusicplayer.DirectorySelectionActivity  Intent 1com.minimalmusicplayer.DirectorySelectionActivity  LinearLayoutManager 1com.minimalmusicplayer.DirectorySelectionActivity  PreferenceManager 1com.minimalmusicplayer.DirectorySelectionActivity  String 1com.minimalmusicplayer.DirectorySelectionActivity  Toast 1com.minimalmusicplayer.DirectorySelectionActivity  Uri 1com.minimalmusicplayer.DirectorySelectionActivity  View 1com.minimalmusicplayer.DirectorySelectionActivity  apply 1com.minimalmusicplayer.DirectorySelectionActivity  binding 1com.minimalmusicplayer.DirectorySelectionActivity  contentResolver 1com.minimalmusicplayer.DirectorySelectionActivity  directoryAdapter 1com.minimalmusicplayer.DirectorySelectionActivity  directoryPickerLauncher 1com.minimalmusicplayer.DirectorySelectionActivity  fileScanner 1com.minimalmusicplayer.DirectorySelectionActivity  finish 1com.minimalmusicplayer.DirectorySelectionActivity  getAPPLY 1com.minimalmusicplayer.DirectorySelectionActivity  getApply 1com.minimalmusicplayer.DirectorySelectionActivity  getCONTENTResolver 1com.minimalmusicplayer.DirectorySelectionActivity  getContentResolver 1com.minimalmusicplayer.DirectorySelectionActivity  getDirectoryDisplayName 1com.minimalmusicplayer.DirectorySelectionActivity  
getIFEmpty 1com.minimalmusicplayer.DirectorySelectionActivity  
getIfEmpty 1com.minimalmusicplayer.DirectorySelectionActivity  	getLAUNCH 1com.minimalmusicplayer.DirectorySelectionActivity  getLAYOUTInflater 1com.minimalmusicplayer.DirectorySelectionActivity  getLET 1com.minimalmusicplayer.DirectorySelectionActivity  getLIFECYCLEScope 1com.minimalmusicplayer.DirectorySelectionActivity  	getLaunch 1com.minimalmusicplayer.DirectorySelectionActivity  getLayoutInflater 1com.minimalmusicplayer.DirectorySelectionActivity  getLet 1com.minimalmusicplayer.DirectorySelectionActivity  getLifecycleScope 1com.minimalmusicplayer.DirectorySelectionActivity  getMAP 1com.minimalmusicplayer.DirectorySelectionActivity  getMap 1com.minimalmusicplayer.DirectorySelectionActivity  
getPLUSAssign 1com.minimalmusicplayer.DirectorySelectionActivity  
getPlusAssign 1com.minimalmusicplayer.DirectorySelectionActivity  getSPLIT 1com.minimalmusicplayer.DirectorySelectionActivity  getSUPPORTActionBar 1com.minimalmusicplayer.DirectorySelectionActivity  getSplit 1com.minimalmusicplayer.DirectorySelectionActivity  getSupportActionBar 1com.minimalmusicplayer.DirectorySelectionActivity  getTOMutableSet 1com.minimalmusicplayer.DirectorySelectionActivity  getToMutableSet 1com.minimalmusicplayer.DirectorySelectionActivity  handleDirectorySelection 1com.minimalmusicplayer.DirectorySelectionActivity  ifEmpty 1com.minimalmusicplayer.DirectorySelectionActivity  invoke 1com.minimalmusicplayer.DirectorySelectionActivity  launch 1com.minimalmusicplayer.DirectorySelectionActivity  layoutInflater 1com.minimalmusicplayer.DirectorySelectionActivity  let 1com.minimalmusicplayer.DirectorySelectionActivity  lifecycleScope 1com.minimalmusicplayer.DirectorySelectionActivity  loadSelectedDirectories 1com.minimalmusicplayer.DirectorySelectionActivity  map 1com.minimalmusicplayer.DirectorySelectionActivity  
onBackPressed 1com.minimalmusicplayer.DirectorySelectionActivity  openDirectoryPicker 1com.minimalmusicplayer.DirectorySelectionActivity  
plusAssign 1com.minimalmusicplayer.DirectorySelectionActivity  preferenceManager 1com.minimalmusicplayer.DirectorySelectionActivity  registerForActivityResult 1com.minimalmusicplayer.DirectorySelectionActivity  removeDirectory 1com.minimalmusicplayer.DirectorySelectionActivity  scanAllDirectories 1com.minimalmusicplayer.DirectorySelectionActivity  setContentResolver 1com.minimalmusicplayer.DirectorySelectionActivity  setContentView 1com.minimalmusicplayer.DirectorySelectionActivity  setLayoutInflater 1com.minimalmusicplayer.DirectorySelectionActivity  	setResult 1com.minimalmusicplayer.DirectorySelectionActivity  setSupportActionBar 1com.minimalmusicplayer.DirectorySelectionActivity  setupClickListeners 1com.minimalmusicplayer.DirectorySelectionActivity  setupRecyclerView 1com.minimalmusicplayer.DirectorySelectionActivity  setupToolbar 1com.minimalmusicplayer.DirectorySelectionActivity  split 1com.minimalmusicplayer.DirectorySelectionActivity  supportActionBar 1com.minimalmusicplayer.DirectorySelectionActivity  toMutableSet 1com.minimalmusicplayer.DirectorySelectionActivity  ActivityMainBinding #com.minimalmusicplayer.MainActivity  ActivityResultContracts #com.minimalmusicplayer.MainActivity  Array #com.minimalmusicplayer.MainActivity  Boolean #com.minimalmusicplayer.MainActivity  Bundle #com.minimalmusicplayer.MainActivity  
ComponentName #com.minimalmusicplayer.MainActivity  Context #com.minimalmusicplayer.MainActivity  DirectorySelectionActivity #com.minimalmusicplayer.MainActivity  ErrorHandler #com.minimalmusicplayer.MainActivity  	Exception #com.minimalmusicplayer.MainActivity  FileScanner #com.minimalmusicplayer.MainActivity  IBinder #com.minimalmusicplayer.MainActivity  Int #com.minimalmusicplayer.MainActivity  IntArray #com.minimalmusicplayer.MainActivity  Intent #com.minimalmusicplayer.MainActivity  LinearLayoutManager #com.minimalmusicplayer.MainActivity  Long #com.minimalmusicplayer.MainActivity  Menu #com.minimalmusicplayer.MainActivity  MenuItem #com.minimalmusicplayer.MainActivity  MusicService #com.minimalmusicplayer.MainActivity  PermissionHelper #com.minimalmusicplayer.MainActivity  PreferenceManager #com.minimalmusicplayer.MainActivity  R #com.minimalmusicplayer.MainActivity  	RESULT_OK #com.minimalmusicplayer.MainActivity  ServiceConnection #com.minimalmusicplayer.MainActivity  Song #com.minimalmusicplayer.MainActivity  SongAdapter #com.minimalmusicplayer.MainActivity  String #com.minimalmusicplayer.MainActivity  Toast #com.minimalmusicplayer.MainActivity  Uri #com.minimalmusicplayer.MainActivity  View #com.minimalmusicplayer.MainActivity  apply #com.minimalmusicplayer.MainActivity  bindService #com.minimalmusicplayer.MainActivity  binding #com.minimalmusicplayer.MainActivity  checkPermissionsAndLoadMusic #com.minimalmusicplayer.MainActivity  currentSong #com.minimalmusicplayer.MainActivity  directorySelectionLauncher #com.minimalmusicplayer.MainActivity  fileScanner #com.minimalmusicplayer.MainActivity  finish #com.minimalmusicplayer.MainActivity  getAPPLY #com.minimalmusicplayer.MainActivity  getApply #com.minimalmusicplayer.MainActivity  
getISNotEmpty #com.minimalmusicplayer.MainActivity  
getIsNotEmpty #com.minimalmusicplayer.MainActivity  	getLAUNCH #com.minimalmusicplayer.MainActivity  getLAYOUTInflater #com.minimalmusicplayer.MainActivity  getLET #com.minimalmusicplayer.MainActivity  getLIFECYCLEScope #com.minimalmusicplayer.MainActivity  	getLISTOf #com.minimalmusicplayer.MainActivity  	getLaunch #com.minimalmusicplayer.MainActivity  getLayoutInflater #com.minimalmusicplayer.MainActivity  getLet #com.minimalmusicplayer.MainActivity  getLifecycleScope #com.minimalmusicplayer.MainActivity  	getListOf #com.minimalmusicplayer.MainActivity  getMENUInflater #com.minimalmusicplayer.MainActivity  getMUTABLEListOf #com.minimalmusicplayer.MainActivity  getMenuInflater #com.minimalmusicplayer.MainActivity  getMutableListOf #com.minimalmusicplayer.MainActivity  getSORTEDBy #com.minimalmusicplayer.MainActivity  getSUPPORTActionBar #com.minimalmusicplayer.MainActivity  getSortedBy #com.minimalmusicplayer.MainActivity  getSupportActionBar #com.minimalmusicplayer.MainActivity  invoke #com.minimalmusicplayer.MainActivity  
isNotEmpty #com.minimalmusicplayer.MainActivity  isServiceBound #com.minimalmusicplayer.MainActivity  java #com.minimalmusicplayer.MainActivity  launch #com.minimalmusicplayer.MainActivity  layoutInflater #com.minimalmusicplayer.MainActivity  let #com.minimalmusicplayer.MainActivity  lifecycleScope #com.minimalmusicplayer.MainActivity  listOf #com.minimalmusicplayer.MainActivity  	loadMusic #com.minimalmusicplayer.MainActivity  menuInflater #com.minimalmusicplayer.MainActivity  musicService #com.minimalmusicplayer.MainActivity  
mutableListOf #com.minimalmusicplayer.MainActivity  playSong #com.minimalmusicplayer.MainActivity  preferenceManager #com.minimalmusicplayer.MainActivity  registerForActivityResult #com.minimalmusicplayer.MainActivity  requestPermissions #com.minimalmusicplayer.MainActivity  
runOnUiThread #com.minimalmusicplayer.MainActivity  serviceConnection #com.minimalmusicplayer.MainActivity  setContentView #com.minimalmusicplayer.MainActivity  setLayoutInflater #com.minimalmusicplayer.MainActivity  setMenuInflater #com.minimalmusicplayer.MainActivity  setSupportActionBar #com.minimalmusicplayer.MainActivity  setupPlaybackControls #com.minimalmusicplayer.MainActivity  setupRecyclerView #com.minimalmusicplayer.MainActivity  setupToolbar #com.minimalmusicplayer.MainActivity  showEmptyState #com.minimalmusicplayer.MainActivity  showPermissionDeniedMessage #com.minimalmusicplayer.MainActivity  songAdapter #com.minimalmusicplayer.MainActivity  songs #com.minimalmusicplayer.MainActivity  sortedBy #com.minimalmusicplayer.MainActivity  startMusicService #com.minimalmusicplayer.MainActivity  startService #com.minimalmusicplayer.MainActivity  supportActionBar #com.minimalmusicplayer.MainActivity  togglePlayback #com.minimalmusicplayer.MainActivity  
unbindService #com.minimalmusicplayer.MainActivity  updateCurrentSong #com.minimalmusicplayer.MainActivity  updatePlaybackUI #com.minimalmusicplayer.MainActivity  getISServiceBound Hcom.minimalmusicplayer.MainActivity.serviceConnection.<no name provided>  getIsServiceBound Hcom.minimalmusicplayer.MainActivity.serviceConnection.<no name provided>  getMUSICService Hcom.minimalmusicplayer.MainActivity.serviceConnection.<no name provided>  getMusicService Hcom.minimalmusicplayer.MainActivity.serviceConnection.<no name provided>  getUPDATEPlaybackUI Hcom.minimalmusicplayer.MainActivity.serviceConnection.<no name provided>  getUpdatePlaybackUI Hcom.minimalmusicplayer.MainActivity.serviceConnection.<no name provided>  isServiceBound Hcom.minimalmusicplayer.MainActivity.serviceConnection.<no name provided>  color com.minimalmusicplayer.R  drawable com.minimalmusicplayer.R  id com.minimalmusicplayer.R  layout com.minimalmusicplayer.R  menu com.minimalmusicplayer.R  current_song_background com.minimalmusicplayer.R.color  current_song_text com.minimalmusicplayer.R.color  primary_text com.minimalmusicplayer.R.color  ic_pause !com.minimalmusicplayer.R.drawable  ic_play !com.minimalmusicplayer.R.drawable  action_refresh com.minimalmusicplayer.R.id  action_select_directories com.minimalmusicplayer.R.id  
widget_artist com.minimalmusicplayer.R.id  widget_container com.minimalmusicplayer.R.id  widget_next com.minimalmusicplayer.R.id  widget_play_pause com.minimalmusicplayer.R.id  widget_previous com.minimalmusicplayer.R.id  widget_song_title com.minimalmusicplayer.R.id  widget_music_player com.minimalmusicplayer.R.layout  	main_menu com.minimalmusicplayer.R.menu  
ContextCompat com.minimalmusicplayer.adapter  DirectoryAdapter com.minimalmusicplayer.adapter  Int com.minimalmusicplayer.adapter  ItemDirectoryBinding com.minimalmusicplayer.adapter  ItemSongBinding com.minimalmusicplayer.adapter  LayoutInflater com.minimalmusicplayer.adapter  List com.minimalmusicplayer.adapter  R com.minimalmusicplayer.adapter  SongAdapter com.minimalmusicplayer.adapter  String com.minimalmusicplayer.adapter  Unit com.minimalmusicplayer.adapter  android com.minimalmusicplayer.adapter  currentSong com.minimalmusicplayer.adapter  indexOfFirst com.minimalmusicplayer.adapter  listOf com.minimalmusicplayer.adapter  
onRemoveClick com.minimalmusicplayer.adapter  onSongClick com.minimalmusicplayer.adapter  
DirectoryInfo /com.minimalmusicplayer.adapter.DirectoryAdapter  DirectoryViewHolder /com.minimalmusicplayer.adapter.DirectoryAdapter  Int /com.minimalmusicplayer.adapter.DirectoryAdapter  ItemDirectoryBinding /com.minimalmusicplayer.adapter.DirectoryAdapter  LayoutInflater /com.minimalmusicplayer.adapter.DirectoryAdapter  List /com.minimalmusicplayer.adapter.DirectoryAdapter  RecyclerView /com.minimalmusicplayer.adapter.DirectoryAdapter  String /com.minimalmusicplayer.adapter.DirectoryAdapter  Unit /com.minimalmusicplayer.adapter.DirectoryAdapter  	ViewGroup /com.minimalmusicplayer.adapter.DirectoryAdapter  directories /com.minimalmusicplayer.adapter.DirectoryAdapter  	getLISTOf /com.minimalmusicplayer.adapter.DirectoryAdapter  	getListOf /com.minimalmusicplayer.adapter.DirectoryAdapter  invoke /com.minimalmusicplayer.adapter.DirectoryAdapter  listOf /com.minimalmusicplayer.adapter.DirectoryAdapter  notifyDataSetChanged /com.minimalmusicplayer.adapter.DirectoryAdapter  
onRemoveClick /com.minimalmusicplayer.adapter.DirectoryAdapter  updateDirectories /com.minimalmusicplayer.adapter.DirectoryAdapter  String =com.minimalmusicplayer.adapter.DirectoryAdapter.DirectoryInfo  displayName =com.minimalmusicplayer.adapter.DirectoryAdapter.DirectoryInfo  uri =com.minimalmusicplayer.adapter.DirectoryAdapter.DirectoryInfo  
DirectoryInfo Ccom.minimalmusicplayer.adapter.DirectoryAdapter.DirectoryViewHolder  ItemDirectoryBinding Ccom.minimalmusicplayer.adapter.DirectoryAdapter.DirectoryViewHolder  bind Ccom.minimalmusicplayer.adapter.DirectoryAdapter.DirectoryViewHolder  binding Ccom.minimalmusicplayer.adapter.DirectoryAdapter.DirectoryViewHolder  getONRemoveClick Ccom.minimalmusicplayer.adapter.DirectoryAdapter.DirectoryViewHolder  getOnRemoveClick Ccom.minimalmusicplayer.adapter.DirectoryAdapter.DirectoryViewHolder  invoke Ccom.minimalmusicplayer.adapter.DirectoryAdapter.DirectoryViewHolder  
onRemoveClick Ccom.minimalmusicplayer.adapter.DirectoryAdapter.DirectoryViewHolder  
ContextCompat *com.minimalmusicplayer.adapter.SongAdapter  Int *com.minimalmusicplayer.adapter.SongAdapter  ItemSongBinding *com.minimalmusicplayer.adapter.SongAdapter  LayoutInflater *com.minimalmusicplayer.adapter.SongAdapter  List *com.minimalmusicplayer.adapter.SongAdapter  R *com.minimalmusicplayer.adapter.SongAdapter  RecyclerView *com.minimalmusicplayer.adapter.SongAdapter  Song *com.minimalmusicplayer.adapter.SongAdapter  SongViewHolder *com.minimalmusicplayer.adapter.SongAdapter  Unit *com.minimalmusicplayer.adapter.SongAdapter  	ViewGroup *com.minimalmusicplayer.adapter.SongAdapter  android *com.minimalmusicplayer.adapter.SongAdapter  currentSong *com.minimalmusicplayer.adapter.SongAdapter  
getANDROID *com.minimalmusicplayer.adapter.SongAdapter  
getAndroid *com.minimalmusicplayer.adapter.SongAdapter  getINDEXOfFirst *com.minimalmusicplayer.adapter.SongAdapter  getIndexOfFirst *com.minimalmusicplayer.adapter.SongAdapter  	getLISTOf *com.minimalmusicplayer.adapter.SongAdapter  	getListOf *com.minimalmusicplayer.adapter.SongAdapter  indexOfFirst *com.minimalmusicplayer.adapter.SongAdapter  invoke *com.minimalmusicplayer.adapter.SongAdapter  listOf *com.minimalmusicplayer.adapter.SongAdapter  notifyDataSetChanged *com.minimalmusicplayer.adapter.SongAdapter  notifyItemChanged *com.minimalmusicplayer.adapter.SongAdapter  onSongClick *com.minimalmusicplayer.adapter.SongAdapter  setCurrentSong *com.minimalmusicplayer.adapter.SongAdapter  songs *com.minimalmusicplayer.adapter.SongAdapter  updateSongs *com.minimalmusicplayer.adapter.SongAdapter  
ContextCompat 9com.minimalmusicplayer.adapter.SongAdapter.SongViewHolder  Int 9com.minimalmusicplayer.adapter.SongAdapter.SongViewHolder  ItemSongBinding 9com.minimalmusicplayer.adapter.SongAdapter.SongViewHolder  R 9com.minimalmusicplayer.adapter.SongAdapter.SongViewHolder  Song 9com.minimalmusicplayer.adapter.SongAdapter.SongViewHolder  android 9com.minimalmusicplayer.adapter.SongAdapter.SongViewHolder  bind 9com.minimalmusicplayer.adapter.SongAdapter.SongViewHolder  binding 9com.minimalmusicplayer.adapter.SongAdapter.SongViewHolder  currentSong 9com.minimalmusicplayer.adapter.SongAdapter.SongViewHolder  
getANDROID 9com.minimalmusicplayer.adapter.SongAdapter.SongViewHolder  
getAndroid 9com.minimalmusicplayer.adapter.SongAdapter.SongViewHolder  getCURRENTSong 9com.minimalmusicplayer.adapter.SongAdapter.SongViewHolder  getCurrentSong 9com.minimalmusicplayer.adapter.SongAdapter.SongViewHolder  getONSongClick 9com.minimalmusicplayer.adapter.SongAdapter.SongViewHolder  getOnSongClick 9com.minimalmusicplayer.adapter.SongAdapter.SongViewHolder  invoke 9com.minimalmusicplayer.adapter.SongAdapter.SongViewHolder  onSongClick 9com.minimalmusicplayer.adapter.SongAdapter.SongViewHolder  !ActivityDirectorySelectionBinding "com.minimalmusicplayer.databinding  ActivityMainBinding "com.minimalmusicplayer.databinding  ItemDirectoryBinding "com.minimalmusicplayer.databinding  ItemSongBinding "com.minimalmusicplayer.databinding  
buttonScanAll Dcom.minimalmusicplayer.databinding.ActivityDirectorySelectionBinding  fabAddDirectory Dcom.minimalmusicplayer.databinding.ActivityDirectorySelectionBinding  getROOT Dcom.minimalmusicplayer.databinding.ActivityDirectorySelectionBinding  getRoot Dcom.minimalmusicplayer.databinding.ActivityDirectorySelectionBinding  inflate Dcom.minimalmusicplayer.databinding.ActivityDirectorySelectionBinding  progressBar Dcom.minimalmusicplayer.databinding.ActivityDirectorySelectionBinding  recyclerViewDirectories Dcom.minimalmusicplayer.databinding.ActivityDirectorySelectionBinding  root Dcom.minimalmusicplayer.databinding.ActivityDirectorySelectionBinding  setRoot Dcom.minimalmusicplayer.databinding.ActivityDirectorySelectionBinding  textViewEmptyState Dcom.minimalmusicplayer.databinding.ActivityDirectorySelectionBinding  toolbar Dcom.minimalmusicplayer.databinding.ActivityDirectorySelectionBinding  
buttonNext 6com.minimalmusicplayer.databinding.ActivityMainBinding  buttonPlayPause 6com.minimalmusicplayer.databinding.ActivityMainBinding  buttonPrevious 6com.minimalmusicplayer.databinding.ActivityMainBinding  getROOT 6com.minimalmusicplayer.databinding.ActivityMainBinding  getRoot 6com.minimalmusicplayer.databinding.ActivityMainBinding  inflate 6com.minimalmusicplayer.databinding.ActivityMainBinding  playerContainer 6com.minimalmusicplayer.databinding.ActivityMainBinding  progressBar 6com.minimalmusicplayer.databinding.ActivityMainBinding  recyclerViewSongs 6com.minimalmusicplayer.databinding.ActivityMainBinding  root 6com.minimalmusicplayer.databinding.ActivityMainBinding  setRoot 6com.minimalmusicplayer.databinding.ActivityMainBinding  textViewEmptyState 6com.minimalmusicplayer.databinding.ActivityMainBinding  textViewSongArtist 6com.minimalmusicplayer.databinding.ActivityMainBinding  textViewSongTitle 6com.minimalmusicplayer.databinding.ActivityMainBinding  toolbar 6com.minimalmusicplayer.databinding.ActivityMainBinding  buttonRemove 7com.minimalmusicplayer.databinding.ItemDirectoryBinding  getROOT 7com.minimalmusicplayer.databinding.ItemDirectoryBinding  getRoot 7com.minimalmusicplayer.databinding.ItemDirectoryBinding  inflate 7com.minimalmusicplayer.databinding.ItemDirectoryBinding  root 7com.minimalmusicplayer.databinding.ItemDirectoryBinding  setRoot 7com.minimalmusicplayer.databinding.ItemDirectoryBinding  textViewDirectoryName 7com.minimalmusicplayer.databinding.ItemDirectoryBinding  textViewDirectoryPath 7com.minimalmusicplayer.databinding.ItemDirectoryBinding  getROOT 2com.minimalmusicplayer.databinding.ItemSongBinding  getRoot 2com.minimalmusicplayer.databinding.ItemSongBinding  inflate 2com.minimalmusicplayer.databinding.ItemSongBinding  root 2com.minimalmusicplayer.databinding.ItemSongBinding  setRoot 2com.minimalmusicplayer.databinding.ItemSongBinding  textViewArtist 2com.minimalmusicplayer.databinding.ItemSongBinding  textViewDuration 2com.minimalmusicplayer.databinding.ItemSongBinding  
textViewTitle 2com.minimalmusicplayer.databinding.ItemSongBinding  Boolean com.minimalmusicplayer.model  Long com.minimalmusicplayer.model  Song com.minimalmusicplayer.model  String com.minimalmusicplayer.model  format com.minimalmusicplayer.model  
isNotBlank com.minimalmusicplayer.model  	lowercase com.minimalmusicplayer.model  setOf com.minimalmusicplayer.model  substringAfterLast com.minimalmusicplayer.model  substringBeforeLast com.minimalmusicplayer.model  Boolean !com.minimalmusicplayer.model.Song  Long !com.minimalmusicplayer.model.Song  String !com.minimalmusicplayer.model.Song  album !com.minimalmusicplayer.model.Song  artist !com.minimalmusicplayer.model.Song  duration !com.minimalmusicplayer.model.Song  equals !com.minimalmusicplayer.model.Song  format !com.minimalmusicplayer.model.Song  getDisplayArtist !com.minimalmusicplayer.model.Song  getDisplayTitle !com.minimalmusicplayer.model.Song  getDurationString !com.minimalmusicplayer.model.Song  	getFORMAT !com.minimalmusicplayer.model.Song  getFileExtension !com.minimalmusicplayer.model.Song  	getFormat !com.minimalmusicplayer.model.Song  
getISNotBlank !com.minimalmusicplayer.model.Song  
getIsNotBlank !com.minimalmusicplayer.model.Song  getLET !com.minimalmusicplayer.model.Song  getLOWERCASE !com.minimalmusicplayer.model.Song  getLet !com.minimalmusicplayer.model.Song  getLowercase !com.minimalmusicplayer.model.Song  getSETOf !com.minimalmusicplayer.model.Song  getSUBSTRINGAfterLast !com.minimalmusicplayer.model.Song  getSUBSTRINGBeforeLast !com.minimalmusicplayer.model.Song  getSetOf !com.minimalmusicplayer.model.Song  getSubstringAfterLast !com.minimalmusicplayer.model.Song  getSubstringBeforeLast !com.minimalmusicplayer.model.Song  
isNotBlank !com.minimalmusicplayer.model.Song  let !com.minimalmusicplayer.model.Song  	lowercase !com.minimalmusicplayer.model.Song  path !com.minimalmusicplayer.model.Song  setOf !com.minimalmusicplayer.model.Song  substringAfterLast !com.minimalmusicplayer.model.Song  substringBeforeLast !com.minimalmusicplayer.model.Song  title !com.minimalmusicplayer.model.Song  AudioAttributes com.minimalmusicplayer.service  Boolean com.minimalmusicplayer.service  BrowserRoot com.minimalmusicplayer.service  Build com.minimalmusicplayer.service  C com.minimalmusicplayer.service  CharSequence com.minimalmusicplayer.service  ErrorHandler com.minimalmusicplayer.service  	ExoPlayer com.minimalmusicplayer.service  Int com.minimalmusicplayer.service  Intent com.minimalmusicplayer.service  List com.minimalmusicplayer.service  Long com.minimalmusicplayer.service  MainActivity com.minimalmusicplayer.service  	MediaItem com.minimalmusicplayer.service  MediaSessionCompat com.minimalmusicplayer.service  MusicService com.minimalmusicplayer.service  MusicWidgetProvider com.minimalmusicplayer.service  MutableList com.minimalmusicplayer.service  NOTIFICATION_CHANNEL_ID com.minimalmusicplayer.service  NOTIFICATION_ID com.minimalmusicplayer.service  Notification com.minimalmusicplayer.service  NotificationChannel com.minimalmusicplayer.service  NotificationManager com.minimalmusicplayer.service  
PendingIntent com.minimalmusicplayer.service  PlaybackException com.minimalmusicplayer.service  PlaybackStateCompat com.minimalmusicplayer.service  Player com.minimalmusicplayer.service  PlayerNotificationManager com.minimalmusicplayer.service  PreferenceManager com.minimalmusicplayer.service  START_STICKY com.minimalmusicplayer.service  String com.minimalmusicplayer.service  Uri com.minimalmusicplayer.service  android com.minimalmusicplayer.service  apply com.minimalmusicplayer.service  coerceIn com.minimalmusicplayer.service  currentIndex com.minimalmusicplayer.service  currentSong com.minimalmusicplayer.service  	emptyList com.minimalmusicplayer.service  forEach com.minimalmusicplayer.service  handleTrackCompletion com.minimalmusicplayer.service  indices com.minimalmusicplayer.service  
isNotEmpty com.minimalmusicplayer.service  java com.minimalmusicplayer.service  let com.minimalmusicplayer.service  map com.minimalmusicplayer.service  
mutableListOf com.minimalmusicplayer.service  mutableSetOf com.minimalmusicplayer.service  notifyPlaybackStateChanged com.minimalmusicplayer.service  notifySongChanged com.minimalmusicplayer.service  pause com.minimalmusicplayer.service  play com.minimalmusicplayer.service  playlist com.minimalmusicplayer.service  seekTo com.minimalmusicplayer.service  
skipToNext com.minimalmusicplayer.service  skipToPrevious com.minimalmusicplayer.service  startForeground com.minimalmusicplayer.service  stop com.minimalmusicplayer.service  stopForeground com.minimalmusicplayer.service  stopSelf com.minimalmusicplayer.service  takeIf com.minimalmusicplayer.service  updateCurrentSong com.minimalmusicplayer.service  updatePlaybackState com.minimalmusicplayer.service  AudioAttributes +com.minimalmusicplayer.service.MusicService  Binder +com.minimalmusicplayer.service.MusicService  Boolean +com.minimalmusicplayer.service.MusicService  BrowserRoot +com.minimalmusicplayer.service.MusicService  Build +com.minimalmusicplayer.service.MusicService  Bundle +com.minimalmusicplayer.service.MusicService  C +com.minimalmusicplayer.service.MusicService  CharSequence +com.minimalmusicplayer.service.MusicService  	Companion +com.minimalmusicplayer.service.MusicService  ErrorHandler +com.minimalmusicplayer.service.MusicService  	ExoPlayer +com.minimalmusicplayer.service.MusicService  IBinder +com.minimalmusicplayer.service.MusicService  Int +com.minimalmusicplayer.service.MusicService  Intent +com.minimalmusicplayer.service.MusicService  List +com.minimalmusicplayer.service.MusicService  Long +com.minimalmusicplayer.service.MusicService  MainActivity +com.minimalmusicplayer.service.MusicService  MediaBrowserCompat +com.minimalmusicplayer.service.MusicService  	MediaItem +com.minimalmusicplayer.service.MusicService  MediaSessionCompat +com.minimalmusicplayer.service.MusicService  MusicBinder +com.minimalmusicplayer.service.MusicService  MusicService +com.minimalmusicplayer.service.MusicService  MusicWidgetProvider +com.minimalmusicplayer.service.MusicService  MutableList +com.minimalmusicplayer.service.MusicService  NOTIFICATION_CHANNEL_ID +com.minimalmusicplayer.service.MusicService  NOTIFICATION_ID +com.minimalmusicplayer.service.MusicService  Notification +com.minimalmusicplayer.service.MusicService  NotificationChannel +com.minimalmusicplayer.service.MusicService  NotificationManager +com.minimalmusicplayer.service.MusicService  
PendingIntent +com.minimalmusicplayer.service.MusicService  PlaybackException +com.minimalmusicplayer.service.MusicService  PlaybackListener +com.minimalmusicplayer.service.MusicService  PlaybackStateCompat +com.minimalmusicplayer.service.MusicService  Player +com.minimalmusicplayer.service.MusicService  PlayerNotificationManager +com.minimalmusicplayer.service.MusicService  PreferenceManager +com.minimalmusicplayer.service.MusicService  Result +com.minimalmusicplayer.service.MusicService  START_STICKY +com.minimalmusicplayer.service.MusicService  Song +com.minimalmusicplayer.service.MusicService  String +com.minimalmusicplayer.service.MusicService  Uri +com.minimalmusicplayer.service.MusicService  addPlaybackListener +com.minimalmusicplayer.service.MusicService  android +com.minimalmusicplayer.service.MusicService  apply +com.minimalmusicplayer.service.MusicService  binder +com.minimalmusicplayer.service.MusicService  coerceIn +com.minimalmusicplayer.service.MusicService  createNotificationChannel +com.minimalmusicplayer.service.MusicService  currentIndex +com.minimalmusicplayer.service.MusicService  currentSong +com.minimalmusicplayer.service.MusicService  	emptyList +com.minimalmusicplayer.service.MusicService  	exoPlayer +com.minimalmusicplayer.service.MusicService  getAPPLY +com.minimalmusicplayer.service.MusicService  getApply +com.minimalmusicplayer.service.MusicService  getCOERCEIn +com.minimalmusicplayer.service.MusicService  getCoerceIn +com.minimalmusicplayer.service.MusicService  getCurrentSong +com.minimalmusicplayer.service.MusicService  getEMPTYList +com.minimalmusicplayer.service.MusicService  getEmptyList +com.minimalmusicplayer.service.MusicService  
getISNotEmpty +com.minimalmusicplayer.service.MusicService  
getIsNotEmpty +com.minimalmusicplayer.service.MusicService  getLET +com.minimalmusicplayer.service.MusicService  getLet +com.minimalmusicplayer.service.MusicService  getMAP +com.minimalmusicplayer.service.MusicService  getMUTABLEListOf +com.minimalmusicplayer.service.MusicService  getMUTABLESetOf +com.minimalmusicplayer.service.MusicService  getMap +com.minimalmusicplayer.service.MusicService  getMutableListOf +com.minimalmusicplayer.service.MusicService  getMutableSetOf +com.minimalmusicplayer.service.MusicService  getSESSIONToken +com.minimalmusicplayer.service.MusicService  getSessionToken +com.minimalmusicplayer.service.MusicService  getSystemService +com.minimalmusicplayer.service.MusicService  	getTAKEIf +com.minimalmusicplayer.service.MusicService  	getTakeIf +com.minimalmusicplayer.service.MusicService  handleTrackCompletion +com.minimalmusicplayer.service.MusicService  indices +com.minimalmusicplayer.service.MusicService  initializeMediaSession +com.minimalmusicplayer.service.MusicService  initializeNotification +com.minimalmusicplayer.service.MusicService  initializePlayer +com.minimalmusicplayer.service.MusicService  invoke +com.minimalmusicplayer.service.MusicService  
isNotEmpty +com.minimalmusicplayer.service.MusicService  	isPlaying +com.minimalmusicplayer.service.MusicService  java +com.minimalmusicplayer.service.MusicService  let +com.minimalmusicplayer.service.MusicService  map +com.minimalmusicplayer.service.MusicService  mediaSession +com.minimalmusicplayer.service.MusicService  
mutableListOf +com.minimalmusicplayer.service.MusicService  mutableSetOf +com.minimalmusicplayer.service.MusicService  notificationManager +com.minimalmusicplayer.service.MusicService  notifyPlaybackStateChanged +com.minimalmusicplayer.service.MusicService  notifySongChanged +com.minimalmusicplayer.service.MusicService  pause +com.minimalmusicplayer.service.MusicService  play +com.minimalmusicplayer.service.MusicService  playbackListeners +com.minimalmusicplayer.service.MusicService  playlist +com.minimalmusicplayer.service.MusicService  preferenceManager +com.minimalmusicplayer.service.MusicService  preparePlaylist +com.minimalmusicplayer.service.MusicService  removePlaybackListener +com.minimalmusicplayer.service.MusicService  
repeatMode +com.minimalmusicplayer.service.MusicService  seekTo +com.minimalmusicplayer.service.MusicService  sessionToken +com.minimalmusicplayer.service.MusicService  setPlaylist +com.minimalmusicplayer.service.MusicService  setSessionToken +com.minimalmusicplayer.service.MusicService  
skipToNext +com.minimalmusicplayer.service.MusicService  skipToPrevious +com.minimalmusicplayer.service.MusicService  startForeground +com.minimalmusicplayer.service.MusicService  stop +com.minimalmusicplayer.service.MusicService  stopForeground +com.minimalmusicplayer.service.MusicService  stopSelf +com.minimalmusicplayer.service.MusicService  takeIf +com.minimalmusicplayer.service.MusicService  updateCurrentSong +com.minimalmusicplayer.service.MusicService  updatePlaybackState +com.minimalmusicplayer.service.MusicService  updateWidget +com.minimalmusicplayer.service.MusicService  AudioAttributes 5com.minimalmusicplayer.service.MusicService.Companion  Binder 5com.minimalmusicplayer.service.MusicService.Companion  Boolean 5com.minimalmusicplayer.service.MusicService.Companion  BrowserRoot 5com.minimalmusicplayer.service.MusicService.Companion  Build 5com.minimalmusicplayer.service.MusicService.Companion  Bundle 5com.minimalmusicplayer.service.MusicService.Companion  C 5com.minimalmusicplayer.service.MusicService.Companion  CharSequence 5com.minimalmusicplayer.service.MusicService.Companion  ErrorHandler 5com.minimalmusicplayer.service.MusicService.Companion  	ExoPlayer 5com.minimalmusicplayer.service.MusicService.Companion  IBinder 5com.minimalmusicplayer.service.MusicService.Companion  Int 5com.minimalmusicplayer.service.MusicService.Companion  Intent 5com.minimalmusicplayer.service.MusicService.Companion  List 5com.minimalmusicplayer.service.MusicService.Companion  Long 5com.minimalmusicplayer.service.MusicService.Companion  MainActivity 5com.minimalmusicplayer.service.MusicService.Companion  MediaBrowserCompat 5com.minimalmusicplayer.service.MusicService.Companion  	MediaItem 5com.minimalmusicplayer.service.MusicService.Companion  MediaSessionCompat 5com.minimalmusicplayer.service.MusicService.Companion  MusicService 5com.minimalmusicplayer.service.MusicService.Companion  MusicWidgetProvider 5com.minimalmusicplayer.service.MusicService.Companion  MutableList 5com.minimalmusicplayer.service.MusicService.Companion  NOTIFICATION_CHANNEL_ID 5com.minimalmusicplayer.service.MusicService.Companion  NOTIFICATION_ID 5com.minimalmusicplayer.service.MusicService.Companion  Notification 5com.minimalmusicplayer.service.MusicService.Companion  NotificationChannel 5com.minimalmusicplayer.service.MusicService.Companion  NotificationManager 5com.minimalmusicplayer.service.MusicService.Companion  
PendingIntent 5com.minimalmusicplayer.service.MusicService.Companion  PlaybackException 5com.minimalmusicplayer.service.MusicService.Companion  PlaybackStateCompat 5com.minimalmusicplayer.service.MusicService.Companion  Player 5com.minimalmusicplayer.service.MusicService.Companion  PlayerNotificationManager 5com.minimalmusicplayer.service.MusicService.Companion  PreferenceManager 5com.minimalmusicplayer.service.MusicService.Companion  Result 5com.minimalmusicplayer.service.MusicService.Companion  START_STICKY 5com.minimalmusicplayer.service.MusicService.Companion  Song 5com.minimalmusicplayer.service.MusicService.Companion  String 5com.minimalmusicplayer.service.MusicService.Companion  Uri 5com.minimalmusicplayer.service.MusicService.Companion  android 5com.minimalmusicplayer.service.MusicService.Companion  apply 5com.minimalmusicplayer.service.MusicService.Companion  coerceIn 5com.minimalmusicplayer.service.MusicService.Companion  currentIndex 5com.minimalmusicplayer.service.MusicService.Companion  currentSong 5com.minimalmusicplayer.service.MusicService.Companion  	emptyList 5com.minimalmusicplayer.service.MusicService.Companion  getAPPLY 5com.minimalmusicplayer.service.MusicService.Companion  getApply 5com.minimalmusicplayer.service.MusicService.Companion  getCOERCEIn 5com.minimalmusicplayer.service.MusicService.Companion  getCoerceIn 5com.minimalmusicplayer.service.MusicService.Companion  getEMPTYList 5com.minimalmusicplayer.service.MusicService.Companion  getEmptyList 5com.minimalmusicplayer.service.MusicService.Companion  
getISNotEmpty 5com.minimalmusicplayer.service.MusicService.Companion  
getIsNotEmpty 5com.minimalmusicplayer.service.MusicService.Companion  getLET 5com.minimalmusicplayer.service.MusicService.Companion  getLet 5com.minimalmusicplayer.service.MusicService.Companion  getMAP 5com.minimalmusicplayer.service.MusicService.Companion  getMUTABLEListOf 5com.minimalmusicplayer.service.MusicService.Companion  getMUTABLESetOf 5com.minimalmusicplayer.service.MusicService.Companion  getMap 5com.minimalmusicplayer.service.MusicService.Companion  getMutableListOf 5com.minimalmusicplayer.service.MusicService.Companion  getMutableSetOf 5com.minimalmusicplayer.service.MusicService.Companion  	getTAKEIf 5com.minimalmusicplayer.service.MusicService.Companion  	getTakeIf 5com.minimalmusicplayer.service.MusicService.Companion  handleTrackCompletion 5com.minimalmusicplayer.service.MusicService.Companion  indices 5com.minimalmusicplayer.service.MusicService.Companion  invoke 5com.minimalmusicplayer.service.MusicService.Companion  
isNotEmpty 5com.minimalmusicplayer.service.MusicService.Companion  java 5com.minimalmusicplayer.service.MusicService.Companion  let 5com.minimalmusicplayer.service.MusicService.Companion  map 5com.minimalmusicplayer.service.MusicService.Companion  
mutableListOf 5com.minimalmusicplayer.service.MusicService.Companion  mutableSetOf 5com.minimalmusicplayer.service.MusicService.Companion  notifyPlaybackStateChanged 5com.minimalmusicplayer.service.MusicService.Companion  notifySongChanged 5com.minimalmusicplayer.service.MusicService.Companion  pause 5com.minimalmusicplayer.service.MusicService.Companion  play 5com.minimalmusicplayer.service.MusicService.Companion  playlist 5com.minimalmusicplayer.service.MusicService.Companion  seekTo 5com.minimalmusicplayer.service.MusicService.Companion  
skipToNext 5com.minimalmusicplayer.service.MusicService.Companion  skipToPrevious 5com.minimalmusicplayer.service.MusicService.Companion  startForeground 5com.minimalmusicplayer.service.MusicService.Companion  stop 5com.minimalmusicplayer.service.MusicService.Companion  stopForeground 5com.minimalmusicplayer.service.MusicService.Companion  stopSelf 5com.minimalmusicplayer.service.MusicService.Companion  takeIf 5com.minimalmusicplayer.service.MusicService.Companion  updateCurrentSong 5com.minimalmusicplayer.service.MusicService.Companion  updatePlaybackState 5com.minimalmusicplayer.service.MusicService.Companion  MusicService 7com.minimalmusicplayer.service.MusicService.MusicBinder  
getService 7com.minimalmusicplayer.service.MusicService.MusicBinder  Boolean <com.minimalmusicplayer.service.MusicService.PlaybackListener  Long <com.minimalmusicplayer.service.MusicService.PlaybackListener  Song <com.minimalmusicplayer.service.MusicService.PlaybackListener  onPlaybackStateChanged <com.minimalmusicplayer.service.MusicService.PlaybackListener  
onSongChanged <com.minimalmusicplayer.service.MusicService.PlaybackListener  getPAUSE Ucom.minimalmusicplayer.service.MusicService.initializeMediaSession.<no name provided>  getPLAY Ucom.minimalmusicplayer.service.MusicService.initializeMediaSession.<no name provided>  getPause Ucom.minimalmusicplayer.service.MusicService.initializeMediaSession.<no name provided>  getPlay Ucom.minimalmusicplayer.service.MusicService.initializeMediaSession.<no name provided>  	getSEEKTo Ucom.minimalmusicplayer.service.MusicService.initializeMediaSession.<no name provided>  
getSKIPToNext Ucom.minimalmusicplayer.service.MusicService.initializeMediaSession.<no name provided>  getSKIPToPrevious Ucom.minimalmusicplayer.service.MusicService.initializeMediaSession.<no name provided>  getSTOP Ucom.minimalmusicplayer.service.MusicService.initializeMediaSession.<no name provided>  	getSeekTo Ucom.minimalmusicplayer.service.MusicService.initializeMediaSession.<no name provided>  
getSkipToNext Ucom.minimalmusicplayer.service.MusicService.initializeMediaSession.<no name provided>  getSkipToPrevious Ucom.minimalmusicplayer.service.MusicService.initializeMediaSession.<no name provided>  getStop Ucom.minimalmusicplayer.service.MusicService.initializeMediaSession.<no name provided>  getCURRENTSong Ucom.minimalmusicplayer.service.MusicService.initializeNotification.<no name provided>  getCurrentSong Ucom.minimalmusicplayer.service.MusicService.initializeNotification.<no name provided>  getSTARTForeground Ucom.minimalmusicplayer.service.MusicService.initializeNotification.<no name provided>  getSTOPForeground Ucom.minimalmusicplayer.service.MusicService.initializeNotification.<no name provided>  getSTOPSelf Ucom.minimalmusicplayer.service.MusicService.initializeNotification.<no name provided>  getStartForeground Ucom.minimalmusicplayer.service.MusicService.initializeNotification.<no name provided>  getStopForeground Ucom.minimalmusicplayer.service.MusicService.initializeNotification.<no name provided>  getStopSelf Ucom.minimalmusicplayer.service.MusicService.initializeNotification.<no name provided>  getCURRENTIndex Ocom.minimalmusicplayer.service.MusicService.initializePlayer.<no name provided>  getCurrentIndex Ocom.minimalmusicplayer.service.MusicService.initializePlayer.<no name provided>  getHANDLETrackCompletion Ocom.minimalmusicplayer.service.MusicService.initializePlayer.<no name provided>  getHandleTrackCompletion Ocom.minimalmusicplayer.service.MusicService.initializePlayer.<no name provided>  getNOTIFYPlaybackStateChanged Ocom.minimalmusicplayer.service.MusicService.initializePlayer.<no name provided>  getNOTIFYSongChanged Ocom.minimalmusicplayer.service.MusicService.initializePlayer.<no name provided>  getNotifyPlaybackStateChanged Ocom.minimalmusicplayer.service.MusicService.initializePlayer.<no name provided>  getNotifySongChanged Ocom.minimalmusicplayer.service.MusicService.initializePlayer.<no name provided>  getPAUSE Ocom.minimalmusicplayer.service.MusicService.initializePlayer.<no name provided>  getPLAYLIST Ocom.minimalmusicplayer.service.MusicService.initializePlayer.<no name provided>  getPause Ocom.minimalmusicplayer.service.MusicService.initializePlayer.<no name provided>  getPlaylist Ocom.minimalmusicplayer.service.MusicService.initializePlayer.<no name provided>  
getSKIPToNext Ocom.minimalmusicplayer.service.MusicService.initializePlayer.<no name provided>  
getSkipToNext Ocom.minimalmusicplayer.service.MusicService.initializePlayer.<no name provided>  getUPDATECurrentSong Ocom.minimalmusicplayer.service.MusicService.initializePlayer.<no name provided>  getUPDATEPlaybackState Ocom.minimalmusicplayer.service.MusicService.initializePlayer.<no name provided>  getUpdateCurrentSong Ocom.minimalmusicplayer.service.MusicService.initializePlayer.<no name provided>  getUpdatePlaybackState Ocom.minimalmusicplayer.service.MusicService.initializePlayer.<no name provided>  ActivityCompat com.minimalmusicplayer.util  Array com.minimalmusicplayer.util  Boolean com.minimalmusicplayer.util  Build com.minimalmusicplayer.util  Context com.minimalmusicplayer.util  
ContextCompat com.minimalmusicplayer.util  Dispatchers com.minimalmusicplayer.util  DocumentFile com.minimalmusicplayer.util  ErrorHandler com.minimalmusicplayer.util  	Exception com.minimalmusicplayer.util  File com.minimalmusicplayer.util  FileScanner com.minimalmusicplayer.util  Int com.minimalmusicplayer.util  IntArray com.minimalmusicplayer.util  KEY_LAST_PLAYED_SONG com.minimalmusicplayer.util  KEY_LAST_POSITION com.minimalmusicplayer.util  KEY_REPEAT_MODE com.minimalmusicplayer.util  KEY_SELECTED_DIRECTORIES com.minimalmusicplayer.util  KEY_SHUFFLE_MODE com.minimalmusicplayer.util  List com.minimalmusicplayer.util  Log com.minimalmusicplayer.util  Long com.minimalmusicplayer.util  Manifest com.minimalmusicplayer.util  MediaMetadataRetriever com.minimalmusicplayer.util  
MediaStore com.minimalmusicplayer.util  MutableList com.minimalmusicplayer.util  
PREFS_NAME com.minimalmusicplayer.util  PackageManager com.minimalmusicplayer.util  PermissionHelper com.minimalmusicplayer.util  PreferenceManager com.minimalmusicplayer.util  SecurityException com.minimalmusicplayer.util  Set com.minimalmusicplayer.util  Song com.minimalmusicplayer.util  String com.minimalmusicplayer.util  	Throwable com.minimalmusicplayer.util  Toast com.minimalmusicplayer.util  all com.minimalmusicplayer.util  any com.minimalmusicplayer.util  arrayOf com.minimalmusicplayer.util  contains com.minimalmusicplayer.util  context com.minimalmusicplayer.util  	emptyList com.minimalmusicplayer.util  emptySet com.minimalmusicplayer.util  forEach com.minimalmusicplayer.util  isAudioFile com.minimalmusicplayer.util  
isNotEmpty com.minimalmusicplayer.util  let com.minimalmusicplayer.util  	lowercase com.minimalmusicplayer.util  
mutableListOf com.minimalmusicplayer.util  scanDirectoryRecursive com.minimalmusicplayer.util  setOf com.minimalmusicplayer.util  substringAfterLast com.minimalmusicplayer.util  substringBeforeLast com.minimalmusicplayer.util  toLongOrNull com.minimalmusicplayer.util  use com.minimalmusicplayer.util  withContext com.minimalmusicplayer.util  Context (com.minimalmusicplayer.util.ErrorHandler  Log (com.minimalmusicplayer.util.ErrorHandler  String (com.minimalmusicplayer.util.ErrorHandler  TAG (com.minimalmusicplayer.util.ErrorHandler  	Throwable (com.minimalmusicplayer.util.ErrorHandler  Toast (com.minimalmusicplayer.util.ErrorHandler  contains (com.minimalmusicplayer.util.ErrorHandler  getCONTAINS (com.minimalmusicplayer.util.ErrorHandler  getContains (com.minimalmusicplayer.util.ErrorHandler  handlePlaybackError (com.minimalmusicplayer.util.ErrorHandler  handleScanError (com.minimalmusicplayer.util.ErrorHandler  handleServiceError (com.minimalmusicplayer.util.ErrorHandler  handleWidgetError (com.minimalmusicplayer.util.ErrorHandler  logError (com.minimalmusicplayer.util.ErrorHandler  logInfo (com.minimalmusicplayer.util.ErrorHandler  
logWarning (com.minimalmusicplayer.util.ErrorHandler  Boolean 'com.minimalmusicplayer.util.FileScanner  Context 'com.minimalmusicplayer.util.FileScanner  Dispatchers 'com.minimalmusicplayer.util.FileScanner  DocumentFile 'com.minimalmusicplayer.util.FileScanner  ErrorHandler 'com.minimalmusicplayer.util.FileScanner  	Exception 'com.minimalmusicplayer.util.FileScanner  File 'com.minimalmusicplayer.util.FileScanner  List 'com.minimalmusicplayer.util.FileScanner  MediaMetadataRetriever 'com.minimalmusicplayer.util.FileScanner  
MediaStore 'com.minimalmusicplayer.util.FileScanner  MutableList 'com.minimalmusicplayer.util.FileScanner  SecurityException 'com.minimalmusicplayer.util.FileScanner  Song 'com.minimalmusicplayer.util.FileScanner  String 'com.minimalmusicplayer.util.FileScanner  Uri 'com.minimalmusicplayer.util.FileScanner  arrayOf 'com.minimalmusicplayer.util.FileScanner  context 'com.minimalmusicplayer.util.FileScanner  	emptyList 'com.minimalmusicplayer.util.FileScanner  extractSongInfo 'com.minimalmusicplayer.util.FileScanner  forEach 'com.minimalmusicplayer.util.FileScanner  
getARRAYOf 'com.minimalmusicplayer.util.FileScanner  
getArrayOf 'com.minimalmusicplayer.util.FileScanner  getEMPTYList 'com.minimalmusicplayer.util.FileScanner  getEmptyList 'com.minimalmusicplayer.util.FileScanner  
getFOREach 'com.minimalmusicplayer.util.FileScanner  
getForEach 'com.minimalmusicplayer.util.FileScanner  getLET 'com.minimalmusicplayer.util.FileScanner  getLOWERCASE 'com.minimalmusicplayer.util.FileScanner  getLet 'com.minimalmusicplayer.util.FileScanner  getLowercase 'com.minimalmusicplayer.util.FileScanner  getMUTABLEListOf 'com.minimalmusicplayer.util.FileScanner  getMutableListOf 'com.minimalmusicplayer.util.FileScanner  getSETOf 'com.minimalmusicplayer.util.FileScanner  getSUBSTRINGAfterLast 'com.minimalmusicplayer.util.FileScanner  getSUBSTRINGBeforeLast 'com.minimalmusicplayer.util.FileScanner  getSetOf 'com.minimalmusicplayer.util.FileScanner  getSubstringAfterLast 'com.minimalmusicplayer.util.FileScanner  getSubstringBeforeLast 'com.minimalmusicplayer.util.FileScanner  getTOLongOrNull 'com.minimalmusicplayer.util.FileScanner  getToLongOrNull 'com.minimalmusicplayer.util.FileScanner  getUSE 'com.minimalmusicplayer.util.FileScanner  getUse 'com.minimalmusicplayer.util.FileScanner  getWITHContext 'com.minimalmusicplayer.util.FileScanner  getWithContext 'com.minimalmusicplayer.util.FileScanner  isAudioFile 'com.minimalmusicplayer.util.FileScanner  let 'com.minimalmusicplayer.util.FileScanner  	lowercase 'com.minimalmusicplayer.util.FileScanner  
mutableListOf 'com.minimalmusicplayer.util.FileScanner  
scanDirectory 'com.minimalmusicplayer.util.FileScanner  scanDirectoryRecursive 'com.minimalmusicplayer.util.FileScanner  scanMediaStore 'com.minimalmusicplayer.util.FileScanner  setOf 'com.minimalmusicplayer.util.FileScanner  substringAfterLast 'com.minimalmusicplayer.util.FileScanner  substringBeforeLast 'com.minimalmusicplayer.util.FileScanner  supportedFormats 'com.minimalmusicplayer.util.FileScanner  toLongOrNull 'com.minimalmusicplayer.util.FileScanner  use 'com.minimalmusicplayer.util.FileScanner  withContext 'com.minimalmusicplayer.util.FileScanner  Activity ,com.minimalmusicplayer.util.PermissionHelper  ActivityCompat ,com.minimalmusicplayer.util.PermissionHelper  Array ,com.minimalmusicplayer.util.PermissionHelper  Boolean ,com.minimalmusicplayer.util.PermissionHelper  Build ,com.minimalmusicplayer.util.PermissionHelper  Context ,com.minimalmusicplayer.util.PermissionHelper  
ContextCompat ,com.minimalmusicplayer.util.PermissionHelper  IntArray ,com.minimalmusicplayer.util.PermissionHelper  Manifest ,com.minimalmusicplayer.util.PermissionHelper  PackageManager ,com.minimalmusicplayer.util.PermissionHelper  STORAGE_PERMISSION_REQUEST_CODE ,com.minimalmusicplayer.util.PermissionHelper  String ,com.minimalmusicplayer.util.PermissionHelper  all ,com.minimalmusicplayer.util.PermissionHelper  any ,com.minimalmusicplayer.util.PermissionHelper  arrayOf ,com.minimalmusicplayer.util.PermissionHelper  getALL ,com.minimalmusicplayer.util.PermissionHelper  getANY ,com.minimalmusicplayer.util.PermissionHelper  
getARRAYOf ,com.minimalmusicplayer.util.PermissionHelper  getAll ,com.minimalmusicplayer.util.PermissionHelper  getAny ,com.minimalmusicplayer.util.PermissionHelper  
getArrayOf ,com.minimalmusicplayer.util.PermissionHelper  
getISNotEmpty ,com.minimalmusicplayer.util.PermissionHelper  
getIsNotEmpty ,com.minimalmusicplayer.util.PermissionHelper  getRequiredPermissions ,com.minimalmusicplayer.util.PermissionHelper  hasStoragePermission ,com.minimalmusicplayer.util.PermissionHelper  
isNotEmpty ,com.minimalmusicplayer.util.PermissionHelper  isPermissionGranted ,com.minimalmusicplayer.util.PermissionHelper  requestStoragePermission ,com.minimalmusicplayer.util.PermissionHelper  shouldShowRationale ,com.minimalmusicplayer.util.PermissionHelper  Boolean -com.minimalmusicplayer.util.PreferenceManager  Context -com.minimalmusicplayer.util.PreferenceManager  Int -com.minimalmusicplayer.util.PreferenceManager  KEY_LAST_PLAYED_SONG -com.minimalmusicplayer.util.PreferenceManager  KEY_LAST_POSITION -com.minimalmusicplayer.util.PreferenceManager  KEY_REPEAT_MODE -com.minimalmusicplayer.util.PreferenceManager  KEY_SELECTED_DIRECTORIES -com.minimalmusicplayer.util.PreferenceManager  KEY_SHUFFLE_MODE -com.minimalmusicplayer.util.PreferenceManager  Long -com.minimalmusicplayer.util.PreferenceManager  
PREFS_NAME -com.minimalmusicplayer.util.PreferenceManager  Set -com.minimalmusicplayer.util.PreferenceManager  SharedPreferences -com.minimalmusicplayer.util.PreferenceManager  String -com.minimalmusicplayer.util.PreferenceManager  emptySet -com.minimalmusicplayer.util.PreferenceManager  getEMPTYSet -com.minimalmusicplayer.util.PreferenceManager  getEmptySet -com.minimalmusicplayer.util.PreferenceManager  getSelectedDirectories -com.minimalmusicplayer.util.PreferenceManager  prefs -com.minimalmusicplayer.util.PreferenceManager  saveLastPlayedSong -com.minimalmusicplayer.util.PreferenceManager  saveSelectedDirectories -com.minimalmusicplayer.util.PreferenceManager  Boolean 7com.minimalmusicplayer.util.PreferenceManager.Companion  Context 7com.minimalmusicplayer.util.PreferenceManager.Companion  Int 7com.minimalmusicplayer.util.PreferenceManager.Companion  KEY_LAST_PLAYED_SONG 7com.minimalmusicplayer.util.PreferenceManager.Companion  KEY_LAST_POSITION 7com.minimalmusicplayer.util.PreferenceManager.Companion  KEY_REPEAT_MODE 7com.minimalmusicplayer.util.PreferenceManager.Companion  KEY_SELECTED_DIRECTORIES 7com.minimalmusicplayer.util.PreferenceManager.Companion  KEY_SHUFFLE_MODE 7com.minimalmusicplayer.util.PreferenceManager.Companion  Long 7com.minimalmusicplayer.util.PreferenceManager.Companion  
PREFS_NAME 7com.minimalmusicplayer.util.PreferenceManager.Companion  Set 7com.minimalmusicplayer.util.PreferenceManager.Companion  SharedPreferences 7com.minimalmusicplayer.util.PreferenceManager.Companion  String 7com.minimalmusicplayer.util.PreferenceManager.Companion  emptySet 7com.minimalmusicplayer.util.PreferenceManager.Companion  getEMPTYSet 7com.minimalmusicplayer.util.PreferenceManager.Companion  getEmptySet 7com.minimalmusicplayer.util.PreferenceManager.Companion  invoke 7com.minimalmusicplayer.util.PreferenceManager.Companion  ACTION_NEXT com.minimalmusicplayer.widget  ACTION_PLAY_PAUSE com.minimalmusicplayer.widget  ACTION_PREVIOUS com.minimalmusicplayer.widget  AppWidgetManager com.minimalmusicplayer.widget  Boolean com.minimalmusicplayer.widget  
ComponentName com.minimalmusicplayer.widget  ErrorHandler com.minimalmusicplayer.widget  	Exception com.minimalmusicplayer.widget  Int com.minimalmusicplayer.widget  IntArray com.minimalmusicplayer.widget  Intent com.minimalmusicplayer.widget  MainActivity com.minimalmusicplayer.widget  MusicService com.minimalmusicplayer.widget  MusicWidgetProvider com.minimalmusicplayer.widget  
PendingIntent com.minimalmusicplayer.widget  R com.minimalmusicplayer.widget  RemoteViews com.minimalmusicplayer.widget  String com.minimalmusicplayer.widget  apply com.minimalmusicplayer.widget  java com.minimalmusicplayer.widget  updateAppWidget com.minimalmusicplayer.widget  ACTION_NEXT 1com.minimalmusicplayer.widget.MusicWidgetProvider  ACTION_PLAY_PAUSE 1com.minimalmusicplayer.widget.MusicWidgetProvider  ACTION_PREVIOUS 1com.minimalmusicplayer.widget.MusicWidgetProvider  AppWidgetManager 1com.minimalmusicplayer.widget.MusicWidgetProvider  Boolean 1com.minimalmusicplayer.widget.MusicWidgetProvider  	Companion 1com.minimalmusicplayer.widget.MusicWidgetProvider  
ComponentName 1com.minimalmusicplayer.widget.MusicWidgetProvider  Context 1com.minimalmusicplayer.widget.MusicWidgetProvider  ErrorHandler 1com.minimalmusicplayer.widget.MusicWidgetProvider  	Exception 1com.minimalmusicplayer.widget.MusicWidgetProvider  Int 1com.minimalmusicplayer.widget.MusicWidgetProvider  IntArray 1com.minimalmusicplayer.widget.MusicWidgetProvider  Intent 1com.minimalmusicplayer.widget.MusicWidgetProvider  MainActivity 1com.minimalmusicplayer.widget.MusicWidgetProvider  MusicService 1com.minimalmusicplayer.widget.MusicWidgetProvider  MusicWidgetProvider 1com.minimalmusicplayer.widget.MusicWidgetProvider  
PendingIntent 1com.minimalmusicplayer.widget.MusicWidgetProvider  R 1com.minimalmusicplayer.widget.MusicWidgetProvider  RemoteViews 1com.minimalmusicplayer.widget.MusicWidgetProvider  String 1com.minimalmusicplayer.widget.MusicWidgetProvider  apply 1com.minimalmusicplayer.widget.MusicWidgetProvider  getAPPLY 1com.minimalmusicplayer.widget.MusicWidgetProvider  getApply 1com.minimalmusicplayer.widget.MusicWidgetProvider  getUPDATEAppWidget 1com.minimalmusicplayer.widget.MusicWidgetProvider  getUpdateAppWidget 1com.minimalmusicplayer.widget.MusicWidgetProvider  java 1com.minimalmusicplayer.widget.MusicWidgetProvider  sendMusicServiceCommand 1com.minimalmusicplayer.widget.MusicWidgetProvider  updateAppWidget 1com.minimalmusicplayer.widget.MusicWidgetProvider  updateWidget 1com.minimalmusicplayer.widget.MusicWidgetProvider  ACTION_NEXT ;com.minimalmusicplayer.widget.MusicWidgetProvider.Companion  ACTION_PLAY_PAUSE ;com.minimalmusicplayer.widget.MusicWidgetProvider.Companion  ACTION_PREVIOUS ;com.minimalmusicplayer.widget.MusicWidgetProvider.Companion  AppWidgetManager ;com.minimalmusicplayer.widget.MusicWidgetProvider.Companion  Boolean ;com.minimalmusicplayer.widget.MusicWidgetProvider.Companion  
ComponentName ;com.minimalmusicplayer.widget.MusicWidgetProvider.Companion  Context ;com.minimalmusicplayer.widget.MusicWidgetProvider.Companion  ErrorHandler ;com.minimalmusicplayer.widget.MusicWidgetProvider.Companion  	Exception ;com.minimalmusicplayer.widget.MusicWidgetProvider.Companion  Int ;com.minimalmusicplayer.widget.MusicWidgetProvider.Companion  IntArray ;com.minimalmusicplayer.widget.MusicWidgetProvider.Companion  Intent ;com.minimalmusicplayer.widget.MusicWidgetProvider.Companion  MainActivity ;com.minimalmusicplayer.widget.MusicWidgetProvider.Companion  MusicService ;com.minimalmusicplayer.widget.MusicWidgetProvider.Companion  MusicWidgetProvider ;com.minimalmusicplayer.widget.MusicWidgetProvider.Companion  
PendingIntent ;com.minimalmusicplayer.widget.MusicWidgetProvider.Companion  R ;com.minimalmusicplayer.widget.MusicWidgetProvider.Companion  RemoteViews ;com.minimalmusicplayer.widget.MusicWidgetProvider.Companion  String ;com.minimalmusicplayer.widget.MusicWidgetProvider.Companion  apply ;com.minimalmusicplayer.widget.MusicWidgetProvider.Companion  getAPPLY ;com.minimalmusicplayer.widget.MusicWidgetProvider.Companion  getApply ;com.minimalmusicplayer.widget.MusicWidgetProvider.Companion  java ;com.minimalmusicplayer.widget.MusicWidgetProvider.Companion  setupClickIntents ;com.minimalmusicplayer.widget.MusicWidgetProvider.Companion  updateAppWidget ;com.minimalmusicplayer.widget.MusicWidgetProvider.Companion  updateWidget ;com.minimalmusicplayer.widget.MusicWidgetProvider.Companion  File java.io  exists java.io.File  ACTION_NEXT 	java.lang  ACTION_PLAY_PAUSE 	java.lang  ACTION_PREVIOUS 	java.lang  Activity 	java.lang  ActivityCompat 	java.lang  !ActivityDirectorySelectionBinding 	java.lang  ActivityMainBinding 	java.lang  ActivityResultContracts 	java.lang  AppWidgetManager 	java.lang  AudioAttributes 	java.lang  BrowserRoot 	java.lang  Build 	java.lang  C 	java.lang  Class 	java.lang  
ComponentName 	java.lang  Context 	java.lang  
ContextCompat 	java.lang  DirectoryAdapter 	java.lang  DirectorySelectionActivity 	java.lang  Dispatchers 	java.lang  DocumentFile 	java.lang  DocumentsContract 	java.lang  ErrorHandler 	java.lang  	Exception 	java.lang  	ExoPlayer 	java.lang  File 	java.lang  FileScanner 	java.lang  Intent 	java.lang  ItemDirectoryBinding 	java.lang  ItemSongBinding 	java.lang  KEY_LAST_PLAYED_SONG 	java.lang  KEY_LAST_POSITION 	java.lang  KEY_REPEAT_MODE 	java.lang  KEY_SELECTED_DIRECTORIES 	java.lang  KEY_SHUFFLE_MODE 	java.lang  LayoutInflater 	java.lang  LinearLayoutManager 	java.lang  Log 	java.lang  MainActivity 	java.lang  Manifest 	java.lang  	MediaItem 	java.lang  MediaMetadataRetriever 	java.lang  MediaSessionCompat 	java.lang  
MediaStore 	java.lang  MusicService 	java.lang  MusicWidgetProvider 	java.lang  NOTIFICATION_CHANNEL_ID 	java.lang  NOTIFICATION_ID 	java.lang  Notification 	java.lang  NotificationChannel 	java.lang  NotificationManager 	java.lang  
PREFS_NAME 	java.lang  PackageManager 	java.lang  
PendingIntent 	java.lang  PermissionHelper 	java.lang  PlaybackStateCompat 	java.lang  Player 	java.lang  PlayerNotificationManager 	java.lang  PreferenceManager 	java.lang  R 	java.lang  	RESULT_OK 	java.lang  RemoteViews 	java.lang  START_STICKY 	java.lang  SecurityException 	java.lang  Song 	java.lang  SongAdapter 	java.lang  String 	java.lang  Toast 	java.lang  Uri 	java.lang  View 	java.lang  all 	java.lang  android 	java.lang  any 	java.lang  apply 	java.lang  arrayOf 	java.lang  binding 	java.lang  coerceIn 	java.lang  contains 	java.lang  context 	java.lang  currentIndex 	java.lang  currentSong 	java.lang  directoryAdapter 	java.lang  	emptyList 	java.lang  emptySet 	java.lang  fileScanner 	java.lang  finish 	java.lang  forEach 	java.lang  format 	java.lang  handleTrackCompletion 	java.lang  ifEmpty 	java.lang  indexOfFirst 	java.lang  indices 	java.lang  isAudioFile 	java.lang  
isNotBlank 	java.lang  
isNotEmpty 	java.lang  isServiceBound 	java.lang  java 	java.lang  launch 	java.lang  let 	java.lang  listOf 	java.lang  	lowercase 	java.lang  map 	java.lang  musicService 	java.lang  
mutableListOf 	java.lang  mutableSetOf 	java.lang  notifyPlaybackStateChanged 	java.lang  notifySongChanged 	java.lang  
onRemoveClick 	java.lang  onSongClick 	java.lang  pause 	java.lang  play 	java.lang  playlist 	java.lang  
plusAssign 	java.lang  preferenceManager 	java.lang  scanDirectoryRecursive 	java.lang  seekTo 	java.lang  setOf 	java.lang  	setResult 	java.lang  showEmptyState 	java.lang  
skipToNext 	java.lang  skipToPrevious 	java.lang  songAdapter 	java.lang  songs 	java.lang  sortedBy 	java.lang  split 	java.lang  startForeground 	java.lang  startMusicService 	java.lang  stop 	java.lang  stopForeground 	java.lang  stopSelf 	java.lang  substringAfterLast 	java.lang  substringBeforeLast 	java.lang  takeIf 	java.lang  toLongOrNull 	java.lang  toMutableSet 	java.lang  updateAppWidget 	java.lang  updateCurrentSong 	java.lang  updatePlaybackState 	java.lang  updatePlaybackUI 	java.lang  use 	java.lang  withContext 	java.lang  message java.lang.Exception  printStackTrace java.lang.Exception  <SAM-CONSTRUCTOR> java.lang.Runnable  ACTION_NEXT kotlin  ACTION_PLAY_PAUSE kotlin  ACTION_PREVIOUS kotlin  Activity kotlin  ActivityCompat kotlin  !ActivityDirectorySelectionBinding kotlin  ActivityMainBinding kotlin  ActivityResultContracts kotlin  Any kotlin  AppWidgetManager kotlin  Array kotlin  AudioAttributes kotlin  Boolean kotlin  BrowserRoot kotlin  Build kotlin  C kotlin  Char kotlin  CharSequence kotlin  
ComponentName kotlin  Context kotlin  
ContextCompat kotlin  DirectoryAdapter kotlin  DirectorySelectionActivity kotlin  Dispatchers kotlin  DocumentFile kotlin  DocumentsContract kotlin  ErrorHandler kotlin  	Exception kotlin  	ExoPlayer kotlin  File kotlin  FileScanner kotlin  Float kotlin  	Function0 kotlin  	Function1 kotlin  	Function2 kotlin  Int kotlin  IntArray kotlin  Intent kotlin  ItemDirectoryBinding kotlin  ItemSongBinding kotlin  KEY_LAST_PLAYED_SONG kotlin  KEY_LAST_POSITION kotlin  KEY_REPEAT_MODE kotlin  KEY_SELECTED_DIRECTORIES kotlin  KEY_SHUFFLE_MODE kotlin  LayoutInflater kotlin  LinearLayoutManager kotlin  Log kotlin  Long kotlin  MainActivity kotlin  Manifest kotlin  	MediaItem kotlin  MediaMetadataRetriever kotlin  MediaSessionCompat kotlin  
MediaStore kotlin  MusicService kotlin  MusicWidgetProvider kotlin  NOTIFICATION_CHANNEL_ID kotlin  NOTIFICATION_ID kotlin  Nothing kotlin  Notification kotlin  NotificationChannel kotlin  NotificationManager kotlin  
PREFS_NAME kotlin  PackageManager kotlin  
PendingIntent kotlin  PermissionHelper kotlin  PlaybackStateCompat kotlin  Player kotlin  PlayerNotificationManager kotlin  PreferenceManager kotlin  R kotlin  	RESULT_OK kotlin  RemoteViews kotlin  START_STICKY kotlin  SecurityException kotlin  Song kotlin  SongAdapter kotlin  String kotlin  	Throwable kotlin  Toast kotlin  Unit kotlin  Uri kotlin  View kotlin  all kotlin  android kotlin  any kotlin  apply kotlin  arrayOf kotlin  binding kotlin  coerceIn kotlin  contains kotlin  context kotlin  currentIndex kotlin  currentSong kotlin  directoryAdapter kotlin  	emptyList kotlin  emptySet kotlin  fileScanner kotlin  finish kotlin  forEach kotlin  format kotlin  handleTrackCompletion kotlin  ifEmpty kotlin  indexOfFirst kotlin  indices kotlin  isAudioFile kotlin  
isNotBlank kotlin  
isNotEmpty kotlin  isServiceBound kotlin  java kotlin  launch kotlin  let kotlin  listOf kotlin  	lowercase kotlin  map kotlin  musicService kotlin  
mutableListOf kotlin  mutableSetOf kotlin  notifyPlaybackStateChanged kotlin  notifySongChanged kotlin  
onRemoveClick kotlin  onSongClick kotlin  pause kotlin  play kotlin  playlist kotlin  
plusAssign kotlin  preferenceManager kotlin  scanDirectoryRecursive kotlin  seekTo kotlin  setOf kotlin  	setResult kotlin  showEmptyState kotlin  
skipToNext kotlin  skipToPrevious kotlin  songAdapter kotlin  songs kotlin  sortedBy kotlin  split kotlin  startForeground kotlin  startMusicService kotlin  stop kotlin  stopForeground kotlin  stopSelf kotlin  substringAfterLast kotlin  substringBeforeLast kotlin  takeIf kotlin  toLongOrNull kotlin  toMutableSet kotlin  updateAppWidget kotlin  updateCurrentSong kotlin  updatePlaybackState kotlin  updatePlaybackUI kotlin  use kotlin  withContext kotlin  getALL kotlin.Array  getANY kotlin.Array  getAll kotlin.Array  getAny kotlin.Array  
getFOREach kotlin.Array  
getForEach kotlin.Array  getCOERCEIn 
kotlin.Int  getCoerceIn 
kotlin.Int  
getPLUSAssign 
kotlin.Int  
getPlusAssign 
kotlin.Int  getALL kotlin.IntArray  getAll kotlin.IntArray  
getISNotEmpty kotlin.IntArray  
getIsNotEmpty kotlin.IntArray  
isNotEmpty kotlin.IntArray  	getTAKEIf kotlin.Long  	getTakeIf kotlin.Long  getCONTAINS 
kotlin.String  getContains 
kotlin.String  
getIFEmpty 
kotlin.String  
getISNotBlank 
kotlin.String  
getIfEmpty 
kotlin.String  
getIsNotBlank 
kotlin.String  getLET 
kotlin.String  getLOWERCASE 
kotlin.String  getLet 
kotlin.String  getLowercase 
kotlin.String  getSPLIT 
kotlin.String  getSUBSTRINGAfterLast 
kotlin.String  getSUBSTRINGBeforeLast 
kotlin.String  getSplit 
kotlin.String  getSubstringAfterLast 
kotlin.String  getSubstringBeforeLast 
kotlin.String  getTOLongOrNull 
kotlin.String  getToLongOrNull 
kotlin.String  
isNotBlank 
kotlin.String  	getFORMAT kotlin.String.Companion  	getFormat kotlin.String.Companion  ACTION_NEXT kotlin.annotation  ACTION_PLAY_PAUSE kotlin.annotation  ACTION_PREVIOUS kotlin.annotation  Activity kotlin.annotation  ActivityCompat kotlin.annotation  !ActivityDirectorySelectionBinding kotlin.annotation  ActivityMainBinding kotlin.annotation  ActivityResultContracts kotlin.annotation  AppWidgetManager kotlin.annotation  AudioAttributes kotlin.annotation  BrowserRoot kotlin.annotation  Build kotlin.annotation  C kotlin.annotation  
ComponentName kotlin.annotation  Context kotlin.annotation  
ContextCompat kotlin.annotation  DirectoryAdapter kotlin.annotation  DirectorySelectionActivity kotlin.annotation  Dispatchers kotlin.annotation  DocumentFile kotlin.annotation  DocumentsContract kotlin.annotation  ErrorHandler kotlin.annotation  	Exception kotlin.annotation  	ExoPlayer kotlin.annotation  File kotlin.annotation  FileScanner kotlin.annotation  Intent kotlin.annotation  ItemDirectoryBinding kotlin.annotation  ItemSongBinding kotlin.annotation  KEY_LAST_PLAYED_SONG kotlin.annotation  KEY_LAST_POSITION kotlin.annotation  KEY_REPEAT_MODE kotlin.annotation  KEY_SELECTED_DIRECTORIES kotlin.annotation  KEY_SHUFFLE_MODE kotlin.annotation  LayoutInflater kotlin.annotation  LinearLayoutManager kotlin.annotation  Log kotlin.annotation  MainActivity kotlin.annotation  Manifest kotlin.annotation  	MediaItem kotlin.annotation  MediaMetadataRetriever kotlin.annotation  MediaSessionCompat kotlin.annotation  
MediaStore kotlin.annotation  MusicService kotlin.annotation  MusicWidgetProvider kotlin.annotation  NOTIFICATION_CHANNEL_ID kotlin.annotation  NOTIFICATION_ID kotlin.annotation  Notification kotlin.annotation  NotificationChannel kotlin.annotation  NotificationManager kotlin.annotation  
PREFS_NAME kotlin.annotation  PackageManager kotlin.annotation  
PendingIntent kotlin.annotation  PermissionHelper kotlin.annotation  PlaybackStateCompat kotlin.annotation  Player kotlin.annotation  PlayerNotificationManager kotlin.annotation  PreferenceManager kotlin.annotation  R kotlin.annotation  	RESULT_OK kotlin.annotation  RemoteViews kotlin.annotation  START_STICKY kotlin.annotation  SecurityException kotlin.annotation  Song kotlin.annotation  SongAdapter kotlin.annotation  String kotlin.annotation  Toast kotlin.annotation  Uri kotlin.annotation  View kotlin.annotation  all kotlin.annotation  android kotlin.annotation  any kotlin.annotation  apply kotlin.annotation  arrayOf kotlin.annotation  binding kotlin.annotation  coerceIn kotlin.annotation  contains kotlin.annotation  context kotlin.annotation  currentIndex kotlin.annotation  currentSong kotlin.annotation  directoryAdapter kotlin.annotation  	emptyList kotlin.annotation  emptySet kotlin.annotation  fileScanner kotlin.annotation  finish kotlin.annotation  forEach kotlin.annotation  format kotlin.annotation  handleTrackCompletion kotlin.annotation  ifEmpty kotlin.annotation  indexOfFirst kotlin.annotation  indices kotlin.annotation  isAudioFile kotlin.annotation  
isNotBlank kotlin.annotation  
isNotEmpty kotlin.annotation  isServiceBound kotlin.annotation  java kotlin.annotation  launch kotlin.annotation  let kotlin.annotation  listOf kotlin.annotation  	lowercase kotlin.annotation  map kotlin.annotation  musicService kotlin.annotation  
mutableListOf kotlin.annotation  mutableSetOf kotlin.annotation  notifyPlaybackStateChanged kotlin.annotation  notifySongChanged kotlin.annotation  
onRemoveClick kotlin.annotation  onSongClick kotlin.annotation  pause kotlin.annotation  play kotlin.annotation  playlist kotlin.annotation  
plusAssign kotlin.annotation  preferenceManager kotlin.annotation  scanDirectoryRecursive kotlin.annotation  seekTo kotlin.annotation  setOf kotlin.annotation  	setResult kotlin.annotation  showEmptyState kotlin.annotation  
skipToNext kotlin.annotation  skipToPrevious kotlin.annotation  songAdapter kotlin.annotation  songs kotlin.annotation  sortedBy kotlin.annotation  split kotlin.annotation  startForeground kotlin.annotation  startMusicService kotlin.annotation  stop kotlin.annotation  stopForeground kotlin.annotation  stopSelf kotlin.annotation  substringAfterLast kotlin.annotation  substringBeforeLast kotlin.annotation  takeIf kotlin.annotation  toLongOrNull kotlin.annotation  toMutableSet kotlin.annotation  updateAppWidget kotlin.annotation  updateCurrentSong kotlin.annotation  updatePlaybackState kotlin.annotation  updatePlaybackUI kotlin.annotation  use kotlin.annotation  withContext kotlin.annotation  ACTION_NEXT kotlin.collections  ACTION_PLAY_PAUSE kotlin.collections  ACTION_PREVIOUS kotlin.collections  Activity kotlin.collections  ActivityCompat kotlin.collections  !ActivityDirectorySelectionBinding kotlin.collections  ActivityMainBinding kotlin.collections  ActivityResultContracts kotlin.collections  AppWidgetManager kotlin.collections  AudioAttributes kotlin.collections  BrowserRoot kotlin.collections  Build kotlin.collections  C kotlin.collections  
ComponentName kotlin.collections  Context kotlin.collections  
ContextCompat kotlin.collections  DirectoryAdapter kotlin.collections  DirectorySelectionActivity kotlin.collections  Dispatchers kotlin.collections  DocumentFile kotlin.collections  DocumentsContract kotlin.collections  ErrorHandler kotlin.collections  	Exception kotlin.collections  	ExoPlayer kotlin.collections  File kotlin.collections  FileScanner kotlin.collections  Intent kotlin.collections  ItemDirectoryBinding kotlin.collections  ItemSongBinding kotlin.collections  KEY_LAST_PLAYED_SONG kotlin.collections  KEY_LAST_POSITION kotlin.collections  KEY_REPEAT_MODE kotlin.collections  KEY_SELECTED_DIRECTORIES kotlin.collections  KEY_SHUFFLE_MODE kotlin.collections  LayoutInflater kotlin.collections  LinearLayoutManager kotlin.collections  List kotlin.collections  Log kotlin.collections  MainActivity kotlin.collections  Manifest kotlin.collections  	MediaItem kotlin.collections  MediaMetadataRetriever kotlin.collections  MediaSessionCompat kotlin.collections  
MediaStore kotlin.collections  MusicService kotlin.collections  MusicWidgetProvider kotlin.collections  MutableList kotlin.collections  
MutableSet kotlin.collections  NOTIFICATION_CHANNEL_ID kotlin.collections  NOTIFICATION_ID kotlin.collections  Notification kotlin.collections  NotificationChannel kotlin.collections  NotificationManager kotlin.collections  
PREFS_NAME kotlin.collections  PackageManager kotlin.collections  
PendingIntent kotlin.collections  PermissionHelper kotlin.collections  PlaybackStateCompat kotlin.collections  Player kotlin.collections  PlayerNotificationManager kotlin.collections  PreferenceManager kotlin.collections  R kotlin.collections  	RESULT_OK kotlin.collections  RemoteViews kotlin.collections  START_STICKY kotlin.collections  SecurityException kotlin.collections  Set kotlin.collections  Song kotlin.collections  SongAdapter kotlin.collections  String kotlin.collections  Toast kotlin.collections  Uri kotlin.collections  View kotlin.collections  all kotlin.collections  android kotlin.collections  any kotlin.collections  apply kotlin.collections  arrayOf kotlin.collections  binding kotlin.collections  coerceIn kotlin.collections  contains kotlin.collections  context kotlin.collections  currentIndex kotlin.collections  currentSong kotlin.collections  directoryAdapter kotlin.collections  	emptyList kotlin.collections  emptySet kotlin.collections  fileScanner kotlin.collections  finish kotlin.collections  forEach kotlin.collections  format kotlin.collections  handleTrackCompletion kotlin.collections  ifEmpty kotlin.collections  indexOfFirst kotlin.collections  indices kotlin.collections  isAudioFile kotlin.collections  
isNotBlank kotlin.collections  
isNotEmpty kotlin.collections  isServiceBound kotlin.collections  java kotlin.collections  launch kotlin.collections  let kotlin.collections  listOf kotlin.collections  	lowercase kotlin.collections  map kotlin.collections  musicService kotlin.collections  
mutableListOf kotlin.collections  mutableSetOf kotlin.collections  notifyPlaybackStateChanged kotlin.collections  notifySongChanged kotlin.collections  
onRemoveClick kotlin.collections  onSongClick kotlin.collections  pause kotlin.collections  play kotlin.collections  playlist kotlin.collections  
plusAssign kotlin.collections  preferenceManager kotlin.collections  scanDirectoryRecursive kotlin.collections  seekTo kotlin.collections  setOf kotlin.collections  	setResult kotlin.collections  showEmptyState kotlin.collections  
skipToNext kotlin.collections  skipToPrevious kotlin.collections  songAdapter kotlin.collections  songs kotlin.collections  sortedBy kotlin.collections  split kotlin.collections  startForeground kotlin.collections  startMusicService kotlin.collections  stop kotlin.collections  stopForeground kotlin.collections  stopSelf kotlin.collections  substringAfterLast kotlin.collections  substringBeforeLast kotlin.collections  takeIf kotlin.collections  toLongOrNull kotlin.collections  toMutableSet kotlin.collections  updateAppWidget kotlin.collections  updateCurrentSong kotlin.collections  updatePlaybackState kotlin.collections  updatePlaybackUI kotlin.collections  use kotlin.collections  withContext kotlin.collections  hasNext kotlin.collections.IntIterator  next kotlin.collections.IntIterator  getINDEXOfFirst kotlin.collections.List  
getINDICES kotlin.collections.List  
getISNotEmpty kotlin.collections.List  getIndexOfFirst kotlin.collections.List  
getIndices kotlin.collections.List  
getIsNotEmpty kotlin.collections.List  getMAP kotlin.collections.List  getMap kotlin.collections.List  
isNotEmpty kotlin.collections.List  getSORTEDBy kotlin.collections.MutableList  getSortedBy kotlin.collections.MutableList  
getISNotEmpty kotlin.collections.Set  
getIsNotEmpty kotlin.collections.Set  getMAP kotlin.collections.Set  getMap kotlin.collections.Set  getTOMutableSet kotlin.collections.Set  getToMutableSet kotlin.collections.Set  
isNotEmpty kotlin.collections.Set  ACTION_NEXT kotlin.comparisons  ACTION_PLAY_PAUSE kotlin.comparisons  ACTION_PREVIOUS kotlin.comparisons  Activity kotlin.comparisons  ActivityCompat kotlin.comparisons  !ActivityDirectorySelectionBinding kotlin.comparisons  ActivityMainBinding kotlin.comparisons  ActivityResultContracts kotlin.comparisons  AppWidgetManager kotlin.comparisons  AudioAttributes kotlin.comparisons  BrowserRoot kotlin.comparisons  Build kotlin.comparisons  C kotlin.comparisons  
ComponentName kotlin.comparisons  Context kotlin.comparisons  
ContextCompat kotlin.comparisons  DirectoryAdapter kotlin.comparisons  DirectorySelectionActivity kotlin.comparisons  Dispatchers kotlin.comparisons  DocumentFile kotlin.comparisons  DocumentsContract kotlin.comparisons  ErrorHandler kotlin.comparisons  	Exception kotlin.comparisons  	ExoPlayer kotlin.comparisons  File kotlin.comparisons  FileScanner kotlin.comparisons  Intent kotlin.comparisons  ItemDirectoryBinding kotlin.comparisons  ItemSongBinding kotlin.comparisons  KEY_LAST_PLAYED_SONG kotlin.comparisons  KEY_LAST_POSITION kotlin.comparisons  KEY_REPEAT_MODE kotlin.comparisons  KEY_SELECTED_DIRECTORIES kotlin.comparisons  KEY_SHUFFLE_MODE kotlin.comparisons  LayoutInflater kotlin.comparisons  LinearLayoutManager kotlin.comparisons  Log kotlin.comparisons  MainActivity kotlin.comparisons  Manifest kotlin.comparisons  	MediaItem kotlin.comparisons  MediaMetadataRetriever kotlin.comparisons  MediaSessionCompat kotlin.comparisons  
MediaStore kotlin.comparisons  MusicService kotlin.comparisons  MusicWidgetProvider kotlin.comparisons  NOTIFICATION_CHANNEL_ID kotlin.comparisons  NOTIFICATION_ID kotlin.comparisons  Notification kotlin.comparisons  NotificationChannel kotlin.comparisons  NotificationManager kotlin.comparisons  
PREFS_NAME kotlin.comparisons  PackageManager kotlin.comparisons  
PendingIntent kotlin.comparisons  PermissionHelper kotlin.comparisons  PlaybackStateCompat kotlin.comparisons  Player kotlin.comparisons  PlayerNotificationManager kotlin.comparisons  PreferenceManager kotlin.comparisons  R kotlin.comparisons  	RESULT_OK kotlin.comparisons  RemoteViews kotlin.comparisons  START_STICKY kotlin.comparisons  SecurityException kotlin.comparisons  Song kotlin.comparisons  SongAdapter kotlin.comparisons  String kotlin.comparisons  Toast kotlin.comparisons  Uri kotlin.comparisons  View kotlin.comparisons  all kotlin.comparisons  android kotlin.comparisons  any kotlin.comparisons  apply kotlin.comparisons  arrayOf kotlin.comparisons  binding kotlin.comparisons  coerceIn kotlin.comparisons  contains kotlin.comparisons  context kotlin.comparisons  currentIndex kotlin.comparisons  currentSong kotlin.comparisons  directoryAdapter kotlin.comparisons  	emptyList kotlin.comparisons  emptySet kotlin.comparisons  fileScanner kotlin.comparisons  finish kotlin.comparisons  forEach kotlin.comparisons  format kotlin.comparisons  handleTrackCompletion kotlin.comparisons  ifEmpty kotlin.comparisons  indexOfFirst kotlin.comparisons  indices kotlin.comparisons  isAudioFile kotlin.comparisons  
isNotBlank kotlin.comparisons  
isNotEmpty kotlin.comparisons  isServiceBound kotlin.comparisons  java kotlin.comparisons  launch kotlin.comparisons  let kotlin.comparisons  listOf kotlin.comparisons  	lowercase kotlin.comparisons  map kotlin.comparisons  musicService kotlin.comparisons  
mutableListOf kotlin.comparisons  mutableSetOf kotlin.comparisons  notifyPlaybackStateChanged kotlin.comparisons  notifySongChanged kotlin.comparisons  
onRemoveClick kotlin.comparisons  onSongClick kotlin.comparisons  pause kotlin.comparisons  play kotlin.comparisons  playlist kotlin.comparisons  
plusAssign kotlin.comparisons  preferenceManager kotlin.comparisons  scanDirectoryRecursive kotlin.comparisons  seekTo kotlin.comparisons  setOf kotlin.comparisons  	setResult kotlin.comparisons  showEmptyState kotlin.comparisons  
skipToNext kotlin.comparisons  skipToPrevious kotlin.comparisons  songAdapter kotlin.comparisons  songs kotlin.comparisons  sortedBy kotlin.comparisons  split kotlin.comparisons  startForeground kotlin.comparisons  startMusicService kotlin.comparisons  stop kotlin.comparisons  stopForeground kotlin.comparisons  stopSelf kotlin.comparisons  substringAfterLast kotlin.comparisons  substringBeforeLast kotlin.comparisons  takeIf kotlin.comparisons  toLongOrNull kotlin.comparisons  toMutableSet kotlin.comparisons  updateAppWidget kotlin.comparisons  updateCurrentSong kotlin.comparisons  updatePlaybackState kotlin.comparisons  updatePlaybackUI kotlin.comparisons  use kotlin.comparisons  withContext kotlin.comparisons  SuspendFunction1 kotlin.coroutines  ACTION_NEXT 	kotlin.io  ACTION_PLAY_PAUSE 	kotlin.io  ACTION_PREVIOUS 	kotlin.io  Activity 	kotlin.io  ActivityCompat 	kotlin.io  !ActivityDirectorySelectionBinding 	kotlin.io  ActivityMainBinding 	kotlin.io  ActivityResultContracts 	kotlin.io  AppWidgetManager 	kotlin.io  AudioAttributes 	kotlin.io  BrowserRoot 	kotlin.io  Build 	kotlin.io  C 	kotlin.io  
ComponentName 	kotlin.io  Context 	kotlin.io  
ContextCompat 	kotlin.io  DirectoryAdapter 	kotlin.io  DirectorySelectionActivity 	kotlin.io  Dispatchers 	kotlin.io  DocumentFile 	kotlin.io  DocumentsContract 	kotlin.io  ErrorHandler 	kotlin.io  	Exception 	kotlin.io  	ExoPlayer 	kotlin.io  File 	kotlin.io  FileScanner 	kotlin.io  Intent 	kotlin.io  ItemDirectoryBinding 	kotlin.io  ItemSongBinding 	kotlin.io  KEY_LAST_PLAYED_SONG 	kotlin.io  KEY_LAST_POSITION 	kotlin.io  KEY_REPEAT_MODE 	kotlin.io  KEY_SELECTED_DIRECTORIES 	kotlin.io  KEY_SHUFFLE_MODE 	kotlin.io  LayoutInflater 	kotlin.io  LinearLayoutManager 	kotlin.io  Log 	kotlin.io  MainActivity 	kotlin.io  Manifest 	kotlin.io  	MediaItem 	kotlin.io  MediaMetadataRetriever 	kotlin.io  MediaSessionCompat 	kotlin.io  
MediaStore 	kotlin.io  MusicService 	kotlin.io  MusicWidgetProvider 	kotlin.io  NOTIFICATION_CHANNEL_ID 	kotlin.io  NOTIFICATION_ID 	kotlin.io  Notification 	kotlin.io  NotificationChannel 	kotlin.io  NotificationManager 	kotlin.io  
PREFS_NAME 	kotlin.io  PackageManager 	kotlin.io  
PendingIntent 	kotlin.io  PermissionHelper 	kotlin.io  PlaybackStateCompat 	kotlin.io  Player 	kotlin.io  PlayerNotificationManager 	kotlin.io  PreferenceManager 	kotlin.io  R 	kotlin.io  	RESULT_OK 	kotlin.io  RemoteViews 	kotlin.io  START_STICKY 	kotlin.io  SecurityException 	kotlin.io  Song 	kotlin.io  SongAdapter 	kotlin.io  String 	kotlin.io  Toast 	kotlin.io  Uri 	kotlin.io  View 	kotlin.io  all 	kotlin.io  android 	kotlin.io  any 	kotlin.io  apply 	kotlin.io  arrayOf 	kotlin.io  binding 	kotlin.io  coerceIn 	kotlin.io  contains 	kotlin.io  context 	kotlin.io  currentIndex 	kotlin.io  currentSong 	kotlin.io  directoryAdapter 	kotlin.io  	emptyList 	kotlin.io  emptySet 	kotlin.io  fileScanner 	kotlin.io  finish 	kotlin.io  forEach 	kotlin.io  format 	kotlin.io  handleTrackCompletion 	kotlin.io  ifEmpty 	kotlin.io  indexOfFirst 	kotlin.io  indices 	kotlin.io  isAudioFile 	kotlin.io  
isNotBlank 	kotlin.io  
isNotEmpty 	kotlin.io  isServiceBound 	kotlin.io  java 	kotlin.io  launch 	kotlin.io  let 	kotlin.io  listOf 	kotlin.io  	lowercase 	kotlin.io  map 	kotlin.io  musicService 	kotlin.io  
mutableListOf 	kotlin.io  mutableSetOf 	kotlin.io  notifyPlaybackStateChanged 	kotlin.io  notifySongChanged 	kotlin.io  
onRemoveClick 	kotlin.io  onSongClick 	kotlin.io  pause 	kotlin.io  play 	kotlin.io  playlist 	kotlin.io  
plusAssign 	kotlin.io  preferenceManager 	kotlin.io  scanDirectoryRecursive 	kotlin.io  seekTo 	kotlin.io  setOf 	kotlin.io  	setResult 	kotlin.io  showEmptyState 	kotlin.io  
skipToNext 	kotlin.io  skipToPrevious 	kotlin.io  songAdapter 	kotlin.io  songs 	kotlin.io  sortedBy 	kotlin.io  split 	kotlin.io  startForeground 	kotlin.io  startMusicService 	kotlin.io  stop 	kotlin.io  stopForeground 	kotlin.io  stopSelf 	kotlin.io  substringAfterLast 	kotlin.io  substringBeforeLast 	kotlin.io  takeIf 	kotlin.io  toLongOrNull 	kotlin.io  toMutableSet 	kotlin.io  updateAppWidget 	kotlin.io  updateCurrentSong 	kotlin.io  updatePlaybackState 	kotlin.io  updatePlaybackUI 	kotlin.io  use 	kotlin.io  withContext 	kotlin.io  ACTION_NEXT 
kotlin.jvm  ACTION_PLAY_PAUSE 
kotlin.jvm  ACTION_PREVIOUS 
kotlin.jvm  Activity 
kotlin.jvm  ActivityCompat 
kotlin.jvm  !ActivityDirectorySelectionBinding 
kotlin.jvm  ActivityMainBinding 
kotlin.jvm  ActivityResultContracts 
kotlin.jvm  AppWidgetManager 
kotlin.jvm  AudioAttributes 
kotlin.jvm  BrowserRoot 
kotlin.jvm  Build 
kotlin.jvm  C 
kotlin.jvm  
ComponentName 
kotlin.jvm  Context 
kotlin.jvm  
ContextCompat 
kotlin.jvm  DirectoryAdapter 
kotlin.jvm  DirectorySelectionActivity 
kotlin.jvm  Dispatchers 
kotlin.jvm  DocumentFile 
kotlin.jvm  DocumentsContract 
kotlin.jvm  ErrorHandler 
kotlin.jvm  	Exception 
kotlin.jvm  	ExoPlayer 
kotlin.jvm  File 
kotlin.jvm  FileScanner 
kotlin.jvm  Intent 
kotlin.jvm  ItemDirectoryBinding 
kotlin.jvm  ItemSongBinding 
kotlin.jvm  KEY_LAST_PLAYED_SONG 
kotlin.jvm  KEY_LAST_POSITION 
kotlin.jvm  KEY_REPEAT_MODE 
kotlin.jvm  KEY_SELECTED_DIRECTORIES 
kotlin.jvm  KEY_SHUFFLE_MODE 
kotlin.jvm  LayoutInflater 
kotlin.jvm  LinearLayoutManager 
kotlin.jvm  Log 
kotlin.jvm  MainActivity 
kotlin.jvm  Manifest 
kotlin.jvm  	MediaItem 
kotlin.jvm  MediaMetadataRetriever 
kotlin.jvm  MediaSessionCompat 
kotlin.jvm  
MediaStore 
kotlin.jvm  MusicService 
kotlin.jvm  MusicWidgetProvider 
kotlin.jvm  NOTIFICATION_CHANNEL_ID 
kotlin.jvm  NOTIFICATION_ID 
kotlin.jvm  Notification 
kotlin.jvm  NotificationChannel 
kotlin.jvm  NotificationManager 
kotlin.jvm  
PREFS_NAME 
kotlin.jvm  PackageManager 
kotlin.jvm  
PendingIntent 
kotlin.jvm  PermissionHelper 
kotlin.jvm  PlaybackStateCompat 
kotlin.jvm  Player 
kotlin.jvm  PlayerNotificationManager 
kotlin.jvm  PreferenceManager 
kotlin.jvm  R 
kotlin.jvm  	RESULT_OK 
kotlin.jvm  RemoteViews 
kotlin.jvm  START_STICKY 
kotlin.jvm  SecurityException 
kotlin.jvm  Song 
kotlin.jvm  SongAdapter 
kotlin.jvm  String 
kotlin.jvm  Toast 
kotlin.jvm  Uri 
kotlin.jvm  View 
kotlin.jvm  all 
kotlin.jvm  android 
kotlin.jvm  any 
kotlin.jvm  apply 
kotlin.jvm  arrayOf 
kotlin.jvm  binding 
kotlin.jvm  coerceIn 
kotlin.jvm  contains 
kotlin.jvm  context 
kotlin.jvm  currentIndex 
kotlin.jvm  currentSong 
kotlin.jvm  directoryAdapter 
kotlin.jvm  	emptyList 
kotlin.jvm  emptySet 
kotlin.jvm  fileScanner 
kotlin.jvm  finish 
kotlin.jvm  forEach 
kotlin.jvm  format 
kotlin.jvm  handleTrackCompletion 
kotlin.jvm  ifEmpty 
kotlin.jvm  indexOfFirst 
kotlin.jvm  indices 
kotlin.jvm  isAudioFile 
kotlin.jvm  
isNotBlank 
kotlin.jvm  
isNotEmpty 
kotlin.jvm  isServiceBound 
kotlin.jvm  java 
kotlin.jvm  launch 
kotlin.jvm  let 
kotlin.jvm  listOf 
kotlin.jvm  	lowercase 
kotlin.jvm  map 
kotlin.jvm  musicService 
kotlin.jvm  
mutableListOf 
kotlin.jvm  mutableSetOf 
kotlin.jvm  notifyPlaybackStateChanged 
kotlin.jvm  notifySongChanged 
kotlin.jvm  
onRemoveClick 
kotlin.jvm  onSongClick 
kotlin.jvm  pause 
kotlin.jvm  play 
kotlin.jvm  playlist 
kotlin.jvm  
plusAssign 
kotlin.jvm  preferenceManager 
kotlin.jvm  scanDirectoryRecursive 
kotlin.jvm  seekTo 
kotlin.jvm  setOf 
kotlin.jvm  	setResult 
kotlin.jvm  showEmptyState 
kotlin.jvm  
skipToNext 
kotlin.jvm  skipToPrevious 
kotlin.jvm  songAdapter 
kotlin.jvm  songs 
kotlin.jvm  sortedBy 
kotlin.jvm  split 
kotlin.jvm  startForeground 
kotlin.jvm  startMusicService 
kotlin.jvm  stop 
kotlin.jvm  stopForeground 
kotlin.jvm  stopSelf 
kotlin.jvm  substringAfterLast 
kotlin.jvm  substringBeforeLast 
kotlin.jvm  takeIf 
kotlin.jvm  toLongOrNull 
kotlin.jvm  toMutableSet 
kotlin.jvm  updateAppWidget 
kotlin.jvm  updateCurrentSong 
kotlin.jvm  updatePlaybackState 
kotlin.jvm  updatePlaybackUI 
kotlin.jvm  use 
kotlin.jvm  withContext 
kotlin.jvm  ACTION_NEXT 
kotlin.ranges  ACTION_PLAY_PAUSE 
kotlin.ranges  ACTION_PREVIOUS 
kotlin.ranges  Activity 
kotlin.ranges  ActivityCompat 
kotlin.ranges  !ActivityDirectorySelectionBinding 
kotlin.ranges  ActivityMainBinding 
kotlin.ranges  ActivityResultContracts 
kotlin.ranges  AppWidgetManager 
kotlin.ranges  AudioAttributes 
kotlin.ranges  BrowserRoot 
kotlin.ranges  Build 
kotlin.ranges  C 
kotlin.ranges  
ComponentName 
kotlin.ranges  Context 
kotlin.ranges  
ContextCompat 
kotlin.ranges  DirectoryAdapter 
kotlin.ranges  DirectorySelectionActivity 
kotlin.ranges  Dispatchers 
kotlin.ranges  DocumentFile 
kotlin.ranges  DocumentsContract 
kotlin.ranges  ErrorHandler 
kotlin.ranges  	Exception 
kotlin.ranges  	ExoPlayer 
kotlin.ranges  File 
kotlin.ranges  FileScanner 
kotlin.ranges  IntRange 
kotlin.ranges  Intent 
kotlin.ranges  ItemDirectoryBinding 
kotlin.ranges  ItemSongBinding 
kotlin.ranges  KEY_LAST_PLAYED_SONG 
kotlin.ranges  KEY_LAST_POSITION 
kotlin.ranges  KEY_REPEAT_MODE 
kotlin.ranges  KEY_SELECTED_DIRECTORIES 
kotlin.ranges  KEY_SHUFFLE_MODE 
kotlin.ranges  LayoutInflater 
kotlin.ranges  LinearLayoutManager 
kotlin.ranges  Log 
kotlin.ranges  MainActivity 
kotlin.ranges  Manifest 
kotlin.ranges  	MediaItem 
kotlin.ranges  MediaMetadataRetriever 
kotlin.ranges  MediaSessionCompat 
kotlin.ranges  
MediaStore 
kotlin.ranges  MusicService 
kotlin.ranges  MusicWidgetProvider 
kotlin.ranges  NOTIFICATION_CHANNEL_ID 
kotlin.ranges  NOTIFICATION_ID 
kotlin.ranges  Notification 
kotlin.ranges  NotificationChannel 
kotlin.ranges  NotificationManager 
kotlin.ranges  
PREFS_NAME 
kotlin.ranges  PackageManager 
kotlin.ranges  
PendingIntent 
kotlin.ranges  PermissionHelper 
kotlin.ranges  PlaybackStateCompat 
kotlin.ranges  Player 
kotlin.ranges  PlayerNotificationManager 
kotlin.ranges  PreferenceManager 
kotlin.ranges  R 
kotlin.ranges  	RESULT_OK 
kotlin.ranges  RemoteViews 
kotlin.ranges  START_STICKY 
kotlin.ranges  SecurityException 
kotlin.ranges  Song 
kotlin.ranges  SongAdapter 
kotlin.ranges  String 
kotlin.ranges  Toast 
kotlin.ranges  Uri 
kotlin.ranges  View 
kotlin.ranges  all 
kotlin.ranges  android 
kotlin.ranges  any 
kotlin.ranges  apply 
kotlin.ranges  arrayOf 
kotlin.ranges  binding 
kotlin.ranges  coerceIn 
kotlin.ranges  contains 
kotlin.ranges  context 
kotlin.ranges  currentIndex 
kotlin.ranges  currentSong 
kotlin.ranges  directoryAdapter 
kotlin.ranges  	emptyList 
kotlin.ranges  emptySet 
kotlin.ranges  fileScanner 
kotlin.ranges  finish 
kotlin.ranges  forEach 
kotlin.ranges  format 
kotlin.ranges  handleTrackCompletion 
kotlin.ranges  ifEmpty 
kotlin.ranges  indexOfFirst 
kotlin.ranges  indices 
kotlin.ranges  isAudioFile 
kotlin.ranges  
isNotBlank 
kotlin.ranges  
isNotEmpty 
kotlin.ranges  isServiceBound 
kotlin.ranges  java 
kotlin.ranges  launch 
kotlin.ranges  let 
kotlin.ranges  listOf 
kotlin.ranges  	lowercase 
kotlin.ranges  map 
kotlin.ranges  musicService 
kotlin.ranges  
mutableListOf 
kotlin.ranges  mutableSetOf 
kotlin.ranges  notifyPlaybackStateChanged 
kotlin.ranges  notifySongChanged 
kotlin.ranges  
onRemoveClick 
kotlin.ranges  onSongClick 
kotlin.ranges  pause 
kotlin.ranges  play 
kotlin.ranges  playlist 
kotlin.ranges  
plusAssign 
kotlin.ranges  preferenceManager 
kotlin.ranges  scanDirectoryRecursive 
kotlin.ranges  seekTo 
kotlin.ranges  setOf 
kotlin.ranges  	setResult 
kotlin.ranges  showEmptyState 
kotlin.ranges  
skipToNext 
kotlin.ranges  skipToPrevious 
kotlin.ranges  songAdapter 
kotlin.ranges  songs 
kotlin.ranges  sortedBy 
kotlin.ranges  split 
kotlin.ranges  startForeground 
kotlin.ranges  startMusicService 
kotlin.ranges  stop 
kotlin.ranges  stopForeground 
kotlin.ranges  stopSelf 
kotlin.ranges  substringAfterLast 
kotlin.ranges  substringBeforeLast 
kotlin.ranges  takeIf 
kotlin.ranges  toLongOrNull 
kotlin.ranges  toMutableSet 
kotlin.ranges  updateAppWidget 
kotlin.ranges  updateCurrentSong 
kotlin.ranges  updatePlaybackState 
kotlin.ranges  updatePlaybackUI 
kotlin.ranges  use 
kotlin.ranges  withContext 
kotlin.ranges  contains kotlin.ranges.IntProgression  contains kotlin.ranges.IntRange  KClass kotlin.reflect  getJAVA kotlin.reflect.KClass  getJava kotlin.reflect.KClass  java kotlin.reflect.KClass  ACTION_NEXT kotlin.sequences  ACTION_PLAY_PAUSE kotlin.sequences  ACTION_PREVIOUS kotlin.sequences  Activity kotlin.sequences  ActivityCompat kotlin.sequences  !ActivityDirectorySelectionBinding kotlin.sequences  ActivityMainBinding kotlin.sequences  ActivityResultContracts kotlin.sequences  AppWidgetManager kotlin.sequences  AudioAttributes kotlin.sequences  BrowserRoot kotlin.sequences  Build kotlin.sequences  C kotlin.sequences  
ComponentName kotlin.sequences  Context kotlin.sequences  
ContextCompat kotlin.sequences  DirectoryAdapter kotlin.sequences  DirectorySelectionActivity kotlin.sequences  Dispatchers kotlin.sequences  DocumentFile kotlin.sequences  DocumentsContract kotlin.sequences  ErrorHandler kotlin.sequences  	Exception kotlin.sequences  	ExoPlayer kotlin.sequences  File kotlin.sequences  FileScanner kotlin.sequences  Intent kotlin.sequences  ItemDirectoryBinding kotlin.sequences  ItemSongBinding kotlin.sequences  KEY_LAST_PLAYED_SONG kotlin.sequences  KEY_LAST_POSITION kotlin.sequences  KEY_REPEAT_MODE kotlin.sequences  KEY_SELECTED_DIRECTORIES kotlin.sequences  KEY_SHUFFLE_MODE kotlin.sequences  LayoutInflater kotlin.sequences  LinearLayoutManager kotlin.sequences  Log kotlin.sequences  MainActivity kotlin.sequences  Manifest kotlin.sequences  	MediaItem kotlin.sequences  MediaMetadataRetriever kotlin.sequences  MediaSessionCompat kotlin.sequences  
MediaStore kotlin.sequences  MusicService kotlin.sequences  MusicWidgetProvider kotlin.sequences  NOTIFICATION_CHANNEL_ID kotlin.sequences  NOTIFICATION_ID kotlin.sequences  Notification kotlin.sequences  NotificationChannel kotlin.sequences  NotificationManager kotlin.sequences  
PREFS_NAME kotlin.sequences  PackageManager kotlin.sequences  
PendingIntent kotlin.sequences  PermissionHelper kotlin.sequences  PlaybackStateCompat kotlin.sequences  Player kotlin.sequences  PlayerNotificationManager kotlin.sequences  PreferenceManager kotlin.sequences  R kotlin.sequences  	RESULT_OK kotlin.sequences  RemoteViews kotlin.sequences  START_STICKY kotlin.sequences  SecurityException kotlin.sequences  Song kotlin.sequences  SongAdapter kotlin.sequences  String kotlin.sequences  Toast kotlin.sequences  Uri kotlin.sequences  View kotlin.sequences  all kotlin.sequences  android kotlin.sequences  any kotlin.sequences  apply kotlin.sequences  arrayOf kotlin.sequences  binding kotlin.sequences  coerceIn kotlin.sequences  contains kotlin.sequences  context kotlin.sequences  currentIndex kotlin.sequences  currentSong kotlin.sequences  directoryAdapter kotlin.sequences  	emptyList kotlin.sequences  emptySet kotlin.sequences  fileScanner kotlin.sequences  finish kotlin.sequences  forEach kotlin.sequences  format kotlin.sequences  handleTrackCompletion kotlin.sequences  ifEmpty kotlin.sequences  indexOfFirst kotlin.sequences  indices kotlin.sequences  isAudioFile kotlin.sequences  
isNotBlank kotlin.sequences  
isNotEmpty kotlin.sequences  isServiceBound kotlin.sequences  java kotlin.sequences  launch kotlin.sequences  let kotlin.sequences  listOf kotlin.sequences  	lowercase kotlin.sequences  map kotlin.sequences  musicService kotlin.sequences  
mutableListOf kotlin.sequences  mutableSetOf kotlin.sequences  notifyPlaybackStateChanged kotlin.sequences  notifySongChanged kotlin.sequences  
onRemoveClick kotlin.sequences  onSongClick kotlin.sequences  pause kotlin.sequences  play kotlin.sequences  playlist kotlin.sequences  
plusAssign kotlin.sequences  preferenceManager kotlin.sequences  scanDirectoryRecursive kotlin.sequences  seekTo kotlin.sequences  setOf kotlin.sequences  	setResult kotlin.sequences  showEmptyState kotlin.sequences  
skipToNext kotlin.sequences  skipToPrevious kotlin.sequences  songAdapter kotlin.sequences  songs kotlin.sequences  sortedBy kotlin.sequences  split kotlin.sequences  startForeground kotlin.sequences  startMusicService kotlin.sequences  stop kotlin.sequences  stopForeground kotlin.sequences  stopSelf kotlin.sequences  substringAfterLast kotlin.sequences  substringBeforeLast kotlin.sequences  takeIf kotlin.sequences  toLongOrNull kotlin.sequences  toMutableSet kotlin.sequences  updateAppWidget kotlin.sequences  updateCurrentSong kotlin.sequences  updatePlaybackState kotlin.sequences  updatePlaybackUI kotlin.sequences  use kotlin.sequences  withContext kotlin.sequences  ACTION_NEXT kotlin.text  ACTION_PLAY_PAUSE kotlin.text  ACTION_PREVIOUS kotlin.text  Activity kotlin.text  ActivityCompat kotlin.text  !ActivityDirectorySelectionBinding kotlin.text  ActivityMainBinding kotlin.text  ActivityResultContracts kotlin.text  AppWidgetManager kotlin.text  AudioAttributes kotlin.text  BrowserRoot kotlin.text  Build kotlin.text  C kotlin.text  
ComponentName kotlin.text  Context kotlin.text  
ContextCompat kotlin.text  DirectoryAdapter kotlin.text  DirectorySelectionActivity kotlin.text  Dispatchers kotlin.text  DocumentFile kotlin.text  DocumentsContract kotlin.text  ErrorHandler kotlin.text  	Exception kotlin.text  	ExoPlayer kotlin.text  File kotlin.text  FileScanner kotlin.text  Intent kotlin.text  ItemDirectoryBinding kotlin.text  ItemSongBinding kotlin.text  KEY_LAST_PLAYED_SONG kotlin.text  KEY_LAST_POSITION kotlin.text  KEY_REPEAT_MODE kotlin.text  KEY_SELECTED_DIRECTORIES kotlin.text  KEY_SHUFFLE_MODE kotlin.text  LayoutInflater kotlin.text  LinearLayoutManager kotlin.text  Log kotlin.text  MainActivity kotlin.text  Manifest kotlin.text  	MediaItem kotlin.text  MediaMetadataRetriever kotlin.text  MediaSessionCompat kotlin.text  
MediaStore kotlin.text  MusicService kotlin.text  MusicWidgetProvider kotlin.text  NOTIFICATION_CHANNEL_ID kotlin.text  NOTIFICATION_ID kotlin.text  Notification kotlin.text  NotificationChannel kotlin.text  NotificationManager kotlin.text  
PREFS_NAME kotlin.text  PackageManager kotlin.text  
PendingIntent kotlin.text  PermissionHelper kotlin.text  PlaybackStateCompat kotlin.text  Player kotlin.text  PlayerNotificationManager kotlin.text  PreferenceManager kotlin.text  R kotlin.text  	RESULT_OK kotlin.text  RemoteViews kotlin.text  START_STICKY kotlin.text  SecurityException kotlin.text  Song kotlin.text  SongAdapter kotlin.text  String kotlin.text  Toast kotlin.text  Uri kotlin.text  View kotlin.text  all kotlin.text  android kotlin.text  any kotlin.text  apply kotlin.text  arrayOf kotlin.text  binding kotlin.text  coerceIn kotlin.text  contains kotlin.text  context kotlin.text  currentIndex kotlin.text  currentSong kotlin.text  directoryAdapter kotlin.text  	emptyList kotlin.text  emptySet kotlin.text  fileScanner kotlin.text  finish kotlin.text  forEach kotlin.text  format kotlin.text  handleTrackCompletion kotlin.text  ifEmpty kotlin.text  indexOfFirst kotlin.text  indices kotlin.text  isAudioFile kotlin.text  
isNotBlank kotlin.text  
isNotEmpty kotlin.text  isServiceBound kotlin.text  java kotlin.text  launch kotlin.text  let kotlin.text  listOf kotlin.text  	lowercase kotlin.text  map kotlin.text  musicService kotlin.text  
mutableListOf kotlin.text  mutableSetOf kotlin.text  notifyPlaybackStateChanged kotlin.text  notifySongChanged kotlin.text  
onRemoveClick kotlin.text  onSongClick kotlin.text  pause kotlin.text  play kotlin.text  playlist kotlin.text  
plusAssign kotlin.text  preferenceManager kotlin.text  scanDirectoryRecursive kotlin.text  seekTo kotlin.text  setOf kotlin.text  	setResult kotlin.text  showEmptyState kotlin.text  
skipToNext kotlin.text  skipToPrevious kotlin.text  songAdapter kotlin.text  songs kotlin.text  sortedBy kotlin.text  split kotlin.text  startForeground kotlin.text  startMusicService kotlin.text  stop kotlin.text  stopForeground kotlin.text  stopSelf kotlin.text  substringAfterLast kotlin.text  substringBeforeLast kotlin.text  takeIf kotlin.text  toLongOrNull kotlin.text  toMutableSet kotlin.text  updateAppWidget kotlin.text  updateCurrentSong kotlin.text  updatePlaybackState kotlin.text  updatePlaybackUI kotlin.text  use kotlin.text  withContext kotlin.text  CoroutineDispatcher kotlinx.coroutines  CoroutineScope kotlinx.coroutines  Dispatchers kotlinx.coroutines  Job kotlinx.coroutines  launch kotlinx.coroutines  withContext kotlinx.coroutines  Activity !kotlinx.coroutines.CoroutineScope  DocumentFile !kotlinx.coroutines.CoroutineScope  ErrorHandler !kotlinx.coroutines.CoroutineScope  File !kotlinx.coroutines.CoroutineScope  
MediaStore !kotlinx.coroutines.CoroutineScope  Song !kotlinx.coroutines.CoroutineScope  Toast !kotlinx.coroutines.CoroutineScope  Uri !kotlinx.coroutines.CoroutineScope  View !kotlinx.coroutines.CoroutineScope  arrayOf !kotlinx.coroutines.CoroutineScope  binding !kotlinx.coroutines.CoroutineScope  context !kotlinx.coroutines.CoroutineScope  	emptyList !kotlinx.coroutines.CoroutineScope  fileScanner !kotlinx.coroutines.CoroutineScope  finish !kotlinx.coroutines.CoroutineScope  
getARRAYOf !kotlinx.coroutines.CoroutineScope  
getArrayOf !kotlinx.coroutines.CoroutineScope  
getBINDING !kotlinx.coroutines.CoroutineScope  
getBinding !kotlinx.coroutines.CoroutineScope  
getCONTEXT !kotlinx.coroutines.CoroutineScope  
getContext !kotlinx.coroutines.CoroutineScope  getEMPTYList !kotlinx.coroutines.CoroutineScope  getEmptyList !kotlinx.coroutines.CoroutineScope  getFILEScanner !kotlinx.coroutines.CoroutineScope  	getFINISH !kotlinx.coroutines.CoroutineScope  getFileScanner !kotlinx.coroutines.CoroutineScope  	getFinish !kotlinx.coroutines.CoroutineScope  getISAudioFile !kotlinx.coroutines.CoroutineScope  
getISNotEmpty !kotlinx.coroutines.CoroutineScope  getIsAudioFile !kotlinx.coroutines.CoroutineScope  
getIsNotEmpty !kotlinx.coroutines.CoroutineScope  getMUTABLEListOf !kotlinx.coroutines.CoroutineScope  getMutableListOf !kotlinx.coroutines.CoroutineScope  
getPLUSAssign !kotlinx.coroutines.CoroutineScope  getPREFERENCEManager !kotlinx.coroutines.CoroutineScope  
getPlusAssign !kotlinx.coroutines.CoroutineScope  getPreferenceManager !kotlinx.coroutines.CoroutineScope  getSCANDirectoryRecursive !kotlinx.coroutines.CoroutineScope  getSETResult !kotlinx.coroutines.CoroutineScope  getSHOWEmptyState !kotlinx.coroutines.CoroutineScope  getSONGAdapter !kotlinx.coroutines.CoroutineScope  getSONGS !kotlinx.coroutines.CoroutineScope  getSORTEDBy !kotlinx.coroutines.CoroutineScope  getSTARTMusicService !kotlinx.coroutines.CoroutineScope  getScanDirectoryRecursive !kotlinx.coroutines.CoroutineScope  getSetResult !kotlinx.coroutines.CoroutineScope  getShowEmptyState !kotlinx.coroutines.CoroutineScope  getSongAdapter !kotlinx.coroutines.CoroutineScope  getSongs !kotlinx.coroutines.CoroutineScope  getSortedBy !kotlinx.coroutines.CoroutineScope  getStartMusicService !kotlinx.coroutines.CoroutineScope  getUSE !kotlinx.coroutines.CoroutineScope  getUse !kotlinx.coroutines.CoroutineScope  isAudioFile !kotlinx.coroutines.CoroutineScope  
isNotEmpty !kotlinx.coroutines.CoroutineScope  
mutableListOf !kotlinx.coroutines.CoroutineScope  
plusAssign !kotlinx.coroutines.CoroutineScope  preferenceManager !kotlinx.coroutines.CoroutineScope  scanDirectoryRecursive !kotlinx.coroutines.CoroutineScope  	setResult !kotlinx.coroutines.CoroutineScope  showEmptyState !kotlinx.coroutines.CoroutineScope  songAdapter !kotlinx.coroutines.CoroutineScope  songs !kotlinx.coroutines.CoroutineScope  sortedBy !kotlinx.coroutines.CoroutineScope  startMusicService !kotlinx.coroutines.CoroutineScope  use !kotlinx.coroutines.CoroutineScope  IO kotlinx.coroutines.Dispatchers  	Parcelize kotlinx.parcelize  SimpleMainActivity com.minimalmusicplayer  TextView android.app.Activity  TextView android.content.Context  TextView android.content.ContextWrapper  TextView  android.view.ContextThemeWrapper  
setPadding android.view.View  getTEXTSize android.widget.TextView  getTextSize android.widget.TextView  
setPadding android.widget.TextView  setTextSize android.widget.TextView  textSize android.widget.TextView  TextView #androidx.activity.ComponentActivity  TextView (androidx.appcompat.app.AppCompatActivity  TextView #androidx.core.app.ComponentActivity  TextView &androidx.fragment.app.FragmentActivity  TextView com.minimalmusicplayer  Bundle )com.minimalmusicplayer.SimpleMainActivity  TextView )com.minimalmusicplayer.SimpleMainActivity  apply )com.minimalmusicplayer.SimpleMainActivity  getAPPLY )com.minimalmusicplayer.SimpleMainActivity  getApply )com.minimalmusicplayer.SimpleMainActivity  setContentView )com.minimalmusicplayer.SimpleMainActivity  TextView 	java.lang  TextView kotlin  TextView kotlin.annotation  TextView kotlin.collections  TextView kotlin.comparisons  TextView 	kotlin.io  TextView 
kotlin.jvm  TextView 
kotlin.ranges  TextView kotlin.sequences  TextView kotlin.text  android android.app.Activity  findViewById android.app.Activity  showWelcomeMessage android.app.Activity  findViewById android.content.Context  showWelcomeMessage android.content.Context  findViewById android.content.ContextWrapper  showWelcomeMessage android.content.ContextWrapper  android  android.view.ContextThemeWrapper  findViewById  android.view.ContextThemeWrapper  showWelcomeMessage  android.view.ContextThemeWrapper  
getVISIBILITY android.view.View  
getVisibility android.view.View  
setVisibility android.view.View  
visibility android.view.View  android #androidx.activity.ComponentActivity  findViewById #androidx.activity.ComponentActivity  showWelcomeMessage #androidx.activity.ComponentActivity  android (androidx.appcompat.app.AppCompatActivity  findViewById (androidx.appcompat.app.AppCompatActivity  showWelcomeMessage (androidx.appcompat.app.AppCompatActivity  android #androidx.core.app.ComponentActivity  findViewById #androidx.core.app.ComponentActivity  showWelcomeMessage #androidx.core.app.ComponentActivity  android &androidx.fragment.app.FragmentActivity  findViewById &androidx.fragment.app.FragmentActivity  showWelcomeMessage &androidx.fragment.app.FragmentActivity  android com.minimalmusicplayer  android #com.minimalmusicplayer.MainActivity  findViewById #com.minimalmusicplayer.MainActivity  
getANDROID #com.minimalmusicplayer.MainActivity  
getAndroid #com.minimalmusicplayer.MainActivity  showWelcomeMessage #com.minimalmusicplayer.MainActivity  playerContainer com.minimalmusicplayer.R.id  recyclerViewSongs com.minimalmusicplayer.R.id  textViewEmptyState com.minimalmusicplayer.R.id  
activity_main com.minimalmusicplayer.R.layout  Gravity android.view  CENTER android.view.Gravity  android android.widget.TextView  
getANDROID android.widget.TextView  
getAndroid android.widget.TextView  
getGRAVITY android.widget.TextView  
getGravity android.widget.TextView  gravity android.widget.TextView  
setGravity android.widget.TextView  addView android.view.View  addView android.view.ViewGroup  
setPadding android.view.ViewGroup  Button android.widget  android android.widget.Button  apply android.widget.Button  
getANDROID android.widget.Button  getAPPLY android.widget.Button  
getAndroid android.widget.Button  getApply android.widget.Button  getTEXT android.widget.Button  getText android.widget.Button  setText android.widget.Button  text android.widget.Button  VERTICAL android.widget.LinearLayout  addView android.widget.LinearLayout  android android.widget.LinearLayout  apply android.widget.LinearLayout  
getANDROID android.widget.LinearLayout  getAPPLY android.widget.LinearLayout  
getAndroid android.widget.LinearLayout  getApply android.widget.LinearLayout  getORIENTATION android.widget.LinearLayout  getOrientation android.widget.LinearLayout  orientation android.widget.LinearLayout  setOrientation android.widget.LinearLayout  
setPadding android.widget.LinearLayout  drawable 	android.R  list_selector_background android.R.drawable  PackageManager android.app.Activity  createMusicPlayerUI android.app.Activity  forEachIndexed android.app.Activity  nextSong android.app.Activity  previousSong android.app.Activity  scanForMusic android.app.Activity  songListLayout android.app.Activity  statusTextView android.app.Activity  updateSongList android.app.Activity  PackageManager android.content.Context  createMusicPlayerUI android.content.Context  forEachIndexed android.content.Context  nextSong android.content.Context  previousSong android.content.Context  scanForMusic android.content.Context  songListLayout android.content.Context  statusTextView android.content.Context  updateSongList android.content.Context  PackageManager android.content.ContextWrapper  createMusicPlayerUI android.content.ContextWrapper  forEachIndexed android.content.ContextWrapper  nextSong android.content.ContextWrapper  previousSong android.content.ContextWrapper  scanForMusic android.content.ContextWrapper  songListLayout android.content.ContextWrapper  statusTextView android.content.ContextWrapper  updateSongList android.content.ContextWrapper  PackageManager  android.view.ContextThemeWrapper  createMusicPlayerUI  android.view.ContextThemeWrapper  forEachIndexed  android.view.ContextThemeWrapper  nextSong  android.view.ContextThemeWrapper  previousSong  android.view.ContextThemeWrapper  scanForMusic  android.view.ContextThemeWrapper  songListLayout  android.view.ContextThemeWrapper  statusTextView  android.view.ContextThemeWrapper  updateSongList  android.view.ContextThemeWrapper  removeAllViews android.view.View  setBackgroundResource android.view.View  removeAllViews android.view.ViewGroup  
ScrollView android.widget  getNEXTSong android.widget.Button  getNextSong android.widget.Button  getPREVIOUSSong android.widget.Button  getPreviousSong android.widget.Button  getSCANForMusic android.widget.Button  getScanForMusic android.widget.Button  getTOGGLEPlayback android.widget.Button  getTogglePlayback android.widget.Button  nextSong android.widget.Button  previousSong android.widget.Button  scanForMusic android.widget.Button  togglePlayback android.widget.Button  addView android.widget.FrameLayout  apply android.widget.FrameLayout  
HORIZONTAL android.widget.LinearLayout  
getGRAVITY android.widget.LinearLayout  
getGravity android.widget.LinearLayout  gravity android.widget.LinearLayout  removeAllViews android.widget.LinearLayout  
setGravity android.widget.LinearLayout  addView android.widget.ScrollView  apply android.widget.ScrollView  getAPPLY android.widget.ScrollView  getApply android.widget.ScrollView  getSONGListLayout android.widget.ScrollView  getSongListLayout android.widget.ScrollView  songListLayout android.widget.ScrollView  getISClickable android.widget.TextView  getIsClickable android.widget.TextView  getPLAYSong android.widget.TextView  getPlaySong android.widget.TextView  isClickable android.widget.TextView  playSong android.widget.TextView  setBackgroundResource android.widget.TextView  setClickable android.widget.TextView  PackageManager #androidx.activity.ComponentActivity  createMusicPlayerUI #androidx.activity.ComponentActivity  forEachIndexed #androidx.activity.ComponentActivity  nextSong #androidx.activity.ComponentActivity  previousSong #androidx.activity.ComponentActivity  scanForMusic #androidx.activity.ComponentActivity  songListLayout #androidx.activity.ComponentActivity  statusTextView #androidx.activity.ComponentActivity  updateSongList #androidx.activity.ComponentActivity  PackageManager (androidx.appcompat.app.AppCompatActivity  createMusicPlayerUI (androidx.appcompat.app.AppCompatActivity  forEachIndexed (androidx.appcompat.app.AppCompatActivity  nextSong (androidx.appcompat.app.AppCompatActivity  previousSong (androidx.appcompat.app.AppCompatActivity  scanForMusic (androidx.appcompat.app.AppCompatActivity  songListLayout (androidx.appcompat.app.AppCompatActivity  statusTextView (androidx.appcompat.app.AppCompatActivity  updateSongList (androidx.appcompat.app.AppCompatActivity  PackageManager #androidx.core.app.ComponentActivity  createMusicPlayerUI #androidx.core.app.ComponentActivity  forEachIndexed #androidx.core.app.ComponentActivity  nextSong #androidx.core.app.ComponentActivity  previousSong #androidx.core.app.ComponentActivity  scanForMusic #androidx.core.app.ComponentActivity  songListLayout #androidx.core.app.ComponentActivity  statusTextView #androidx.core.app.ComponentActivity  updateSongList #androidx.core.app.ComponentActivity  PackageManager &androidx.fragment.app.FragmentActivity  createMusicPlayerUI &androidx.fragment.app.FragmentActivity  forEachIndexed &androidx.fragment.app.FragmentActivity  nextSong &androidx.fragment.app.FragmentActivity  previousSong &androidx.fragment.app.FragmentActivity  scanForMusic &androidx.fragment.app.FragmentActivity  songListLayout &androidx.fragment.app.FragmentActivity  statusTextView &androidx.fragment.app.FragmentActivity  updateSongList &androidx.fragment.app.FragmentActivity  PackageManager com.minimalmusicplayer  forEachIndexed com.minimalmusicplayer  nextSong com.minimalmusicplayer  playSong com.minimalmusicplayer  previousSong com.minimalmusicplayer  scanForMusic com.minimalmusicplayer  songListLayout com.minimalmusicplayer  statusTextView com.minimalmusicplayer  togglePlayback com.minimalmusicplayer  updateSongList com.minimalmusicplayer  PackageManager #com.minimalmusicplayer.MainActivity  createMusicPlayerUI #com.minimalmusicplayer.MainActivity  currentSongIndex #com.minimalmusicplayer.MainActivity  currentSongTextView #com.minimalmusicplayer.MainActivity  forEachIndexed #com.minimalmusicplayer.MainActivity  getFOREachIndexed #com.minimalmusicplayer.MainActivity  getForEachIndexed #com.minimalmusicplayer.MainActivity  nextSong #com.minimalmusicplayer.MainActivity  playPauseButton #com.minimalmusicplayer.MainActivity  previousSong #com.minimalmusicplayer.MainActivity  scanForMusic #com.minimalmusicplayer.MainActivity  songListLayout #com.minimalmusicplayer.MainActivity  statusTextView #com.minimalmusicplayer.MainActivity  updateSongList #com.minimalmusicplayer.MainActivity  forEachIndexed 	java.lang  nextSong 	java.lang  playSong 	java.lang  previousSong 	java.lang  scanForMusic 	java.lang  songListLayout 	java.lang  statusTextView 	java.lang  togglePlayback 	java.lang  updateSongList 	java.lang  forEachIndexed kotlin  nextSong kotlin  playSong kotlin  previousSong kotlin  scanForMusic kotlin  songListLayout kotlin  statusTextView kotlin  togglePlayback kotlin  updateSongList kotlin  forEachIndexed kotlin.annotation  nextSong kotlin.annotation  playSong kotlin.annotation  previousSong kotlin.annotation  scanForMusic kotlin.annotation  songListLayout kotlin.annotation  statusTextView kotlin.annotation  togglePlayback kotlin.annotation  updateSongList kotlin.annotation  forEachIndexed kotlin.collections  nextSong kotlin.collections  playSong kotlin.collections  previousSong kotlin.collections  scanForMusic kotlin.collections  songListLayout kotlin.collections  statusTextView kotlin.collections  togglePlayback kotlin.collections  updateSongList kotlin.collections  getFOREachIndexed kotlin.collections.MutableList  getForEachIndexed kotlin.collections.MutableList  forEachIndexed kotlin.comparisons  nextSong kotlin.comparisons  playSong kotlin.comparisons  previousSong kotlin.comparisons  scanForMusic kotlin.comparisons  songListLayout kotlin.comparisons  statusTextView kotlin.comparisons  togglePlayback kotlin.comparisons  updateSongList kotlin.comparisons  forEachIndexed 	kotlin.io  nextSong 	kotlin.io  playSong 	kotlin.io  previousSong 	kotlin.io  scanForMusic 	kotlin.io  songListLayout 	kotlin.io  statusTextView 	kotlin.io  togglePlayback 	kotlin.io  updateSongList 	kotlin.io  forEachIndexed 
kotlin.jvm  nextSong 
kotlin.jvm  playSong 
kotlin.jvm  previousSong 
kotlin.jvm  scanForMusic 
kotlin.jvm  songListLayout 
kotlin.jvm  statusTextView 
kotlin.jvm  togglePlayback 
kotlin.jvm  updateSongList 
kotlin.jvm  forEachIndexed 
kotlin.ranges  nextSong 
kotlin.ranges  playSong 
kotlin.ranges  previousSong 
kotlin.ranges  scanForMusic 
kotlin.ranges  songListLayout 
kotlin.ranges  statusTextView 
kotlin.ranges  togglePlayback 
kotlin.ranges  updateSongList 
kotlin.ranges  forEachIndexed kotlin.sequences  nextSong kotlin.sequences  playSong kotlin.sequences  previousSong kotlin.sequences  scanForMusic kotlin.sequences  songListLayout kotlin.sequences  statusTextView kotlin.sequences  togglePlayback kotlin.sequences  updateSongList kotlin.sequences  forEachIndexed kotlin.text  nextSong kotlin.text  playSong kotlin.text  previousSong kotlin.text  scanForMusic kotlin.text  songListLayout kotlin.text  statusTextView kotlin.text  togglePlayback kotlin.text  updateSongList kotlin.text  android !kotlinx.coroutines.CoroutineScope  
getANDROID !kotlinx.coroutines.CoroutineScope  
getAndroid !kotlinx.coroutines.CoroutineScope  getSTATUSTextView !kotlinx.coroutines.CoroutineScope  getStatusTextView !kotlinx.coroutines.CoroutineScope  getUPDATESongList !kotlinx.coroutines.CoroutineScope  getUpdateSongList !kotlinx.coroutines.CoroutineScope  statusTextView !kotlinx.coroutines.CoroutineScope  updateSongList !kotlinx.coroutines.CoroutineScope  MediaPlayer android.app.Activity  currentSong android.app.Activity  currentSongTextView android.app.Activity  isCurrentlyPlaying android.app.Activity  onPause android.app.Activity  playPauseButton android.app.Activity  run android.app.Activity  scanSelectedDirectory android.app.Activity  selectMusicFolder android.app.Activity  stopCurrentPlayback android.app.Activity  MediaPlayer android.content.Context  currentSongTextView android.content.Context  isCurrentlyPlaying android.content.Context  onPause android.content.Context  playPauseButton android.content.Context  run android.content.Context  scanSelectedDirectory android.content.Context  selectMusicFolder android.content.Context  stopCurrentPlayback android.content.Context  MediaPlayer android.content.ContextWrapper  currentSongTextView android.content.ContextWrapper  isCurrentlyPlaying android.content.ContextWrapper  onPause android.content.ContextWrapper  playPauseButton android.content.ContextWrapper  run android.content.ContextWrapper  scanSelectedDirectory android.content.ContextWrapper  selectMusicFolder android.content.ContextWrapper  stopCurrentPlayback android.content.ContextWrapper  MediaPlayer 
android.media  Uri android.media.MediaPlayer  android android.media.MediaPlayer  apply android.media.MediaPlayer  currentSong android.media.MediaPlayer  currentSongTextView android.media.MediaPlayer  
getANDROID android.media.MediaPlayer  getAPPLY android.media.MediaPlayer  
getAndroid android.media.MediaPlayer  getApply android.media.MediaPlayer  getCURRENTSong android.media.MediaPlayer  getCURRENTSongTextView android.media.MediaPlayer  getCurrentSong android.media.MediaPlayer  getCurrentSongTextView android.media.MediaPlayer  getISCurrentlyPlaying android.media.MediaPlayer  getISPlaying android.media.MediaPlayer  getIsCurrentlyPlaying android.media.MediaPlayer  getIsPlaying android.media.MediaPlayer  getLET android.media.MediaPlayer  getLet android.media.MediaPlayer  getNEXTSong android.media.MediaPlayer  getNextSong android.media.MediaPlayer  getPLAYPauseButton android.media.MediaPlayer  getPlayPauseButton android.media.MediaPlayer  isCurrentlyPlaying android.media.MediaPlayer  	isPlaying android.media.MediaPlayer  let android.media.MediaPlayer  nextSong android.media.MediaPlayer  pause android.media.MediaPlayer  playPauseButton android.media.MediaPlayer  prepareAsync android.media.MediaPlayer  release android.media.MediaPlayer  
setDataSource android.media.MediaPlayer  setOnCompletionListener android.media.MediaPlayer  setOnErrorListener android.media.MediaPlayer  setOnPreparedListener android.media.MediaPlayer  
setPlaying android.media.MediaPlayer  start android.media.MediaPlayer  stop android.media.MediaPlayer  <SAM-CONSTRUCTOR> .android.media.MediaPlayer.OnCompletionListener  <SAM-CONSTRUCTOR> )android.media.MediaPlayer.OnErrorListener  <SAM-CONSTRUCTOR> ,android.media.MediaPlayer.OnPreparedListener  MediaPlayer  android.view.ContextThemeWrapper  currentSong  android.view.ContextThemeWrapper  currentSongTextView  android.view.ContextThemeWrapper  isCurrentlyPlaying  android.view.ContextThemeWrapper  onPause  android.view.ContextThemeWrapper  playPauseButton  android.view.ContextThemeWrapper  run  android.view.ContextThemeWrapper  scanSelectedDirectory  android.view.ContextThemeWrapper  selectMusicFolder  android.view.ContextThemeWrapper  stopCurrentPlayback  android.view.ContextThemeWrapper  getSELECTMusicFolder android.widget.Button  getSelectMusicFolder android.widget.Button  selectMusicFolder android.widget.Button  MediaPlayer #androidx.activity.ComponentActivity  currentSong #androidx.activity.ComponentActivity  currentSongTextView #androidx.activity.ComponentActivity  isCurrentlyPlaying #androidx.activity.ComponentActivity  onPause #androidx.activity.ComponentActivity  playPauseButton #androidx.activity.ComponentActivity  run #androidx.activity.ComponentActivity  scanSelectedDirectory #androidx.activity.ComponentActivity  selectMusicFolder #androidx.activity.ComponentActivity  stopCurrentPlayback #androidx.activity.ComponentActivity  OpenDocumentTree 9androidx.activity.result.contract.ActivityResultContracts  MediaPlayer (androidx.appcompat.app.AppCompatActivity  currentSong (androidx.appcompat.app.AppCompatActivity  currentSongTextView (androidx.appcompat.app.AppCompatActivity  isCurrentlyPlaying (androidx.appcompat.app.AppCompatActivity  onPause (androidx.appcompat.app.AppCompatActivity  playPauseButton (androidx.appcompat.app.AppCompatActivity  run (androidx.appcompat.app.AppCompatActivity  scanSelectedDirectory (androidx.appcompat.app.AppCompatActivity  selectMusicFolder (androidx.appcompat.app.AppCompatActivity  stopCurrentPlayback (androidx.appcompat.app.AppCompatActivity  MediaPlayer #androidx.core.app.ComponentActivity  currentSong #androidx.core.app.ComponentActivity  currentSongTextView #androidx.core.app.ComponentActivity  isCurrentlyPlaying #androidx.core.app.ComponentActivity  onPause #androidx.core.app.ComponentActivity  playPauseButton #androidx.core.app.ComponentActivity  run #androidx.core.app.ComponentActivity  scanSelectedDirectory #androidx.core.app.ComponentActivity  selectMusicFolder #androidx.core.app.ComponentActivity  stopCurrentPlayback #androidx.core.app.ComponentActivity  MediaPlayer &androidx.fragment.app.FragmentActivity  currentSong &androidx.fragment.app.FragmentActivity  currentSongTextView &androidx.fragment.app.FragmentActivity  isCurrentlyPlaying &androidx.fragment.app.FragmentActivity  onPause &androidx.fragment.app.FragmentActivity  playPauseButton &androidx.fragment.app.FragmentActivity  run &androidx.fragment.app.FragmentActivity  scanSelectedDirectory &androidx.fragment.app.FragmentActivity  selectMusicFolder &androidx.fragment.app.FragmentActivity  stopCurrentPlayback &androidx.fragment.app.FragmentActivity  MediaPlayer com.minimalmusicplayer  currentSong com.minimalmusicplayer  currentSongTextView com.minimalmusicplayer  isCurrentlyPlaying com.minimalmusicplayer  playPauseButton com.minimalmusicplayer  run com.minimalmusicplayer  selectMusicFolder com.minimalmusicplayer  MediaPlayer #com.minimalmusicplayer.MainActivity  contentResolver #com.minimalmusicplayer.MainActivity  directoryPickerLauncher #com.minimalmusicplayer.MainActivity  getCONTENTResolver #com.minimalmusicplayer.MainActivity  getContentResolver #com.minimalmusicplayer.MainActivity  getRUN #com.minimalmusicplayer.MainActivity  getRun #com.minimalmusicplayer.MainActivity  isCurrentlyPlaying #com.minimalmusicplayer.MainActivity  mediaPlayer #com.minimalmusicplayer.MainActivity  run #com.minimalmusicplayer.MainActivity  scanSelectedDirectory #com.minimalmusicplayer.MainActivity  selectMusicFolder #com.minimalmusicplayer.MainActivity  setContentResolver #com.minimalmusicplayer.MainActivity  stopCurrentPlayback #com.minimalmusicplayer.MainActivity  MediaPlayer 	java.lang  currentSongTextView 	java.lang  isCurrentlyPlaying 	java.lang  playPauseButton 	java.lang  run 	java.lang  selectMusicFolder 	java.lang  	Function3 kotlin  MediaPlayer kotlin  currentSongTextView kotlin  isCurrentlyPlaying kotlin  playPauseButton kotlin  run kotlin  selectMusicFolder kotlin  MediaPlayer kotlin.annotation  currentSongTextView kotlin.annotation  isCurrentlyPlaying kotlin.annotation  playPauseButton kotlin.annotation  run kotlin.annotation  selectMusicFolder kotlin.annotation  MediaPlayer kotlin.collections  currentSongTextView kotlin.collections  isCurrentlyPlaying kotlin.collections  playPauseButton kotlin.collections  run kotlin.collections  selectMusicFolder kotlin.collections  MediaPlayer kotlin.comparisons  currentSongTextView kotlin.comparisons  isCurrentlyPlaying kotlin.comparisons  playPauseButton kotlin.comparisons  run kotlin.comparisons  selectMusicFolder kotlin.comparisons  MediaPlayer 	kotlin.io  currentSongTextView 	kotlin.io  isCurrentlyPlaying 	kotlin.io  playPauseButton 	kotlin.io  run 	kotlin.io  selectMusicFolder 	kotlin.io  MediaPlayer 
kotlin.jvm  currentSongTextView 
kotlin.jvm  isCurrentlyPlaying 
kotlin.jvm  playPauseButton 
kotlin.jvm  run 
kotlin.jvm  selectMusicFolder 
kotlin.jvm  MediaPlayer 
kotlin.ranges  currentSongTextView 
kotlin.ranges  isCurrentlyPlaying 
kotlin.ranges  playPauseButton 
kotlin.ranges  run 
kotlin.ranges  selectMusicFolder 
kotlin.ranges  MediaPlayer kotlin.sequences  currentSongTextView kotlin.sequences  isCurrentlyPlaying kotlin.sequences  playPauseButton kotlin.sequences  run kotlin.sequences  selectMusicFolder kotlin.sequences  MediaPlayer kotlin.text  currentSongTextView kotlin.text  isCurrentlyPlaying kotlin.text  playPauseButton kotlin.text  run kotlin.text  selectMusicFolder kotlin.text  ACTION_PLAY_PAUSE #com.minimalmusicplayer.MainActivity  ACTION_PLAY_PAUSE -com.minimalmusicplayer.MainActivity.Companion  NOTIFICATION_ID -com.minimalmusicplayer.MainActivity.Companion  NOTIFICATION_ID #com.minimalmusicplayer.MainActivity  ACTION_PREVIOUS -com.minimalmusicplayer.MainActivity.Companion  
CHANNEL_ID #com.minimalmusicplayer.MainActivity  ACTION_PREVIOUS #com.minimalmusicplayer.MainActivity  ACTION_NEXT -com.minimalmusicplayer.MainActivity.Companion  ACTION_NEXT #com.minimalmusicplayer.MainActivity  	Companion #com.minimalmusicplayer.MainActivity  
CHANNEL_ID -com.minimalmusicplayer.MainActivity.Companion  ACTION_NEXT android.app.Activity  ACTION_PLAY_PAUSE android.app.Activity  ACTION_PREVIOUS android.app.Activity  BroadcastReceiver android.app.Activity  Build android.app.Activity  
CHANNEL_ID android.app.Activity  IntentFilter android.app.Activity  MainActivity android.app.Activity  MediaMetadataCompat android.app.Activity  MediaSessionCompat android.app.Activity  NOTIFICATION_ID android.app.Activity  NotificationChannel android.app.Activity  NotificationCompat android.app.Activity  NotificationManager android.app.Activity  
PendingIntent android.app.Activity  PlaybackStateCompat android.app.Activity  androidx android.app.Activity  createMediaNotification android.app.Activity  createNotificationChannel android.app.Activity  getSystemService android.app.Activity  initializeMediaSession android.app.Activity  registerMediaControlReceiver android.app.Activity  registerReceiver android.app.Activity  unregisterReceiver android.app.Activity  updateMediaSession android.app.Activity  cancel android.app.NotificationManager  notify android.app.NotificationManager  BroadcastReceiver android.content  IntentFilter android.content  nextSong !android.content.BroadcastReceiver  previousSong !android.content.BroadcastReceiver  togglePlayback !android.content.BroadcastReceiver  ACTION_NEXT android.content.Context  ACTION_PLAY_PAUSE android.content.Context  ACTION_PREVIOUS android.content.Context  BroadcastReceiver android.content.Context  
CHANNEL_ID android.content.Context  IntentFilter android.content.Context  MediaMetadataCompat android.content.Context  NOTIFICATION_SERVICE android.content.Context  NotificationCompat android.content.Context  androidx android.content.Context  createMediaNotification android.content.Context  registerMediaControlReceiver android.content.Context  registerReceiver android.content.Context  unregisterReceiver android.content.Context  updateMediaSession android.content.Context  ACTION_NEXT android.content.ContextWrapper  ACTION_PLAY_PAUSE android.content.ContextWrapper  ACTION_PREVIOUS android.content.ContextWrapper  BroadcastReceiver android.content.ContextWrapper  
CHANNEL_ID android.content.ContextWrapper  IntentFilter android.content.ContextWrapper  MediaMetadataCompat android.content.ContextWrapper  NotificationCompat android.content.ContextWrapper  androidx android.content.ContextWrapper  createMediaNotification android.content.ContextWrapper  registerMediaControlReceiver android.content.ContextWrapper  registerReceiver android.content.ContextWrapper  unregisterReceiver android.content.ContextWrapper  updateMediaSession android.content.ContextWrapper  ACTION_NEXT android.content.IntentFilter  ACTION_PLAY_PAUSE android.content.IntentFilter  ACTION_PREVIOUS android.content.IntentFilter  	addAction android.content.IntentFilter  apply android.content.IntentFilter  getAPPLY android.content.IntentFilter  getApply android.content.IntentFilter  
BitmapFactory android.graphics  createMediaNotification android.media.MediaPlayer  getCREATEMediaNotification android.media.MediaPlayer  getCreateMediaNotification android.media.MediaPlayer  getUPDATEMediaSession android.media.MediaPlayer  getUpdateMediaSession android.media.MediaPlayer  updateMediaSession android.media.MediaPlayer  Builder ,android.support.v4.media.MediaMetadataCompat  METADATA_KEY_ALBUM ,android.support.v4.media.MediaMetadataCompat  METADATA_KEY_ARTIST ,android.support.v4.media.MediaMetadataCompat  METADATA_KEY_DURATION ,android.support.v4.media.MediaMetadataCompat  METADATA_KEY_TITLE ,android.support.v4.media.MediaMetadataCompat  build 4android.support.v4.media.MediaMetadataCompat.Builder  putLong 4android.support.v4.media.MediaMetadataCompat.Builder  	putString 4android.support.v4.media.MediaMetadataCompat.Builder  apply 3android.support.v4.media.session.MediaSessionCompat  currentSong 3android.support.v4.media.session.MediaSessionCompat  getAPPLY 3android.support.v4.media.session.MediaSessionCompat  getApply 3android.support.v4.media.session.MediaSessionCompat  getCURRENTSong 3android.support.v4.media.session.MediaSessionCompat  getCurrentSong 3android.support.v4.media.session.MediaSessionCompat  getISCurrentlyPlaying 3android.support.v4.media.session.MediaSessionCompat  getIsCurrentlyPlaying 3android.support.v4.media.session.MediaSessionCompat  getNEXTSong 3android.support.v4.media.session.MediaSessionCompat  getNextSong 3android.support.v4.media.session.MediaSessionCompat  getPREVIOUSSong 3android.support.v4.media.session.MediaSessionCompat  getPreviousSong 3android.support.v4.media.session.MediaSessionCompat  getTOGGLEPlayback 3android.support.v4.media.session.MediaSessionCompat  getTogglePlayback 3android.support.v4.media.session.MediaSessionCompat  isCurrentlyPlaying 3android.support.v4.media.session.MediaSessionCompat  nextSong 3android.support.v4.media.session.MediaSessionCompat  previousSong 3android.support.v4.media.session.MediaSessionCompat  setMetadata 3android.support.v4.media.session.MediaSessionCompat  togglePlayback 3android.support.v4.media.session.MediaSessionCompat  currentSong <android.support.v4.media.session.MediaSessionCompat.Callback  isCurrentlyPlaying <android.support.v4.media.session.MediaSessionCompat.Callback  nextSong <android.support.v4.media.session.MediaSessionCompat.Callback  previousSong <android.support.v4.media.session.MediaSessionCompat.Callback  togglePlayback <android.support.v4.media.session.MediaSessionCompat.Callback  PLAYBACK_POSITION_UNKNOWN 4android.support.v4.media.session.PlaybackStateCompat  ACTION_NEXT  android.view.ContextThemeWrapper  ACTION_PLAY_PAUSE  android.view.ContextThemeWrapper  ACTION_PREVIOUS  android.view.ContextThemeWrapper  BroadcastReceiver  android.view.ContextThemeWrapper  Build  android.view.ContextThemeWrapper  
CHANNEL_ID  android.view.ContextThemeWrapper  IntentFilter  android.view.ContextThemeWrapper  MainActivity  android.view.ContextThemeWrapper  MediaMetadataCompat  android.view.ContextThemeWrapper  MediaSessionCompat  android.view.ContextThemeWrapper  NOTIFICATION_ID  android.view.ContextThemeWrapper  NotificationChannel  android.view.ContextThemeWrapper  NotificationCompat  android.view.ContextThemeWrapper  NotificationManager  android.view.ContextThemeWrapper  
PendingIntent  android.view.ContextThemeWrapper  PlaybackStateCompat  android.view.ContextThemeWrapper  androidx  android.view.ContextThemeWrapper  createMediaNotification  android.view.ContextThemeWrapper  createNotificationChannel  android.view.ContextThemeWrapper  getSystemService  android.view.ContextThemeWrapper  initializeMediaSession  android.view.ContextThemeWrapper  registerMediaControlReceiver  android.view.ContextThemeWrapper  registerReceiver  android.view.ContextThemeWrapper  unregisterReceiver  android.view.ContextThemeWrapper  updateMediaSession  android.view.ContextThemeWrapper  ACTION_NEXT #androidx.activity.ComponentActivity  ACTION_PLAY_PAUSE #androidx.activity.ComponentActivity  ACTION_PREVIOUS #androidx.activity.ComponentActivity  BroadcastReceiver #androidx.activity.ComponentActivity  Build #androidx.activity.ComponentActivity  
CHANNEL_ID #androidx.activity.ComponentActivity  IntentFilter #androidx.activity.ComponentActivity  MainActivity #androidx.activity.ComponentActivity  MediaMetadataCompat #androidx.activity.ComponentActivity  MediaSessionCompat #androidx.activity.ComponentActivity  NOTIFICATION_ID #androidx.activity.ComponentActivity  NotificationChannel #androidx.activity.ComponentActivity  NotificationCompat #androidx.activity.ComponentActivity  NotificationManager #androidx.activity.ComponentActivity  
PendingIntent #androidx.activity.ComponentActivity  PlaybackStateCompat #androidx.activity.ComponentActivity  androidx #androidx.activity.ComponentActivity  createMediaNotification #androidx.activity.ComponentActivity  createNotificationChannel #androidx.activity.ComponentActivity  getSystemService #androidx.activity.ComponentActivity  initializeMediaSession #androidx.activity.ComponentActivity  registerMediaControlReceiver #androidx.activity.ComponentActivity  registerReceiver #androidx.activity.ComponentActivity  unregisterReceiver #androidx.activity.ComponentActivity  updateMediaSession #androidx.activity.ComponentActivity  ACTION_NEXT (androidx.appcompat.app.AppCompatActivity  ACTION_PLAY_PAUSE (androidx.appcompat.app.AppCompatActivity  ACTION_PREVIOUS (androidx.appcompat.app.AppCompatActivity  BroadcastReceiver (androidx.appcompat.app.AppCompatActivity  Build (androidx.appcompat.app.AppCompatActivity  
CHANNEL_ID (androidx.appcompat.app.AppCompatActivity  IntentFilter (androidx.appcompat.app.AppCompatActivity  MainActivity (androidx.appcompat.app.AppCompatActivity  MediaMetadataCompat (androidx.appcompat.app.AppCompatActivity  MediaSessionCompat (androidx.appcompat.app.AppCompatActivity  NOTIFICATION_ID (androidx.appcompat.app.AppCompatActivity  NotificationChannel (androidx.appcompat.app.AppCompatActivity  NotificationCompat (androidx.appcompat.app.AppCompatActivity  NotificationManager (androidx.appcompat.app.AppCompatActivity  
PendingIntent (androidx.appcompat.app.AppCompatActivity  PlaybackStateCompat (androidx.appcompat.app.AppCompatActivity  androidx (androidx.appcompat.app.AppCompatActivity  createMediaNotification (androidx.appcompat.app.AppCompatActivity  createNotificationChannel (androidx.appcompat.app.AppCompatActivity  getSystemService (androidx.appcompat.app.AppCompatActivity  initializeMediaSession (androidx.appcompat.app.AppCompatActivity  registerMediaControlReceiver (androidx.appcompat.app.AppCompatActivity  registerReceiver (androidx.appcompat.app.AppCompatActivity  unregisterReceiver (androidx.appcompat.app.AppCompatActivity  updateMediaSession (androidx.appcompat.app.AppCompatActivity  ACTION_NEXT #androidx.core.app.ComponentActivity  ACTION_PLAY_PAUSE #androidx.core.app.ComponentActivity  ACTION_PREVIOUS #androidx.core.app.ComponentActivity  BroadcastReceiver #androidx.core.app.ComponentActivity  Build #androidx.core.app.ComponentActivity  
CHANNEL_ID #androidx.core.app.ComponentActivity  IntentFilter #androidx.core.app.ComponentActivity  MainActivity #androidx.core.app.ComponentActivity  MediaMetadataCompat #androidx.core.app.ComponentActivity  MediaSessionCompat #androidx.core.app.ComponentActivity  NOTIFICATION_ID #androidx.core.app.ComponentActivity  NotificationChannel #androidx.core.app.ComponentActivity  NotificationCompat #androidx.core.app.ComponentActivity  NotificationManager #androidx.core.app.ComponentActivity  
PendingIntent #androidx.core.app.ComponentActivity  PlaybackStateCompat #androidx.core.app.ComponentActivity  androidx #androidx.core.app.ComponentActivity  createMediaNotification #androidx.core.app.ComponentActivity  createNotificationChannel #androidx.core.app.ComponentActivity  getSystemService #androidx.core.app.ComponentActivity  initializeMediaSession #androidx.core.app.ComponentActivity  registerMediaControlReceiver #androidx.core.app.ComponentActivity  registerReceiver #androidx.core.app.ComponentActivity  unregisterReceiver #androidx.core.app.ComponentActivity  updateMediaSession #androidx.core.app.ComponentActivity  Builder $androidx.core.app.NotificationCompat  PRIORITY_LOW $androidx.core.app.NotificationCompat  VISIBILITY_PUBLIC $androidx.core.app.NotificationCompat  	addAction ,androidx.core.app.NotificationCompat.Builder  build ,androidx.core.app.NotificationCompat.Builder  setContentIntent ,androidx.core.app.NotificationCompat.Builder  setContentText ,androidx.core.app.NotificationCompat.Builder  setContentTitle ,androidx.core.app.NotificationCompat.Builder  setDeleteIntent ,androidx.core.app.NotificationCompat.Builder  setPriority ,androidx.core.app.NotificationCompat.Builder  setSmallIcon ,androidx.core.app.NotificationCompat.Builder  setStyle ,androidx.core.app.NotificationCompat.Builder  
setVisibility ,androidx.core.app.NotificationCompat.Builder  setMediaSession *androidx.core.app.NotificationCompat.Style  setShowActionsInCompactView *androidx.core.app.NotificationCompat.Style  ACTION_NEXT &androidx.fragment.app.FragmentActivity  ACTION_PLAY_PAUSE &androidx.fragment.app.FragmentActivity  ACTION_PREVIOUS &androidx.fragment.app.FragmentActivity  BroadcastReceiver &androidx.fragment.app.FragmentActivity  Build &androidx.fragment.app.FragmentActivity  
CHANNEL_ID &androidx.fragment.app.FragmentActivity  IntentFilter &androidx.fragment.app.FragmentActivity  MainActivity &androidx.fragment.app.FragmentActivity  MediaMetadataCompat &androidx.fragment.app.FragmentActivity  MediaSessionCompat &androidx.fragment.app.FragmentActivity  NOTIFICATION_ID &androidx.fragment.app.FragmentActivity  NotificationChannel &androidx.fragment.app.FragmentActivity  NotificationCompat &androidx.fragment.app.FragmentActivity  NotificationManager &androidx.fragment.app.FragmentActivity  
PendingIntent &androidx.fragment.app.FragmentActivity  PlaybackStateCompat &androidx.fragment.app.FragmentActivity  androidx &androidx.fragment.app.FragmentActivity  createMediaNotification &androidx.fragment.app.FragmentActivity  createNotificationChannel &androidx.fragment.app.FragmentActivity  getSystemService &androidx.fragment.app.FragmentActivity  initializeMediaSession &androidx.fragment.app.FragmentActivity  registerMediaControlReceiver &androidx.fragment.app.FragmentActivity  registerReceiver &androidx.fragment.app.FragmentActivity  unregisterReceiver &androidx.fragment.app.FragmentActivity  updateMediaSession &androidx.fragment.app.FragmentActivity  NotificationCompat androidx.media.app  
MediaStyle %androidx.media.app.NotificationCompat  setMediaSession 0androidx.media.app.NotificationCompat.MediaStyle  setShowActionsInCompactView 0androidx.media.app.NotificationCompat.MediaStyle  ACTION_NEXT com.minimalmusicplayer  ACTION_PLAY_PAUSE com.minimalmusicplayer  ACTION_PREVIOUS com.minimalmusicplayer  Build com.minimalmusicplayer  
CHANNEL_ID com.minimalmusicplayer  IntentFilter com.minimalmusicplayer  MediaMetadataCompat com.minimalmusicplayer  MediaSessionCompat com.minimalmusicplayer  NOTIFICATION_ID com.minimalmusicplayer  NotificationChannel com.minimalmusicplayer  NotificationCompat com.minimalmusicplayer  NotificationManager com.minimalmusicplayer  
PendingIntent com.minimalmusicplayer  PlaybackStateCompat com.minimalmusicplayer  androidx com.minimalmusicplayer  createMediaNotification com.minimalmusicplayer  updateMediaSession com.minimalmusicplayer  BroadcastReceiver #com.minimalmusicplayer.MainActivity  Build #com.minimalmusicplayer.MainActivity  IntentFilter #com.minimalmusicplayer.MainActivity  MainActivity #com.minimalmusicplayer.MainActivity  MediaMetadataCompat #com.minimalmusicplayer.MainActivity  MediaSessionCompat #com.minimalmusicplayer.MainActivity  NotificationChannel #com.minimalmusicplayer.MainActivity  NotificationCompat #com.minimalmusicplayer.MainActivity  NotificationManager #com.minimalmusicplayer.MainActivity  
PendingIntent #com.minimalmusicplayer.MainActivity  PlaybackStateCompat #com.minimalmusicplayer.MainActivity  androidx #com.minimalmusicplayer.MainActivity  createMediaNotification #com.minimalmusicplayer.MainActivity  createNotificationChannel #com.minimalmusicplayer.MainActivity  getANDROIDX #com.minimalmusicplayer.MainActivity  getAndroidx #com.minimalmusicplayer.MainActivity  getSystemService #com.minimalmusicplayer.MainActivity  initializeMediaSession #com.minimalmusicplayer.MainActivity  mediaControlReceiver #com.minimalmusicplayer.MainActivity  mediaSession #com.minimalmusicplayer.MainActivity  notificationManager #com.minimalmusicplayer.MainActivity  registerMediaControlReceiver #com.minimalmusicplayer.MainActivity  registerReceiver #com.minimalmusicplayer.MainActivity  unregisterReceiver #com.minimalmusicplayer.MainActivity  updateMediaSession #com.minimalmusicplayer.MainActivity  ActivityResultContracts -com.minimalmusicplayer.MainActivity.Companion  Array -com.minimalmusicplayer.MainActivity.Companion  BroadcastReceiver -com.minimalmusicplayer.MainActivity.Companion  Build -com.minimalmusicplayer.MainActivity.Companion  Bundle -com.minimalmusicplayer.MainActivity.Companion  Context -com.minimalmusicplayer.MainActivity.Companion  	Exception -com.minimalmusicplayer.MainActivity.Companion  FileScanner -com.minimalmusicplayer.MainActivity.Companion  Int -com.minimalmusicplayer.MainActivity.Companion  IntArray -com.minimalmusicplayer.MainActivity.Companion  Intent -com.minimalmusicplayer.MainActivity.Companion  IntentFilter -com.minimalmusicplayer.MainActivity.Companion  MainActivity -com.minimalmusicplayer.MainActivity.Companion  MediaMetadataCompat -com.minimalmusicplayer.MainActivity.Companion  MediaPlayer -com.minimalmusicplayer.MainActivity.Companion  MediaSessionCompat -com.minimalmusicplayer.MainActivity.Companion  NotificationChannel -com.minimalmusicplayer.MainActivity.Companion  NotificationCompat -com.minimalmusicplayer.MainActivity.Companion  NotificationManager -com.minimalmusicplayer.MainActivity.Companion  PackageManager -com.minimalmusicplayer.MainActivity.Companion  
PendingIntent -com.minimalmusicplayer.MainActivity.Companion  PermissionHelper -com.minimalmusicplayer.MainActivity.Companion  PlaybackStateCompat -com.minimalmusicplayer.MainActivity.Companion  PreferenceManager -com.minimalmusicplayer.MainActivity.Companion  R -com.minimalmusicplayer.MainActivity.Companion  Song -com.minimalmusicplayer.MainActivity.Companion  String -com.minimalmusicplayer.MainActivity.Companion  Uri -com.minimalmusicplayer.MainActivity.Companion  android -com.minimalmusicplayer.MainActivity.Companion  androidx -com.minimalmusicplayer.MainActivity.Companion  apply -com.minimalmusicplayer.MainActivity.Companion  createMediaNotification -com.minimalmusicplayer.MainActivity.Companion  currentSong -com.minimalmusicplayer.MainActivity.Companion  currentSongTextView -com.minimalmusicplayer.MainActivity.Companion  fileScanner -com.minimalmusicplayer.MainActivity.Companion  forEachIndexed -com.minimalmusicplayer.MainActivity.Companion  
getANDROID -com.minimalmusicplayer.MainActivity.Companion  getANDROIDX -com.minimalmusicplayer.MainActivity.Companion  getAPPLY -com.minimalmusicplayer.MainActivity.Companion  
getAndroid -com.minimalmusicplayer.MainActivity.Companion  getAndroidx -com.minimalmusicplayer.MainActivity.Companion  getApply -com.minimalmusicplayer.MainActivity.Companion  getFOREachIndexed -com.minimalmusicplayer.MainActivity.Companion  getForEachIndexed -com.minimalmusicplayer.MainActivity.Companion  
getISNotEmpty -com.minimalmusicplayer.MainActivity.Companion  
getIsNotEmpty -com.minimalmusicplayer.MainActivity.Companion  	getLAUNCH -com.minimalmusicplayer.MainActivity.Companion  getLET -com.minimalmusicplayer.MainActivity.Companion  	getLaunch -com.minimalmusicplayer.MainActivity.Companion  getLet -com.minimalmusicplayer.MainActivity.Companion  getMUTABLEListOf -com.minimalmusicplayer.MainActivity.Companion  getMutableListOf -com.minimalmusicplayer.MainActivity.Companion  invoke -com.minimalmusicplayer.MainActivity.Companion  isCurrentlyPlaying -com.minimalmusicplayer.MainActivity.Companion  
isNotEmpty -com.minimalmusicplayer.MainActivity.Companion  java -com.minimalmusicplayer.MainActivity.Companion  launch -com.minimalmusicplayer.MainActivity.Companion  let -com.minimalmusicplayer.MainActivity.Companion  lifecycleScope -com.minimalmusicplayer.MainActivity.Companion  
mutableListOf -com.minimalmusicplayer.MainActivity.Companion  nextSong -com.minimalmusicplayer.MainActivity.Companion  playPauseButton -com.minimalmusicplayer.MainActivity.Companion  playSong -com.minimalmusicplayer.MainActivity.Companion  previousSong -com.minimalmusicplayer.MainActivity.Companion  run -com.minimalmusicplayer.MainActivity.Companion  scanForMusic -com.minimalmusicplayer.MainActivity.Companion  selectMusicFolder -com.minimalmusicplayer.MainActivity.Companion  songListLayout -com.minimalmusicplayer.MainActivity.Companion  songs -com.minimalmusicplayer.MainActivity.Companion  statusTextView -com.minimalmusicplayer.MainActivity.Companion  togglePlayback -com.minimalmusicplayer.MainActivity.Companion  updateMediaSession -com.minimalmusicplayer.MainActivity.Companion  updateSongList -com.minimalmusicplayer.MainActivity.Companion  getCURRENTSong Ycom.minimalmusicplayer.MainActivity.initializeMediaSession.<anonymous>.<no name provided>  getCurrentSong Ycom.minimalmusicplayer.MainActivity.initializeMediaSession.<anonymous>.<no name provided>  getISCurrentlyPlaying Ycom.minimalmusicplayer.MainActivity.initializeMediaSession.<anonymous>.<no name provided>  getIsCurrentlyPlaying Ycom.minimalmusicplayer.MainActivity.initializeMediaSession.<anonymous>.<no name provided>  getNEXTSong Ycom.minimalmusicplayer.MainActivity.initializeMediaSession.<anonymous>.<no name provided>  getNextSong Ycom.minimalmusicplayer.MainActivity.initializeMediaSession.<anonymous>.<no name provided>  getPREVIOUSSong Ycom.minimalmusicplayer.MainActivity.initializeMediaSession.<anonymous>.<no name provided>  getPreviousSong Ycom.minimalmusicplayer.MainActivity.initializeMediaSession.<anonymous>.<no name provided>  getTOGGLEPlayback Ycom.minimalmusicplayer.MainActivity.initializeMediaSession.<anonymous>.<no name provided>  getTogglePlayback Ycom.minimalmusicplayer.MainActivity.initializeMediaSession.<anonymous>.<no name provided>  isCurrentlyPlaying Ycom.minimalmusicplayer.MainActivity.initializeMediaSession.<anonymous>.<no name provided>  getNEXTSong Kcom.minimalmusicplayer.MainActivity.mediaControlReceiver.<no name provided>  getNextSong Kcom.minimalmusicplayer.MainActivity.mediaControlReceiver.<no name provided>  getPREVIOUSSong Kcom.minimalmusicplayer.MainActivity.mediaControlReceiver.<no name provided>  getPreviousSong Kcom.minimalmusicplayer.MainActivity.mediaControlReceiver.<no name provided>  getTOGGLEPlayback Kcom.minimalmusicplayer.MainActivity.mediaControlReceiver.<no name provided>  getTogglePlayback Kcom.minimalmusicplayer.MainActivity.mediaControlReceiver.<no name provided>  
ic_music_note !com.minimalmusicplayer.R.drawable  ic_skip_next !com.minimalmusicplayer.R.drawable  ic_skip_previous !com.minimalmusicplayer.R.drawable  
CHANNEL_ID 	java.lang  IntentFilter 	java.lang  MediaMetadataCompat 	java.lang  NotificationCompat 	java.lang  androidx 	java.lang  createMediaNotification 	java.lang  updateMediaSession 	java.lang  
CHANNEL_ID kotlin  IntentFilter kotlin  MediaMetadataCompat kotlin  NotificationCompat kotlin  androidx kotlin  createMediaNotification kotlin  updateMediaSession kotlin  
CHANNEL_ID kotlin.annotation  IntentFilter kotlin.annotation  MediaMetadataCompat kotlin.annotation  NotificationCompat kotlin.annotation  androidx kotlin.annotation  createMediaNotification kotlin.annotation  updateMediaSession kotlin.annotation  
CHANNEL_ID kotlin.collections  IntentFilter kotlin.collections  MediaMetadataCompat kotlin.collections  NotificationCompat kotlin.collections  androidx kotlin.collections  createMediaNotification kotlin.collections  updateMediaSession kotlin.collections  
CHANNEL_ID kotlin.comparisons  IntentFilter kotlin.comparisons  MediaMetadataCompat kotlin.comparisons  NotificationCompat kotlin.comparisons  androidx kotlin.comparisons  createMediaNotification kotlin.comparisons  updateMediaSession kotlin.comparisons  
CHANNEL_ID 	kotlin.io  IntentFilter 	kotlin.io  MediaMetadataCompat 	kotlin.io  NotificationCompat 	kotlin.io  androidx 	kotlin.io  createMediaNotification 	kotlin.io  updateMediaSession 	kotlin.io  
CHANNEL_ID 
kotlin.jvm  IntentFilter 
kotlin.jvm  MediaMetadataCompat 
kotlin.jvm  NotificationCompat 
kotlin.jvm  androidx 
kotlin.jvm  createMediaNotification 
kotlin.jvm  updateMediaSession 
kotlin.jvm  
CHANNEL_ID 
kotlin.ranges  IntentFilter 
kotlin.ranges  MediaMetadataCompat 
kotlin.ranges  NotificationCompat 
kotlin.ranges  androidx 
kotlin.ranges  createMediaNotification 
kotlin.ranges  updateMediaSession 
kotlin.ranges  
CHANNEL_ID kotlin.sequences  IntentFilter kotlin.sequences  MediaMetadataCompat kotlin.sequences  NotificationCompat kotlin.sequences  androidx kotlin.sequences  createMediaNotification kotlin.sequences  updateMediaSession kotlin.sequences  
CHANNEL_ID kotlin.text  IntentFilter kotlin.text  MediaMetadataCompat kotlin.text  NotificationCompat kotlin.text  androidx kotlin.text  createMediaNotification kotlin.text  updateMediaSession kotlin.text  MusicWidgetProvider android.app.Activity  updateWidget android.app.Activity  getUPDATEWidget android.media.MediaPlayer  getUpdateWidget android.media.MediaPlayer  updateWidget android.media.MediaPlayer  MusicWidgetProvider  android.view.ContextThemeWrapper  updateWidget  android.view.ContextThemeWrapper  MusicWidgetProvider #androidx.activity.ComponentActivity  updateWidget #androidx.activity.ComponentActivity  MusicWidgetProvider (androidx.appcompat.app.AppCompatActivity  updateWidget (androidx.appcompat.app.AppCompatActivity  MusicWidgetProvider #androidx.core.app.ComponentActivity  updateWidget #androidx.core.app.ComponentActivity  MusicWidgetProvider &androidx.fragment.app.FragmentActivity  updateWidget &androidx.fragment.app.FragmentActivity  MusicWidgetProvider com.minimalmusicplayer  updateWidget com.minimalmusicplayer  MusicWidgetProvider #com.minimalmusicplayer.MainActivity  updateWidget #com.minimalmusicplayer.MainActivity  MusicWidgetProvider -com.minimalmusicplayer.MainActivity.Companion  updateWidget -com.minimalmusicplayer.MainActivity.Companion  widget_btn_next com.minimalmusicplayer.R.id  widget_btn_play_pause com.minimalmusicplayer.R.id  widget_btn_previous com.minimalmusicplayer.R.id  widget_song_artist com.minimalmusicplayer.R.id  music_widget com.minimalmusicplayer.R.layout  updateWidget 	java.lang  updateWidget kotlin  updateWidget kotlin.annotation  updateWidget kotlin.collections  updateWidget kotlin.comparisons  updateWidget 	kotlin.io  updateWidget 
kotlin.jvm  updateWidget 
kotlin.ranges  updateWidget kotlin.sequences  updateWidget kotlin.text  Unit android.app.Activity  createActionButtons android.app.Activity  createAppBar android.app.Activity  createBottomPlayer android.app.Activity  createModernButton android.app.Activity  createPlayerButton android.app.Activity  createSongItem android.app.Activity  createSongListContainer android.app.Activity  createStatusCard android.app.Activity  currentSongIndex android.app.Activity  format android.app.Activity  formatDuration android.app.Activity  getColor android.app.Activity  Unit android.content.Context  createActionButtons android.content.Context  createAppBar android.content.Context  createBottomPlayer android.content.Context  createModernButton android.content.Context  createPlayerButton android.content.Context  createSongItem android.content.Context  createSongListContainer android.content.Context  createStatusCard android.content.Context  currentSongIndex android.content.Context  format android.content.Context  formatDuration android.content.Context  getColor android.content.Context  
sendBroadcast android.content.Context  Unit android.content.ContextWrapper  createActionButtons android.content.ContextWrapper  createAppBar android.content.ContextWrapper  createBottomPlayer android.content.ContextWrapper  createModernButton android.content.ContextWrapper  createPlayerButton android.content.ContextWrapper  createSongItem android.content.ContextWrapper  createSongListContainer android.content.ContextWrapper  createStatusCard android.content.ContextWrapper  currentSongIndex android.content.ContextWrapper  format android.content.ContextWrapper  formatDuration android.content.ContextWrapper  getColor android.content.ContextWrapper  Color android.graphics  Typeface android.graphics  TRANSPARENT android.graphics.Color  DEFAULT android.graphics.Typeface  DEFAULT_BOLD android.graphics.Typeface  getUPDATESongList android.media.MediaPlayer  getUpdateSongList android.media.MediaPlayer  updateSongList android.media.MediaPlayer  	TextUtils android.text  
TruncateAt android.text.TextUtils  END !android.text.TextUtils.TruncateAt  Unit  android.view.ContextThemeWrapper  createActionButtons  android.view.ContextThemeWrapper  createAppBar  android.view.ContextThemeWrapper  createBottomPlayer  android.view.ContextThemeWrapper  createModernButton  android.view.ContextThemeWrapper  createPlayerButton  android.view.ContextThemeWrapper  createSongItem  android.view.ContextThemeWrapper  createSongListContainer  android.view.ContextThemeWrapper  createStatusCard  android.view.ContextThemeWrapper  currentSongIndex  android.view.ContextThemeWrapper  format  android.view.ContextThemeWrapper  formatDuration  android.view.ContextThemeWrapper  getColor  android.view.ContextThemeWrapper  CENTER_VERTICAL android.view.Gravity  R android.view.View  android android.view.View  
getANDROID android.view.View  getAPPLY android.view.View  
getAndroid android.view.View  getApply android.view.View  getColor android.view.View  getGETColor android.view.View  getGetColor android.view.View  getLAYOUTParams android.view.View  getLayoutParams android.view.View  layoutParams android.view.View  setLayoutParams android.view.View  LayoutParams android.view.ViewGroup  apply #android.view.ViewGroup.LayoutParams  
setMargins #android.view.ViewGroup.LayoutParams  apply )android.view.ViewGroup.MarginLayoutParams  
setMargins )android.view.ViewGroup.MarginLayoutParams  FrameLayout android.widget  R android.widget.Button  	elevation android.widget.Button  getColor android.widget.Button  getELEVATION android.widget.Button  getElevation android.widget.Button  getGETColor android.widget.Button  getGetColor android.widget.Button  getLAYOUTParams android.widget.Button  getLayoutParams android.widget.Button  getTEXTSize android.widget.Button  getTextSize android.widget.Button  layoutParams android.widget.Button  setBackgroundColor android.widget.Button  setElevation android.widget.Button  setLayoutParams android.widget.Button  
setPadding android.widget.Button  setTextColor android.widget.Button  setTextSize android.widget.Button  textSize android.widget.Button  getAPPLY android.widget.FrameLayout  getApply android.widget.FrameLayout  
setPadding android.widget.FrameLayout  LayoutParams android.widget.LinearLayout  R android.widget.LinearLayout  currentSongIndex android.widget.LinearLayout  	elevation android.widget.LinearLayout  getCURRENTSongIndex android.widget.LinearLayout  getColor android.widget.LinearLayout  getCurrentSongIndex android.widget.LinearLayout  getELEVATION android.widget.LinearLayout  getElevation android.widget.LinearLayout  getGETColor android.widget.LinearLayout  getGetColor android.widget.LinearLayout  getISClickable android.widget.LinearLayout  getISFocusable android.widget.LinearLayout  getIsClickable android.widget.LinearLayout  getIsFocusable android.widget.LinearLayout  getLAYOUTParams android.widget.LinearLayout  getLayoutParams android.widget.LinearLayout  getPLAYSong android.widget.LinearLayout  getPlaySong android.widget.LinearLayout  isClickable android.widget.LinearLayout  isFocusable android.widget.LinearLayout  layoutParams android.widget.LinearLayout  playSong android.widget.LinearLayout  setClickable android.widget.LinearLayout  setElevation android.widget.LinearLayout  setFocusable android.widget.LinearLayout  setLayoutParams android.widget.LinearLayout  MATCH_PARENT (android.widget.LinearLayout.LayoutParams  WRAP_CONTENT (android.widget.LinearLayout.LayoutParams  apply (android.widget.LinearLayout.LayoutParams  getAPPLY (android.widget.LinearLayout.LayoutParams  getApply (android.widget.LinearLayout.LayoutParams  
setMargins (android.widget.LinearLayout.LayoutParams  android android.widget.ScrollView  
getANDROID android.widget.ScrollView  
getAndroid android.widget.ScrollView  getLAYOUTParams android.widget.ScrollView  getLayoutParams android.widget.ScrollView  layoutParams android.widget.ScrollView  setLayoutParams android.widget.ScrollView  R android.widget.TextView  currentSongIndex android.widget.TextView  	ellipsize android.widget.TextView  formatDuration android.widget.TextView  getCURRENTSongIndex android.widget.TextView  getColor android.widget.TextView  getCurrentSongIndex android.widget.TextView  getELLIPSIZE android.widget.TextView  getEllipsize android.widget.TextView  getFORMATDuration android.widget.TextView  getFormatDuration android.widget.TextView  getGETColor android.widget.TextView  getGetColor android.widget.TextView  getISCurrentlyPlaying android.widget.TextView  getIsCurrentlyPlaying android.widget.TextView  getLAYOUTParams android.widget.TextView  getLayoutParams android.widget.TextView  getMAXLines android.widget.TextView  getMaxLines android.widget.TextView  getTYPEFACE android.widget.TextView  getTypeface android.widget.TextView  isCurrentlyPlaying android.widget.TextView  layoutParams android.widget.TextView  maxLines android.widget.TextView  setBackgroundColor android.widget.TextView  setEllipsize android.widget.TextView  setLayoutParams android.widget.TextView  setMaxLines android.widget.TextView  setTypeface android.widget.TextView  typeface android.widget.TextView  Unit #androidx.activity.ComponentActivity  createActionButtons #androidx.activity.ComponentActivity  createAppBar #androidx.activity.ComponentActivity  createBottomPlayer #androidx.activity.ComponentActivity  createModernButton #androidx.activity.ComponentActivity  createPlayerButton #androidx.activity.ComponentActivity  createSongItem #androidx.activity.ComponentActivity  createSongListContainer #androidx.activity.ComponentActivity  createStatusCard #androidx.activity.ComponentActivity  currentSongIndex #androidx.activity.ComponentActivity  format #androidx.activity.ComponentActivity  formatDuration #androidx.activity.ComponentActivity  getColor #androidx.activity.ComponentActivity  Unit (androidx.appcompat.app.AppCompatActivity  createActionButtons (androidx.appcompat.app.AppCompatActivity  createAppBar (androidx.appcompat.app.AppCompatActivity  createBottomPlayer (androidx.appcompat.app.AppCompatActivity  createModernButton (androidx.appcompat.app.AppCompatActivity  createPlayerButton (androidx.appcompat.app.AppCompatActivity  createSongItem (androidx.appcompat.app.AppCompatActivity  createSongListContainer (androidx.appcompat.app.AppCompatActivity  createStatusCard (androidx.appcompat.app.AppCompatActivity  currentSongIndex (androidx.appcompat.app.AppCompatActivity  format (androidx.appcompat.app.AppCompatActivity  formatDuration (androidx.appcompat.app.AppCompatActivity  getColor (androidx.appcompat.app.AppCompatActivity  Unit #androidx.core.app.ComponentActivity  createActionButtons #androidx.core.app.ComponentActivity  createAppBar #androidx.core.app.ComponentActivity  createBottomPlayer #androidx.core.app.ComponentActivity  createModernButton #androidx.core.app.ComponentActivity  createPlayerButton #androidx.core.app.ComponentActivity  createSongItem #androidx.core.app.ComponentActivity  createSongListContainer #androidx.core.app.ComponentActivity  createStatusCard #androidx.core.app.ComponentActivity  currentSongIndex #androidx.core.app.ComponentActivity  format #androidx.core.app.ComponentActivity  formatDuration #androidx.core.app.ComponentActivity  getColor #androidx.core.app.ComponentActivity  Unit &androidx.fragment.app.FragmentActivity  createActionButtons &androidx.fragment.app.FragmentActivity  createAppBar &androidx.fragment.app.FragmentActivity  createBottomPlayer &androidx.fragment.app.FragmentActivity  createModernButton &androidx.fragment.app.FragmentActivity  createPlayerButton &androidx.fragment.app.FragmentActivity  createSongItem &androidx.fragment.app.FragmentActivity  createSongListContainer &androidx.fragment.app.FragmentActivity  createStatusCard &androidx.fragment.app.FragmentActivity  currentSongIndex &androidx.fragment.app.FragmentActivity  format &androidx.fragment.app.FragmentActivity  formatDuration &androidx.fragment.app.FragmentActivity  getColor &androidx.fragment.app.FragmentActivity  Unit com.minimalmusicplayer  currentSongIndex com.minimalmusicplayer  format com.minimalmusicplayer  formatDuration com.minimalmusicplayer  getColor com.minimalmusicplayer  Unit #com.minimalmusicplayer.MainActivity  createActionButtons #com.minimalmusicplayer.MainActivity  createAppBar #com.minimalmusicplayer.MainActivity  createBottomPlayer #com.minimalmusicplayer.MainActivity  createModernButton #com.minimalmusicplayer.MainActivity  createPlayerButton #com.minimalmusicplayer.MainActivity  createSongItem #com.minimalmusicplayer.MainActivity  createSongListContainer #com.minimalmusicplayer.MainActivity  createStatusCard #com.minimalmusicplayer.MainActivity  format #com.minimalmusicplayer.MainActivity  formatDuration #com.minimalmusicplayer.MainActivity  getColor #com.minimalmusicplayer.MainActivity  	getFORMAT #com.minimalmusicplayer.MainActivity  	getFormat #com.minimalmusicplayer.MainActivity  Boolean -com.minimalmusicplayer.MainActivity.Companion  Long -com.minimalmusicplayer.MainActivity.Companion  Unit -com.minimalmusicplayer.MainActivity.Companion  currentSongIndex -com.minimalmusicplayer.MainActivity.Companion  format -com.minimalmusicplayer.MainActivity.Companion  formatDuration -com.minimalmusicplayer.MainActivity.Companion  getColor -com.minimalmusicplayer.MainActivity.Companion  	getFORMAT -com.minimalmusicplayer.MainActivity.Companion  	getFormat -com.minimalmusicplayer.MainActivity.Companion  divider com.minimalmusicplayer.R.color  music_background com.minimalmusicplayer.R.color  music_on_surface com.minimalmusicplayer.R.color  music_on_surface_variant com.minimalmusicplayer.R.color  
music_primary com.minimalmusicplayer.R.color  music_secondary com.minimalmusicplayer.R.color  
music_surface com.minimalmusicplayer.R.color  
player_accent com.minimalmusicplayer.R.color  player_background com.minimalmusicplayer.R.color  player_on_surface com.minimalmusicplayer.R.color  white com.minimalmusicplayer.R.color  currentSongIndex 	java.lang  formatDuration 	java.lang  getColor 	java.lang  currentSongIndex kotlin  formatDuration kotlin  getColor kotlin  currentSongIndex kotlin.annotation  formatDuration kotlin.annotation  getColor kotlin.annotation  currentSongIndex kotlin.collections  formatDuration kotlin.collections  getColor kotlin.collections  currentSongIndex kotlin.comparisons  formatDuration kotlin.comparisons  getColor kotlin.comparisons  currentSongIndex 	kotlin.io  formatDuration 	kotlin.io  getColor 	kotlin.io  currentSongIndex 
kotlin.jvm  formatDuration 
kotlin.jvm  getColor 
kotlin.jvm  currentSongIndex 
kotlin.ranges  formatDuration 
kotlin.ranges  getColor 
kotlin.ranges  currentSongIndex kotlin.sequences  formatDuration kotlin.sequences  getColor kotlin.sequences  currentSongIndex kotlin.text  formatDuration kotlin.text  getColor kotlin.text  createAlbumCard android.app.Activity  createBottomContainer android.app.Activity  createBottomNavigation android.app.Activity  createCurrentPlayingCard android.app.Activity  createEmptyCard android.app.Activity  createMostPlayedSection android.app.Activity  createNavButton android.app.Activity  createRecentlyAddedSection android.app.Activity  createRecentlyPlayedSection android.app.Activity  createSectionTitle android.app.Activity  createSpacer android.app.Activity  firstOrNull android.app.Activity  reversed android.app.Activity  take android.app.Activity  takeLast android.app.Activity  createAlbumCard android.content.Context  createBottomContainer android.content.Context  createBottomNavigation android.content.Context  createCurrentPlayingCard android.content.Context  createEmptyCard android.content.Context  createMostPlayedSection android.content.Context  createNavButton android.content.Context  createRecentlyAddedSection android.content.Context  createRecentlyPlayedSection android.content.Context  createSectionTitle android.content.Context  createSpacer android.content.Context  firstOrNull android.content.Context  reversed android.content.Context  take android.content.Context  takeLast android.content.Context  createAlbumCard android.content.ContextWrapper  createBottomContainer android.content.ContextWrapper  createBottomNavigation android.content.ContextWrapper  createCurrentPlayingCard android.content.ContextWrapper  createEmptyCard android.content.ContextWrapper  createMostPlayedSection android.content.ContextWrapper  createNavButton android.content.ContextWrapper  createRecentlyAddedSection android.content.ContextWrapper  createRecentlyPlayedSection android.content.ContextWrapper  createSectionTitle android.content.ContextWrapper  createSpacer android.content.ContextWrapper  firstOrNull android.content.ContextWrapper  reversed android.content.ContextWrapper  take android.content.ContextWrapper  takeLast android.content.ContextWrapper  
setPackage android.content.Intent  createAlbumCard  android.view.ContextThemeWrapper  createBottomContainer  android.view.ContextThemeWrapper  createBottomNavigation  android.view.ContextThemeWrapper  createCurrentPlayingCard  android.view.ContextThemeWrapper  createEmptyCard  android.view.ContextThemeWrapper  createMostPlayedSection  android.view.ContextThemeWrapper  createNavButton  android.view.ContextThemeWrapper  createRecentlyAddedSection  android.view.ContextThemeWrapper  createRecentlyPlayedSection  android.view.ContextThemeWrapper  createSectionTitle  android.view.ContextThemeWrapper  createSpacer  android.view.ContextThemeWrapper  firstOrNull  android.view.ContextThemeWrapper  reversed  android.view.ContextThemeWrapper  take  android.view.ContextThemeWrapper  takeLast  android.view.ContextThemeWrapper  HorizontalScrollView android.widget  currentSongIndex android.widget.Button  getCURRENTSongIndex android.widget.Button  getCurrentSongIndex android.widget.Button  getISCurrentlyPlaying android.widget.Button  getIsCurrentlyPlaying android.widget.Button  isCurrentlyPlaying android.widget.Button  addView #android.widget.HorizontalScrollView  createAlbumCard #androidx.activity.ComponentActivity  createBottomContainer #androidx.activity.ComponentActivity  createBottomNavigation #androidx.activity.ComponentActivity  createCurrentPlayingCard #androidx.activity.ComponentActivity  createEmptyCard #androidx.activity.ComponentActivity  createMostPlayedSection #androidx.activity.ComponentActivity  createNavButton #androidx.activity.ComponentActivity  createRecentlyAddedSection #androidx.activity.ComponentActivity  createRecentlyPlayedSection #androidx.activity.ComponentActivity  createSectionTitle #androidx.activity.ComponentActivity  createSpacer #androidx.activity.ComponentActivity  firstOrNull #androidx.activity.ComponentActivity  reversed #androidx.activity.ComponentActivity  take #androidx.activity.ComponentActivity  takeLast #androidx.activity.ComponentActivity  createAlbumCard (androidx.appcompat.app.AppCompatActivity  createBottomContainer (androidx.appcompat.app.AppCompatActivity  createBottomNavigation (androidx.appcompat.app.AppCompatActivity  createCurrentPlayingCard (androidx.appcompat.app.AppCompatActivity  createEmptyCard (androidx.appcompat.app.AppCompatActivity  createMostPlayedSection (androidx.appcompat.app.AppCompatActivity  createNavButton (androidx.appcompat.app.AppCompatActivity  createRecentlyAddedSection (androidx.appcompat.app.AppCompatActivity  createRecentlyPlayedSection (androidx.appcompat.app.AppCompatActivity  createSectionTitle (androidx.appcompat.app.AppCompatActivity  createSpacer (androidx.appcompat.app.AppCompatActivity  firstOrNull (androidx.appcompat.app.AppCompatActivity  reversed (androidx.appcompat.app.AppCompatActivity  take (androidx.appcompat.app.AppCompatActivity  takeLast (androidx.appcompat.app.AppCompatActivity  createAlbumCard #androidx.core.app.ComponentActivity  createBottomContainer #androidx.core.app.ComponentActivity  createBottomNavigation #androidx.core.app.ComponentActivity  createCurrentPlayingCard #androidx.core.app.ComponentActivity  createEmptyCard #androidx.core.app.ComponentActivity  createMostPlayedSection #androidx.core.app.ComponentActivity  createNavButton #androidx.core.app.ComponentActivity  createRecentlyAddedSection #androidx.core.app.ComponentActivity  createRecentlyPlayedSection #androidx.core.app.ComponentActivity  createSectionTitle #androidx.core.app.ComponentActivity  createSpacer #androidx.core.app.ComponentActivity  firstOrNull #androidx.core.app.ComponentActivity  reversed #androidx.core.app.ComponentActivity  take #androidx.core.app.ComponentActivity  takeLast #androidx.core.app.ComponentActivity  createAlbumCard &androidx.fragment.app.FragmentActivity  createBottomContainer &androidx.fragment.app.FragmentActivity  createBottomNavigation &androidx.fragment.app.FragmentActivity  createCurrentPlayingCard &androidx.fragment.app.FragmentActivity  createEmptyCard &androidx.fragment.app.FragmentActivity  createMostPlayedSection &androidx.fragment.app.FragmentActivity  createNavButton &androidx.fragment.app.FragmentActivity  createRecentlyAddedSection &androidx.fragment.app.FragmentActivity  createRecentlyPlayedSection &androidx.fragment.app.FragmentActivity  createSectionTitle &androidx.fragment.app.FragmentActivity  createSpacer &androidx.fragment.app.FragmentActivity  firstOrNull &androidx.fragment.app.FragmentActivity  reversed &androidx.fragment.app.FragmentActivity  take &androidx.fragment.app.FragmentActivity  takeLast &androidx.fragment.app.FragmentActivity  firstOrNull com.minimalmusicplayer  reversed com.minimalmusicplayer  take com.minimalmusicplayer  takeLast com.minimalmusicplayer  createAlbumCard #com.minimalmusicplayer.MainActivity  createBottomContainer #com.minimalmusicplayer.MainActivity  createBottomNavigation #com.minimalmusicplayer.MainActivity  createCurrentPlayingCard #com.minimalmusicplayer.MainActivity  createEmptyCard #com.minimalmusicplayer.MainActivity  createMostPlayedSection #com.minimalmusicplayer.MainActivity  createNavButton #com.minimalmusicplayer.MainActivity  createRecentlyAddedSection #com.minimalmusicplayer.MainActivity  createRecentlyPlayedSection #com.minimalmusicplayer.MainActivity  createSectionTitle #com.minimalmusicplayer.MainActivity  createSpacer #com.minimalmusicplayer.MainActivity  firstOrNull #com.minimalmusicplayer.MainActivity  getFIRSTOrNull #com.minimalmusicplayer.MainActivity  getFirstOrNull #com.minimalmusicplayer.MainActivity  getREVERSED #com.minimalmusicplayer.MainActivity  getReversed #com.minimalmusicplayer.MainActivity  getTAKE #com.minimalmusicplayer.MainActivity  getTAKELast #com.minimalmusicplayer.MainActivity  getTake #com.minimalmusicplayer.MainActivity  getTakeLast #com.minimalmusicplayer.MainActivity  reversed #com.minimalmusicplayer.MainActivity  take #com.minimalmusicplayer.MainActivity  takeLast #com.minimalmusicplayer.MainActivity  firstOrNull -com.minimalmusicplayer.MainActivity.Companion  getFIRSTOrNull -com.minimalmusicplayer.MainActivity.Companion  getFirstOrNull -com.minimalmusicplayer.MainActivity.Companion  getREVERSED -com.minimalmusicplayer.MainActivity.Companion  getReversed -com.minimalmusicplayer.MainActivity.Companion  getTAKE -com.minimalmusicplayer.MainActivity.Companion  getTAKELast -com.minimalmusicplayer.MainActivity.Companion  getTake -com.minimalmusicplayer.MainActivity.Companion  getTakeLast -com.minimalmusicplayer.MainActivity.Companion  reversed -com.minimalmusicplayer.MainActivity.Companion  take -com.minimalmusicplayer.MainActivity.Companion  takeLast -com.minimalmusicplayer.MainActivity.Companion  music_surface_variant com.minimalmusicplayer.R.color  firstOrNull 	java.lang  reversed 	java.lang  take 	java.lang  takeLast 	java.lang  firstOrNull kotlin  reversed kotlin  take kotlin  takeLast kotlin  firstOrNull kotlin.annotation  reversed kotlin.annotation  take kotlin.annotation  takeLast kotlin.annotation  firstOrNull kotlin.collections  reversed kotlin.collections  take kotlin.collections  takeLast kotlin.collections  getFOREachIndexed kotlin.collections.List  getForEachIndexed kotlin.collections.List  getREVERSED kotlin.collections.List  getReversed kotlin.collections.List  getFIRSTOrNull kotlin.collections.MutableList  getFirstOrNull kotlin.collections.MutableList  getTAKE kotlin.collections.MutableList  getTAKELast kotlin.collections.MutableList  getTake kotlin.collections.MutableList  getTakeLast kotlin.collections.MutableList  firstOrNull kotlin.comparisons  reversed kotlin.comparisons  take kotlin.comparisons  takeLast kotlin.comparisons  firstOrNull 	kotlin.io  reversed 	kotlin.io  take 	kotlin.io  takeLast 	kotlin.io  firstOrNull 
kotlin.jvm  reversed 
kotlin.jvm  take 
kotlin.jvm  takeLast 
kotlin.jvm  firstOrNull 
kotlin.ranges  reversed 
kotlin.ranges  take 
kotlin.ranges  takeLast 
kotlin.ranges  firstOrNull kotlin.sequences  reversed kotlin.sequences  take kotlin.sequences  takeLast kotlin.sequences  firstOrNull kotlin.text  reversed kotlin.text  take kotlin.text  takeLast kotlin.text                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                   