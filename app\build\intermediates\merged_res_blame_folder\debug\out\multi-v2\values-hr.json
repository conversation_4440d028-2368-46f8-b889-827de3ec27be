{"logs": [{"outputFile": "com.minimalmusicplayer.app-mergeDebugResources-2:/values-hr/values-hr.xml", "map": [{"source": "E:\\android_development_cache\\.gradle\\caches\\transforms-3\\b61f7d3f27c328116375b3d38dc2a4b7\\transformed\\appcompat-1.6.1\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,305,412,498,602,721,806,888,979,1072,1167,1261,1361,1454,1549,1644,1735,1826,1912,2016,2128,2229,2334,2448,2550,2719,2816", "endColumns": "104,94,106,85,103,118,84,81,90,92,94,93,99,92,94,94,90,90,85,103,111,100,104,113,101,168,96,84", "endOffsets": "205,300,407,493,597,716,801,883,974,1067,1162,1256,1356,1449,1544,1639,1730,1821,1907,2011,2123,2224,2329,2443,2545,2714,2811,2896"}, "to": {"startLines": "26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,177", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1179,1284,1379,1486,1572,1676,1795,1880,1962,2053,2146,2241,2335,2435,2528,2623,2718,2809,2900,2986,3090,3202,3303,3408,3522,3624,3793,13904", "endColumns": "104,94,106,85,103,118,84,81,90,92,94,93,99,92,94,94,90,90,85,103,111,100,104,113,101,168,96,84", "endOffsets": "1279,1374,1481,1567,1671,1790,1875,1957,2048,2141,2236,2330,2430,2523,2618,2713,2804,2895,2981,3085,3197,3298,3403,3517,3619,3788,3885,13984"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\transforms-3\\1674ca777f183a0ca81338d078e0cd6f\\transformed\\material-1.9.0\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,320,398,476,561,658,751,847,977,1061,1129,1225,1293,1356,1464,1524,1590,1646,1717,1777,1831,1957,2014,2076,2130,2205,2339,2424,2505,2642,2726,2812,2903,2959,3014,3080,3154,3232,3320,3392,3469,3549,3623,3716,3789,3881,3977,4051,4127,4223,4275,4342,4429,4516,4578,4642,4705,4811,4912,5009,5113,5173,5232", "endLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68", "endColumns": "12,77,77,84,96,92,95,129,83,67,95,67,62,107,59,65,55,70,59,53,125,56,61,53,74,133,84,80,136,83,85,90,55,54,65,73,77,87,71,76,79,73,92,72,91,95,73,75,95,51,66,86,86,61,63,62,105,100,96,103,59,58,79", "endOffsets": "315,393,471,556,653,746,842,972,1056,1124,1220,1288,1351,1459,1519,1585,1641,1712,1772,1826,1952,2009,2071,2125,2200,2334,2419,2500,2637,2721,2807,2898,2954,3009,3075,3149,3227,3315,3387,3464,3544,3618,3711,3784,3876,3972,4046,4122,4218,4270,4337,4424,4511,4573,4637,4700,4806,4907,5004,5108,5168,5227,5307"}, "to": {"startLines": "21,53,54,55,56,57,65,66,67,69,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,175", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "959,3890,3968,4046,4131,4228,5047,5143,5273,5428,9490,9586,9654,9717,9825,9885,9951,10007,10078,10138,10192,10318,10375,10437,10491,10566,10700,10785,10866,11003,11087,11173,11264,11320,11375,11441,11515,11593,11681,11753,11830,11910,11984,12077,12150,12242,12338,12412,12488,12584,12636,12703,12790,12877,12939,13003,13066,13172,13273,13370,13474,13534,13676", "endLines": "25,53,54,55,56,57,65,66,67,69,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,175", "endColumns": "12,77,77,84,96,92,95,129,83,67,95,67,62,107,59,65,55,70,59,53,125,56,61,53,74,133,84,80,136,83,85,90,55,54,65,73,77,87,71,76,79,73,92,72,91,95,73,75,95,51,66,86,86,61,63,62,105,100,96,103,59,58,79", "endOffsets": "1174,3963,4041,4126,4223,4316,5138,5268,5352,5491,9581,9649,9712,9820,9880,9946,10002,10073,10133,10187,10313,10370,10432,10486,10561,10695,10780,10861,10998,11082,11168,11259,11315,11370,11436,11510,11588,11676,11748,11825,11905,11979,12072,12145,12237,12333,12407,12483,12579,12631,12698,12785,12872,12934,12998,13061,13167,13268,13365,13469,13529,13588,13751"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\transforms-3\\550895908bcd254b169aeba9b6074ec2\\transformed\\preference-1.2.1\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,176,263,346,494,663,750", "endColumns": "70,86,82,147,168,86,82", "endOffsets": "171,258,341,489,658,745,828"}, "to": {"startLines": "68,121,174,176,179,180,181", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "5357,9403,13593,13756,14090,14259,14346", "endColumns": "70,86,82,147,168,86,82", "endOffsets": "5423,9485,13671,13899,14254,14341,14424"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\transforms-3\\f6a56acf230789289efb8d310bea470b\\transformed\\exoplayer-core-2.19.1\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,130,191,256,329,408,481,566,648", "endColumns": "74,60,64,72,78,72,84,81,72", "endOffsets": "125,186,251,324,403,476,561,643,716"}, "to": {"startLines": "94,95,96,97,98,99,100,101,102", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "7543,7618,7679,7744,7817,7896,7969,8054,8136", "endColumns": "74,60,64,72,78,72,84,81,72", "endOffsets": "7613,7674,7739,7812,7891,7964,8049,8131,8204"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\transforms-3\\44bdd8c9f94ff480fecdbcca682a189c\\transformed\\exoplayer-ui-2.19.1\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,11,16,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,295,585,864,945,1027,1107,1214,1321,1391,1458,1549,1641,1706,1777,1840,1912,2031,2155,2276,2344,2428,2499,2570,2674,2779,2846,2911,2964,3022,3070,3131,3205,3284,3360,3434,3498,3557,3628,3693,3764,3816,3879,3964,4049", "endLines": "10,15,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62", "endColumns": "17,12,12,80,81,79,106,106,69,66,90,91,64,70,62,71,118,123,120,67,83,70,70,103,104,66,64,52,57,47,60,73,78,75,73,63,58,70,64,70,51,62,84,84,55", "endOffsets": "290,580,859,940,1022,1102,1209,1316,1386,1453,1544,1636,1701,1772,1835,1907,2026,2150,2271,2339,2423,2494,2565,2669,2774,2841,2906,2959,3017,3065,3126,3200,3279,3355,3429,3493,3552,3623,3688,3759,3811,3874,3959,4044,4100"}, "to": {"startLines": "2,11,16,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,390,680,5496,5577,5659,5739,5846,5953,6023,6090,6181,6273,6338,6409,6472,6544,6663,6787,6908,6976,7060,7131,7202,7306,7411,7478,8209,8262,8320,8368,8429,8503,8582,8658,8732,8796,8855,8926,8991,9062,9114,9177,9262,9347", "endLines": "10,15,20,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120", "endColumns": "17,12,12,80,81,79,106,106,69,66,90,91,64,70,62,71,118,123,120,67,83,70,70,103,104,66,64,52,57,47,60,73,78,75,73,63,58,70,64,70,51,62,84,84,55", "endOffsets": "385,675,954,5572,5654,5734,5841,5948,6018,6085,6176,6268,6333,6404,6467,6539,6658,6782,6903,6971,7055,7126,7197,7301,7406,7473,7538,8257,8315,8363,8424,8498,8577,8653,8727,8791,8850,8921,8986,9057,9109,9172,9257,9342,9398"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\transforms-3\\d001c43b87f49a29f46bbdd20bd4ce3a\\transformed\\core-1.10.1\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,260,357,456,560,664,781", "endColumns": "97,106,96,98,103,103,116,100", "endOffsets": "148,255,352,451,555,659,776,877"}, "to": {"startLines": "58,59,60,61,62,63,64,178", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4321,4419,4526,4623,4722,4826,4930,13989", "endColumns": "97,106,96,98,103,103,116,100", "endOffsets": "4414,4521,4618,4717,4821,4925,5042,14085"}}]}]}