package com.minimalmusicplayer.util

import android.content.Context
import android.content.SharedPreferences
import android.net.Uri

class PreferenceManager(context: Context) {
    
    private val prefs: SharedPreferences = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
    
    companion object {
        private const val PREFS_NAME = "minimal_music_player_prefs"
        private const val KEY_SELECTED_DIRECTORIES = "selected_directories"
        private const val KEY_LAST_PLAYED_SONG = "last_played_song"
        private const val KEY_LAST_POSITION = "last_position"
        private const val KEY_SHUFFLE_MODE = "shuffle_mode"
        private const val KEY_REPEAT_MODE = "repeat_mode"
    }
    
    fun saveSelectedDirectories(directories: Set<String>) {
        prefs.edit()
            .putStringSet(KEY_SELECTED_DIRECTORIES, directories)
            .apply()
    }
    
    fun getSelectedDirectories(): Set<String> {
        return prefs.getStringSet(KEY_SELECTED_DIRECTORIES, emptySet()) ?: emptySet()
    }
    
    fun saveLastPlayedSong(songPath: String) {
        prefs.edit()
            .putString(KEY_LAST_PLAYED_SONG, songPath)
            .apply()
    }
    
    fun getLastPlayedSong(): String? {
        return prefs.getString(KEY_LAST_PLAYED_SONG, null)
    }
    
    fun saveLastPosition(position: Long) {
        prefs.edit()
            .putLong(KEY_LAST_POSITION, position)
            .apply()
    }
    
    fun getLastPosition(): Long {
        return prefs.getLong(KEY_LAST_POSITION, 0)
    }
    
    fun setShuffleMode(enabled: Boolean) {
        prefs.edit()
            .putBoolean(KEY_SHUFFLE_MODE, enabled)
            .apply()
    }
    
    fun isShuffleModeEnabled(): Boolean {
        return prefs.getBoolean(KEY_SHUFFLE_MODE, false)
    }
    
    fun setRepeatMode(mode: Int) {
        prefs.edit()
            .putInt(KEY_REPEAT_MODE, mode)
            .apply()
    }
    
    fun getRepeatMode(): Int {
        return prefs.getInt(KEY_REPEAT_MODE, 0) // 0 = off, 1 = all, 2 = one
    }
}
