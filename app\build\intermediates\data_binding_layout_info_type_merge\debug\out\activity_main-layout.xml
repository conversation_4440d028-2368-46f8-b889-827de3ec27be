<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_main" modulePackage="com.minimalmusicplayer" filePath="app\src\main\res\layout\activity_main.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/activity_main_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="143" endOffset="14"/></Target><Target id="@+id/toolbar" view="androidx.appcompat.widget.Toolbar"><Expressions/><location startLine="9" startOffset="4" endLine="15" endOffset="62"/></Target><Target id="@+id/recyclerViewSongs" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="30" startOffset="12" endLine="36" endOffset="52"/></Target><Target id="@+id/textViewEmptyState" view="TextView"><Expressions/><location startLine="39" startOffset="12" endLine="48" endOffset="43"/></Target><Target id="@+id/progressBar" view="ProgressBar"><Expressions/><location startLine="51" startOffset="12" endLine="56" endOffset="43"/></Target><Target id="@+id/playerContainer" view="LinearLayout"><Expressions/><location startLine="61" startOffset="8" endLine="139" endOffset="22"/></Target><Target id="@+id/textViewSongTitle" view="TextView"><Expressions/><location startLine="79" startOffset="16" endLine="88" endOffset="45"/></Target><Target id="@+id/textViewSongArtist" view="TextView"><Expressions/><location startLine="90" startOffset="16" endLine="98" endOffset="46"/></Target><Target id="@+id/buttonPrevious" view="ImageButton"><Expressions/><location startLine="109" startOffset="16" endLine="116" endOffset="57"/></Target><Target id="@+id/buttonPlayPause" view="ImageButton"><Expressions/><location startLine="118" startOffset="16" endLine="126" endOffset="57"/></Target><Target id="@+id/buttonNext" view="ImageButton"><Expressions/><location startLine="128" startOffset="16" endLine="135" endOffset="57"/></Target></Targets></Layout>