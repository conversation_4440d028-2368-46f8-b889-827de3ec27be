{"logs": [{"outputFile": "com.minimalmusicplayer.app-mergeDebugResources-2:/values-fr-rCA/values-fr-rCA.xml", "map": [{"source": "E:\\android_development_cache\\.gradle\\caches\\transforms-3\\b61f7d3f27c328116375b3d38dc2a4b7\\transformed\\appcompat-1.6.1\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,323,433,520,626,756,841,921,1012,1105,1203,1298,1398,1491,1584,1679,1770,1861,1947,2057,2168,2271,2382,2490,2597,2756,2855", "endColumns": "110,106,109,86,105,129,84,79,90,92,97,94,99,92,92,94,90,90,85,109,110,102,110,107,106,158,98,86", "endOffsets": "211,318,428,515,621,751,836,916,1007,1100,1198,1293,1393,1486,1579,1674,1765,1856,1942,2052,2163,2266,2377,2485,2592,2751,2850,2937"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,174", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "964,1075,1182,1292,1379,1485,1615,1700,1780,1871,1964,2062,2157,2257,2350,2443,2538,2629,2720,2806,2916,3027,3130,3241,3349,3456,3615,13897", "endColumns": "110,106,109,86,105,129,84,79,90,92,97,94,99,92,92,94,90,90,85,109,110,102,110,107,106,158,98,86", "endOffsets": "1070,1177,1287,1374,1480,1610,1695,1775,1866,1959,2057,2152,2252,2345,2438,2533,2624,2715,2801,2911,3022,3125,3236,3344,3451,3610,3709,13979"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\transforms-3\\1674ca777f183a0ca81338d078e0cd6f\\transformed\\material-1.9.0\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,283,390,498,580,681,778,878,1000,1085,1151,1248,1328,1390,1482,1549,1623,1684,1763,1827,1881,1997,2056,2118,2172,2254,2383,2475,2559,2703,2782,2863,2956,3011,3062,3128,3207,3288,3379,3451,3529,3604,3676,3773,3850,3948,4046,4124,4205,4305,4362,4428,4511,4598,4660,4724,4787,4889,4996,5093,5202,5261,5316", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "endColumns": "12,106,107,81,100,96,99,121,84,65,96,79,61,91,66,73,60,78,63,53,115,58,61,53,81,128,91,83,143,78,80,92,54,50,65,78,80,90,71,77,74,71,96,76,97,97,77,80,99,56,65,82,86,61,63,62,101,106,96,108,58,54,88", "endOffsets": "278,385,493,575,676,773,873,995,1080,1146,1243,1323,1385,1477,1544,1618,1679,1758,1822,1876,1992,2051,2113,2167,2249,2378,2470,2554,2698,2777,2858,2951,3006,3057,3123,3202,3283,3374,3446,3524,3599,3671,3768,3845,3943,4041,4119,4200,4300,4357,4423,4506,4593,4655,4719,4782,4884,4991,5088,5197,5256,5311,5400"}, "to": {"startLines": "19,50,51,52,53,54,62,63,64,66,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,172", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "781,3714,3821,3929,4011,4112,4932,5032,5154,5309,9423,9520,9600,9662,9754,9821,9895,9956,10035,10099,10153,10269,10328,10390,10444,10526,10655,10747,10831,10975,11054,11135,11228,11283,11334,11400,11479,11560,11651,11723,11801,11876,11948,12045,12122,12220,12318,12396,12477,12577,12634,12700,12783,12870,12932,12996,13059,13161,13268,13365,13474,13533,13665", "endLines": "22,50,51,52,53,54,62,63,64,66,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,172", "endColumns": "12,106,107,81,100,96,99,121,84,65,96,79,61,91,66,73,60,78,63,53,115,58,61,53,81,128,91,83,143,78,80,92,54,50,65,78,80,90,71,77,74,71,96,76,97,97,77,80,99,56,65,82,86,61,63,62,101,106,96,108,58,54,88", "endOffsets": "959,3816,3924,4006,4107,4204,5027,5149,5234,5370,9515,9595,9657,9749,9816,9890,9951,10030,10094,10148,10264,10323,10385,10439,10521,10650,10742,10826,10970,11049,11130,11223,11278,11329,11395,11474,11555,11646,11718,11796,11871,11943,12040,12117,12215,12313,12391,12472,12572,12629,12695,12778,12865,12927,12991,13054,13156,13263,13360,13469,13528,13583,13749"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\transforms-3\\d001c43b87f49a29f46bbdd20bd4ce3a\\transformed\\core-1.10.1\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,354,456,560,664,778", "endColumns": "97,101,98,101,103,103,113,100", "endOffsets": "148,250,349,451,555,659,773,874"}, "to": {"startLines": "55,56,57,58,59,60,61,175", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4209,4307,4409,4508,4610,4714,4818,13984", "endColumns": "97,101,98,101,103,103,113,100", "endOffsets": "4302,4404,4503,4605,4709,4813,4927,14080"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\transforms-3\\44bdd8c9f94ff480fecdbcca682a189c\\transformed\\exoplayer-ui-2.19.1\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,285,499,686,775,866,945,1043,1140,1219,1285,1382,1479,1544,1607,1671,1743,1864,1990,2115,2190,2278,2351,2431,2530,2631,2697,2761,2814,2872,2920,2981,3048,3125,3192,3264,3322,3381,3447,3512,3578,3630,3695,3774,3853", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,88,90,78,97,96,78,65,96,96,64,62,63,71,120,125,124,74,87,72,79,98,100,65,63,52,57,47,60,66,76,66,71,57,58,65,64,65,51,64,78,78,53", "endOffsets": "280,494,681,770,861,940,1038,1135,1214,1280,1377,1474,1539,1602,1666,1738,1859,1985,2110,2185,2273,2346,2426,2525,2626,2692,2756,2809,2867,2915,2976,3043,3120,3187,3259,3317,3376,3442,3507,3573,3625,3690,3769,3848,3902"}, "to": {"startLines": "2,11,15,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,380,594,5375,5464,5555,5634,5732,5829,5908,5974,6071,6168,6233,6296,6360,6432,6553,6679,6804,6879,6967,7040,7120,7219,7320,7386,8180,8233,8291,8339,8400,8467,8544,8611,8683,8741,8800,8866,8931,8997,9049,9114,9193,9272", "endLines": "10,14,18,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117", "endColumns": "17,12,12,88,90,78,97,96,78,65,96,96,64,62,63,71,120,125,124,74,87,72,79,98,100,65,63,52,57,47,60,66,76,66,71,57,58,65,64,65,51,64,78,78,53", "endOffsets": "375,589,776,5459,5550,5629,5727,5824,5903,5969,6066,6163,6228,6291,6355,6427,6548,6674,6799,6874,6962,7035,7115,7214,7315,7381,7445,8228,8286,8334,8395,8462,8539,8606,8678,8736,8795,8861,8926,8992,9044,9109,9188,9267,9321"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\transforms-3\\550895908bcd254b169aeba9b6074ec2\\transformed\\preference-1.2.1\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,175,272,349,492,661,747", "endColumns": "69,96,76,142,168,85,79", "endOffsets": "170,267,344,487,656,742,822"}, "to": {"startLines": "65,118,171,173,176,177,178", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "5239,9326,13588,13754,14085,14254,14340", "endColumns": "69,96,76,142,168,85,79", "endOffsets": "5304,9418,13660,13892,14249,14335,14415"}}, {"source": "E:\\android_development_cache\\.gradle\\caches\\transforms-3\\f6a56acf230789289efb8d310bea470b\\transformed\\exoplayer-core-2.19.1\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,129,194,272,344,427,503,600,693", "endColumns": "73,64,77,71,82,75,96,92,91", "endOffsets": "124,189,267,339,422,498,595,688,780"}, "to": {"startLines": "91,92,93,94,95,96,97,98,99", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "7450,7524,7589,7667,7739,7822,7898,7995,8088", "endColumns": "73,64,77,71,82,75,96,92,91", "endOffsets": "7519,7584,7662,7734,7817,7893,7990,8083,8175"}}]}]}