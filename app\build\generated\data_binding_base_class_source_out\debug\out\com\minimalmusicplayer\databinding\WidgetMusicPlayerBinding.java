// Generated by view binder compiler. Do not edit!
package com.minimalmusicplayer.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageButton;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.minimalmusicplayer.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class WidgetMusicPlayerBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final TextView widgetArtist;

  @NonNull
  public final LinearLayout widgetContainer;

  @NonNull
  public final ImageButton widgetNext;

  @NonNull
  public final ImageButton widgetPlayPause;

  @NonNull
  public final ImageButton widgetPrevious;

  @NonNull
  public final TextView widgetSongTitle;

  private WidgetMusicPlayerBinding(@NonNull LinearLayout rootView, @NonNull TextView widgetArtist,
      @NonNull LinearLayout widgetContainer, @NonNull ImageButton widgetNext,
      @NonNull ImageButton widgetPlayPause, @NonNull ImageButton widgetPrevious,
      @NonNull TextView widgetSongTitle) {
    this.rootView = rootView;
    this.widgetArtist = widgetArtist;
    this.widgetContainer = widgetContainer;
    this.widgetNext = widgetNext;
    this.widgetPlayPause = widgetPlayPause;
    this.widgetPrevious = widgetPrevious;
    this.widgetSongTitle = widgetSongTitle;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static WidgetMusicPlayerBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static WidgetMusicPlayerBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.widget_music_player, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static WidgetMusicPlayerBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.widget_artist;
      TextView widgetArtist = ViewBindings.findChildViewById(rootView, id);
      if (widgetArtist == null) {
        break missingId;
      }

      LinearLayout widgetContainer = (LinearLayout) rootView;

      id = R.id.widget_next;
      ImageButton widgetNext = ViewBindings.findChildViewById(rootView, id);
      if (widgetNext == null) {
        break missingId;
      }

      id = R.id.widget_play_pause;
      ImageButton widgetPlayPause = ViewBindings.findChildViewById(rootView, id);
      if (widgetPlayPause == null) {
        break missingId;
      }

      id = R.id.widget_previous;
      ImageButton widgetPrevious = ViewBindings.findChildViewById(rootView, id);
      if (widgetPrevious == null) {
        break missingId;
      }

      id = R.id.widget_song_title;
      TextView widgetSongTitle = ViewBindings.findChildViewById(rootView, id);
      if (widgetSongTitle == null) {
        break missingId;
      }

      return new WidgetMusicPlayerBinding((LinearLayout) rootView, widgetArtist, widgetContainer,
          widgetNext, widgetPlayPause, widgetPrevious, widgetSongTitle);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
