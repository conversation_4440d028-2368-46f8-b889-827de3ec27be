<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@drawable/widget_background"
    android:padding="8dp">

    <!-- Song Info -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:orientation="vertical"
        android:gravity="center_vertical">

        <TextView
            android:id="@+id/widget_song_title"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="No song playing"
            android:textSize="14sp"
            android:textStyle="bold"
            android:textColor="@android:color/white"
            android:maxLines="1"
            android:ellipsize="end" />

        <TextView
            android:id="@+id/widget_song_artist"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Unknown Artist"
            android:textSize="12sp"
            android:textColor="@android:color/white"
            android:alpha="0.8"
            android:maxLines="1"
            android:ellipsize="end" />

    </LinearLayout>

    <!-- Control Buttons -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center">

        <ImageButton
            android:id="@+id/widget_btn_previous"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:background="?android:attr/selectableItemBackgroundBorderless"
            android:src="@drawable/ic_skip_previous"
            android:contentDescription="Previous"
            android:tint="@android:color/white" />

        <ImageButton
            android:id="@+id/widget_btn_play_pause"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:layout_marginHorizontal="16dp"
            android:background="?android:attr/selectableItemBackgroundBorderless"
            android:src="@drawable/ic_play"
            android:contentDescription="Play/Pause"
            android:tint="@android:color/white" />

        <ImageButton
            android:id="@+id/widget_btn_next"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:background="?android:attr/selectableItemBackgroundBorderless"
            android:src="@drawable/ic_skip_next"
            android:contentDescription="Next"
            android:tint="@android:color/white" />

    </LinearLayout>

</LinearLayout>
