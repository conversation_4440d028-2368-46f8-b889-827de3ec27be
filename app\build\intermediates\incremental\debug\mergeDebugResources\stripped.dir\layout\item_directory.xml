<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="?attr/selectableItemBackground"
    android:orientation="horizontal"
    android:padding="16dp">

    <!-- Directory icon -->
    <ImageView
        android:layout_width="24dp"
        android:layout_height="24dp"
        android:layout_gravity="center_vertical"
        android:layout_marginEnd="16dp"
        android:src="@drawable/ic_folder"
        android:tint="?attr/colorOnSurface" />

    <!-- Directory info -->
    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:orientation="vertical">

        <TextView
            android:id="@+id/textViewDirectoryName"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:maxLines="1"
            android:textColor="?attr/colorOnSurface"
            android:textSize="16sp"
            android:textStyle="bold"
            tools:text="Music" />

        <TextView
            android:id="@+id/textViewDirectoryPath"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:ellipsize="end"
            android:maxLines="1"
            android:textColor="?attr/colorOnSurfaceVariant"
            android:textSize="12sp"
            tools:text="/storage/emulated/0/Music" />

    </LinearLayout>

    <!-- Remove button -->
    <ImageButton
        android:id="@+id/buttonRemove"
        android:layout_width="48dp"
        android:layout_height="48dp"
        android:layout_gravity="center_vertical"
        android:background="?attr/selectableItemBackgroundBorderless"
        android:contentDescription="Remove Directory"
        android:src="@drawable/ic_delete"
        android:tint="?attr/colorError" />

</LinearLayout>
