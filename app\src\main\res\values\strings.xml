<?xml version="1.0" encoding="utf-8"?>
<resources>
    <string name="app_name">Minimal Music Player</string>
    <string name="widget_description">Music player controls</string>
    <string name="notification_channel_name">Music Playback</string>
    <string name="notification_channel_description">Controls for music playback</string>
    
    <!-- Menu items -->
    <string name="action_select_directories">Select Folders</string>
    <string name="action_refresh">Refresh</string>
    
    <!-- Permissions -->
    <string name="permission_storage_rationale">Storage permission is required to access your music files</string>
    <string name="permission_denied_message">Storage permission is required to access your music files. Please grant permission in app settings.</string>
    
    <!-- Empty states -->
    <string name="empty_no_directories">No music directories selected.\n\nUse the menu to add music folders.</string>
    <string name="empty_no_songs">No music files found in selected directories.</string>
    <string name="empty_no_songs_directories">No music directories selected. Use the menu to add music folders.</string>
    
    <!-- Directory selection -->
    <string name="directory_added">Directory added successfully</string>
    <string name="directory_removed">Directory removed</string>
    <string name="directory_add_failed">Failed to add directory</string>
    <string name="directory_remove_failed">Failed to remove directory</string>
    <string name="scan_completed">Found %d songs</string>
    <string name="scan_failed">Scan failed</string>
    
    <!-- Playback -->
    <string name="unknown_title">Unknown</string>
    <string name="unknown_artist">Unknown Artist</string>
    <string name="unknown_album">Unknown Album</string>
    <string name="no_song_playing">No song playing</string>
</resources>
